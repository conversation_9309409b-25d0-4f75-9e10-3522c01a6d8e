{"timestamp": "2025-03-13T14:05:20.017Z", "summary": {"total": 16, "passed": 8, "failed": 8, "skipped": 0}, "details": [{"name": "用户登录 - 管理员", "result": "passed"}, {"name": "获取管理员个人资料", "result": "failed", "error": "Request failed with status code 500", "details": {"message": "获取用户资料失败", "error": "sequelize is not defined"}}, {"name": "获取所有类别", "result": "passed"}, {"name": "创建测试类别", "result": "failed", "error": "创建类别失败"}, {"name": "获取所有产品", "result": "passed"}, {"name": "创建测试产品", "result": "failed", "error": "Request failed with status code 400", "details": {"message": "产品名称、描述和价格为必填项"}}, {"name": "获取所有用户", "result": "failed", "error": "Request failed with status code 404", "details": {"success": false, "error": "路由未找到: GET /api/users"}}, {"name": "注册测试用户", "result": "passed"}, {"name": "生成代理邀请链接", "result": "passed"}, {"name": "用户登录 - 普通用户", "result": "passed"}, {"name": "获取个人资料", "result": "failed", "error": "Request failed with status code 500", "details": {"message": "获取用户资料失败", "error": "sequelize is not defined"}}, {"name": "浏览产品列表", "result": "failed", "error": "产品列表格式错误"}, {"name": "查询佣金记录", "result": "passed"}, {"name": "查询提现记录", "result": "passed"}, {"name": "获取仪表板数据", "result": "failed", "error": "Request failed with status code 404", "details": {"success": false, "error": "路由未找到: GET /api/dashboard"}}, {"name": "API健康检查", "result": "failed", "error": "API健康检查失败: Request failed with status code 401"}], "config": {"baseURL": "https://ye.bzcy.xyz", "includeAdmin": true}}