/**
 * 调试八字产品支付跳转问题
 * 检查实际的订单数据和跳转逻辑
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { Order, ProductLink, Product, User } from './src/models/index.js';
import sequelize from './src/config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '.env') });

console.log('🔍 八字产品支付跳转问题调试');
console.log('='.repeat(50));

async function debugBaziPaymentRedirect() {
  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 检查环境变量
    console.log('\n1️⃣ 环境变量检查:');
    console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
    console.log(`PAID_RETURN_URL: ${process.env.PAID_RETURN_URL}`);
    console.log(`PAID_RETURN_URL2: ${process.env.PAID_RETURN_URL2}`);
    
    // 2. 检查八字产品
    console.log('\n2️⃣ 八字产品检查:');
    const baziProducts = await Product.findAll({
      where: { 
        status: 'active',
        type: 'service'
      },
      attributes: ['id', 'title', 'type', 'price']
    });
    
    console.log(`找到 ${baziProducts.length} 个服务类产品:`);
    baziProducts.forEach(product => {
      const isBazi = product.title.includes('八字') || product.title.includes('命理');
      console.log(`- ID: ${product.id}, 标题: "${product.title}", 类型: ${product.type}, 是否八字: ${isBazi}`);
    });
    
    // 3. 检查最近的八字订单
    console.log('\n3️⃣ 最近的八字订单检查:');
    const recentOrders = await Order.findAll({
      include: [{
        model: ProductLink,
        as: 'productLink',
        include: [{
          model: Product,
          as: 'product',
          where: { type: 'service' }
        }]
      }],
      order: [['createdAt', 'DESC']],
      limit: 5
    });
    
    console.log(`找到 ${recentOrders.length} 个最近的服务类订单:`);
    recentOrders.forEach(order => {
      const product = order.productLink?.product;
      if (product) {
        const isBazi = product.title.includes('八字') || product.title.includes('命理');
        console.log(`- 订单: ${order.id}, 产品: "${product.title}", 状态: ${order.status}, 是否八字: ${isBazi}`);
      }
    });
    
    // 4. 模拟支付回调逻辑
    console.log('\n4️⃣ 支付回调逻辑模拟:');
    
    if (recentOrders.length > 0) {
      const testOrder = recentOrders[0];
      console.log(`\n测试订单: ${testOrder.id}`);
      
      const productTitle = testOrder.productLink?.product?.title || '';
      const productType = testOrder.productLink?.product?.type || '';
      
      console.log(`产品标题: "${productTitle}"`);
      console.log(`产品类型: "${productType}"`);
      
      // 执行判断逻辑
      const isBaziService = productType === 'service' && 
                           (productTitle.includes('八字') || productTitle.includes('命理'));
      
      const hasDeepseek = productTitle.includes('Deepseek');
      const hasAI = productTitle.includes('AI');
      const hasModel = productTitle.includes('大模型');
      const isDeepseekCourse = hasDeepseek || hasModel || hasAI;
      
      console.log(`\n判断结果:`);
      console.log(`- isBaziService: ${isBaziService}`);
      console.log(`- isDeepseekCourse: ${isDeepseekCourse}`);
      
      let redirectUrl;
      let productTypeDesc;
      
      if (isBaziService) {
        redirectUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=${testOrder.id}`;
        productTypeDesc = '八字查询服务';
      } else if (isDeepseekCourse) {
        redirectUrl = process.env.PAID_RETURN_URL2;
        productTypeDesc = 'Deepseek AI课程';
      } else {
        redirectUrl = process.env.PAID_RETURN_URL;
        productTypeDesc = '儿童纪录片';
      }
      
      console.log(`\n跳转结果:`);
      console.log(`产品类型描述: ${productTypeDesc}`);
      console.log(`跳转地址: ${redirectUrl}`);
      
      // 判断跳转是否正确
      if (isBaziService && redirectUrl.includes('/bazi-report')) {
        console.log(`\n✅ 八字产品跳转正确！`);
      } else if (isBaziService && !redirectUrl.includes('/bazi-report')) {
        console.log(`\n❌ 八字产品跳转错误！应该跳转到报告页面，实际跳转到: ${redirectUrl}`);
      } else {
        console.log(`\n ℹ️  非八字产品，跳转符合预期`);
      }
    }
    
    // 5. 检查可能的问题
    console.log('\n5️⃣ 问题诊断:');
    
    const issues = [];
    
    if (!process.env.FRONTEND_URL) {
      issues.push('❌ FRONTEND_URL 环境变量未设置');
    } else if (process.env.FRONTEND_URL.includes('localhost') && process.env.NODE_ENV === 'production') {
      issues.push('⚠️  生产环境使用了localhost地址');
    }
    
    if (baziProducts.length === 0) {
      issues.push('❌ 没有找到八字产品');
    }
    
    if (recentOrders.length === 0) {
      issues.push('⚠️  没有找到最近的服务类订单');
    }
    
    if (issues.length === 0) {
      console.log('✅ 未发现明显问题，支付跳转逻辑应该正常工作');
    } else {
      console.log('发现以下问题:');
      issues.forEach(issue => console.log(`  ${issue}`));
    }
    
    // 6. 修复建议
    console.log('\n6️⃣ 修复建议:');
    console.log('1. 确保本地开发环境使用正确的环境变量文件');
    console.log('2. 检查实际支付的订单是否为八字产品');
    console.log('3. 查看支付回调的详细日志');
    console.log('4. 验证订单与产品的数据库关联是否正确');
    
  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行调试
debugBaziPaymentRedirect().catch(console.error);
