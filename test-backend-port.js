#!/usr/bin/env node
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function testBackendPort() {
  console.log('🔍 测试后端服务端口连接...');
  console.log('=' .repeat(50));

  // 测试不同的连接方式
  const tests = [
    {
      name: '直接测试服务器3005端口',
      command: 'nc -z -v ************ 3005'
    },
    {
      name: '通过域名测试后端连接',
      command: 'curl -s -I --connect-timeout 5 https://ye.bzcy.xyz:3005/api/auth/login'
    },
    {
      name: '测试Nginx代理到后端的连接',
      command: 'curl -s -I --connect-timeout 5 -H "Host: ye.bzcy.xyz" http://************/api/auth/login'
    },
    {
      name: '测试根路径',
      command: 'curl -s -I --connect-timeout 5 https://ye.bzcy.xyz/'
    },
    {
      name: '测试API根路径',
      command: 'curl -s -I --connect-timeout 5 https://ye.bzcy.xyz/api/'
    }
  ];

  for (const test of tests) {
    console.log(`\n📋 ${test.name}`);
    console.log(`命令: ${test.command}`);
    
    try {
      const { stdout, stderr } = await execAsync(test.command);
      
      if (stdout) {
        const lines = stdout.trim().split('\n');
        if (lines[0].includes('HTTP')) {
          const statusCode = lines[0].split(' ')[1];
          console.log(`✅ 响应: ${statusCode} ${lines[0].split(' ').slice(2).join(' ')}`);
        } else {
          console.log(`✅ 输出: ${lines[0]}`);
        }
      }
      
      if (stderr && !stderr.includes('succeeded')) {
        console.log(`⚠️  错误信息: ${stderr.trim()}`);
      }
      
    } catch (error) {
      console.log(`❌ 失败: ${error.message.split('\n')[0]}`);
    }
  }

  // 额外的诊断信息
  console.log('\n' + '=' .repeat(50));
  console.log('🔧 诊断结果分析:');
  
  console.log('\n如果所有测试都失败，可能的原因:');
  console.log('1. 后端服务未启动或崩溃');
  console.log('2. 后端服务端口配置错误');
  console.log('3. 防火墙阻止了3005端口');
  console.log('4. PM2进程管理器配置问题');
  
  console.log('\n建议的修复步骤:');
  console.log('1. 重新运行部署脚本: ./deploy_new.sh');
  console.log('2. 手动检查服务器状态 (需要SSH访问):');
  console.log('   - pm2 status');
  console.log('   - pm2 logs commission-platform-api');
  console.log('   - netstat -tlnp | grep :3005');
  console.log('   - systemctl status nginx');
}

// 运行测试
testBackendPort();