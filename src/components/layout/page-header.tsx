import type { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import '../../styles/BaziReportEnhanced.css';

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  className?: string;
}

export function PageHeader({ title, description, icon: Icon, className }: PageHeaderProps) {
  return (
    <header className={cn("mb-6 md:mb-8 py-8 sm:py-10 border-b-2 border-amber-200/60 bg-gradient-to-r from-amber-50/80 via-orange-50/60 to-yellow-50/80", className)}>
      <div className="container mx-auto px-3 sm:px-4 md:px-6 flex flex-col sm:flex-row items-center sm:space-x-6 text-center sm:text-left">
        {Icon && (
          <div className="p-3 bg-gradient-to-r from-amber-400 to-orange-400 rounded-xl shadow-lg mb-4 sm:mb-0">
            <Icon className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
          </div>
        )}
        <div className="space-y-2">
          <h1 className="enhanced-title bazi-title text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
            {title}
          </h1>
          {description && (
            <p className="text-sm sm:text-base md:text-lg text-amber-700 font-medium mt-2">
              {description}
            </p>
          )}
        </div>
      </div>
    </header>
  );
}