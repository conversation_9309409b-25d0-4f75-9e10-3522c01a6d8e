import React, { useEffect } from 'react';
import { TopSection2 } from '../components/TopSection2';
import { TopSection2Debug } from '../components/TopSection2Debug';
import { FormSection2 } from '../components/FormSection2';
import { ImageBlock2 } from '../components/ImageBlock2';
import { Marquee } from '../components/Marquee';

// 导入图片
const a0 = '/assets/theme/2/0.png';

const BaziQueryBz: React.FC = () => {
  useEffect(() => {
    // 设置页面背景色
    document.body.style.background = '#864a30';

    return () => {
      document.body.removeAttribute('style');
    };
  }, []);



  return (
    <div className="page home2">
      {/* 跑马灯 */}
      <Marquee />

      {/* Banner图片 */}
      <img className="banner" src={a0} alt="Banner" style={{ width: '100%', display: 'block' }} />

      {/* 顶部罗盘动画 */}
      <TopSection2 />
      
      {/* 表单区域 */}
      <section className="block" style={{ paddingTop: 0 }}>
        <FormSection2 />
      </section>

      {/* 图片展示区域 */}
      <ImageBlock2 />

      <style jsx>{`
        @keyframes marquee {
          0% { transform: translateX(100%); }
          100% { transform: translateX(-100%); }
        }
      `}</style>
    </div>
  );
};

export default BaziQueryBz;
