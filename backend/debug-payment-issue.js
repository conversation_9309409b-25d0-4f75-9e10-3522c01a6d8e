console.log('🔍 调试八字产品支付跳转问题');
console.log('=' .repeat(60));

console.log('\n💡 问题分析:');
console.log('根据你的反馈，修复后仍然跳转到百度网盘，可能的原因：');

console.log('\n🎯 最可能的原因：');
console.log('1. **生产环境代码版本过旧** - 生产服务器上的代码还是旧版本');
console.log('2. **生产环境服务未重启** - 新代码部署后服务没有重启');
console.log('3. **环境变量缓存** - 环境变量被缓存，没有生效');

console.log('\n🔧 解决方案：');
console.log('');
console.log('**步骤1: 检查生产环境代码版本**');
console.log('- 登录生产服务器');
console.log('- 检查 backend/src/routes/payment.js 文件');
console.log('- 确认包含八字产品判断逻辑的代码');
console.log('');

console.log('**步骤2: 部署最新代码到生产环境**');
console.log('```bash');
console.log('# 在项目根目录执行');
console.log('git add .');
console.log('git commit -m "修复八字产品支付跳转逻辑"');
console.log('git push origin main');
console.log('');
console.log('# 然后运行部署脚本');
console.log('./deploy_new.sh');
console.log('```');
console.log('');

console.log('**步骤3: 确认生产环境配置**');
console.log('- 确认生产环境的 .env.production 文件包含：');
console.log('  FRONTEND_URL=https://ye.bzcy.xyz');
console.log('- 确认支付回调URL正确：');
console.log('  ALIPAY_RETURN_URL=https://ye.bzcy.xyz/api/payment/alipay/return');
console.log('');

console.log('**步骤4: 重启生产环境服务**');
console.log('```bash');
console.log('# 在生产服务器上');
console.log('pm2 restart all');
console.log('# 或者');
console.log('systemctl restart your-app-service');
console.log('```');
console.log('');

console.log('**步骤5: 验证修复效果**');
console.log('- 创建一个新的八字产品订单');
console.log('- 完成支付流程');
console.log('- 查看支付成功后的跳转地址');
console.log('- 检查生产环境的日志输出');

console.log('\n🚨 关键检查点：');
console.log('');
console.log('1. **支付回调日志** - 查看生产环境的支付回调日志：');
console.log('   应该看到：【支付宝同步回调】产品类型描述: 八字查询服务');
console.log('   应该看到：【支付宝同步回调】跳转地址: https://ye.bzcy.xyz/bazi-report?orderId=...');
console.log('');

console.log('2. **环境变量检查** - 在生产环境执行：');
console.log('   echo $FRONTEND_URL');
console.log('   应该输出：https://ye.bzcy.xyz');
console.log('');

console.log('3. **代码版本检查** - 确认生产环境的 payment.js 包含以下逻辑：');
console.log('   ```javascript');
console.log('   const isBaziService = productType === "service" && ');
console.log('                        (productTitle.includes("八字") || productTitle.includes("命理"));');
console.log('   if (isBaziService) {');
console.log('     redirectUrl = `${process.env.FRONTEND_URL}/bazi-report?orderId=${orderId}`;');
console.log('   }');
console.log('   ```');

console.log('\n⚡ 快速修复步骤：');
console.log('1. 运行：git add . && git commit -m "fix" && git push');
console.log('2. 运行：./deploy_new.sh');
console.log('3. 登录生产服务器重启服务');
console.log('4. 测试支付流程');

console.log('\n🎉 修复成功的标志：');
console.log('- 八字产品支付成功后跳转到：https://ye.bzcy.xyz/bazi-report?orderId=xxx');
console.log('- 不再跳转到百度网盘');
console.log('- 生产环境日志显示正确的产品类型判断');

console.log('\n' + '=' .repeat(60)); 