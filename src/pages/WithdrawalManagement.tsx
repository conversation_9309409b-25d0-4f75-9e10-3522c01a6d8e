import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import { Card, Space, Button, Divider, Table, message } from 'antd';

interface Withdrawal {
  id: number;
  amount: number;
  status: string;
  remarks: string;
  createdAt: string;
  alipayAccount: string;
  withdrawalAgent?: {
    name: string;
    username: string;
  };
}

interface ConsistencyData {
  isConsistent: boolean;
  orderTotal: number;
  commissionTotal: number;
  difference: number;
  inconsistentCommissions: Array<{
    id: number;
    amount: number;
    status: string;
    orderId: string;
    orderStatus: string;
  }>;
}

// 添加数据一致性检查组件
const ConsistencyCheck: React.FC = () => {
  const [consistencyData, setConsistencyData] = useState<ConsistencyData | null>(null);
  const [loading, setLoading] = useState(false);
  const [fixing, setFixing] = useState(false);
  const { token } = useAuth();

  const checkConsistency = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${config.apiUrl}/api/commissions/check-consistency`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('检查数据一致性失败');
      }
      
      const data = await response.json();
      setConsistencyData(data);
    } catch (error) {
      console.error('检查数据一致性失败:', error);
      message.error('检查数据一致性失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const fixInconsistency = async () => {
    setFixing(true);
    try {
      const response = await fetch(`${config.apiUrl}/api/commissions/fix-consistency`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('修复数据一致性失败');
      }
      
      const data = await response.json();
      message.success(data.message);
      // 重新检查一致性
      await checkConsistency();
    } catch (error) {
      console.error('修复数据一致性失败:', error);
      message.error('修复数据一致性失败，请重试');
    } finally {
      setFixing(false);
    }
  };

  return (
    <Card title="数据一致性检查" className="mb-4">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Space>
          <Button 
            type="primary" 
            onClick={checkConsistency} 
            loading={loading}
          >
            检查数据一致性
          </Button>
          {consistencyData && !consistencyData.isConsistent && (
            <Button 
              danger 
              onClick={fixInconsistency} 
              loading={fixing}
            >
              修复数据不一致
            </Button>
          )}
        </Space>
        
        {consistencyData && (
          <div className="mt-4">
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="p-4 bg-gray-50 rounded">
                <div className="text-sm text-gray-500">订单总佣金</div>
                <div className="text-lg font-semibold">¥{consistencyData.orderTotal.toFixed(2)}</div>
              </div>
              <div className="p-4 bg-gray-50 rounded">
                <div className="text-sm text-gray-500">佣金记录总额</div>
                <div className="text-lg font-semibold">¥{consistencyData.commissionTotal.toFixed(2)}</div>
              </div>
            </div>
            
            <div className="mb-4">
              <div className="text-sm text-gray-500">差额</div>
              <div className={`text-lg font-semibold ${consistencyData.difference === 0 ? 'text-green-500' : 'text-red-500'}`}>
                ¥{consistencyData.difference.toFixed(2)}
              </div>
            </div>
            
            <div className={`text-lg font-semibold mb-4 ${consistencyData.isConsistent ? 'text-green-500' : 'text-red-500'}`}>
              状态: {consistencyData.isConsistent ? '数据一致' : '数据不一致'}
            </div>
            
            {consistencyData.inconsistentCommissions.length > 0 && (
              <>
                <Divider />
                <div className="font-medium text-lg mb-4">不一致的佣金记录</div>
                <Table 
                  dataSource={consistencyData.inconsistentCommissions}
                  rowKey="id"
                  columns={[
                    { 
                      title: 'ID', 
                      dataIndex: 'id',
                      width: 80
                    },
                    { 
                      title: '金额', 
                      dataIndex: 'amount',
                      width: 120,
                      render: (val: number) => `¥${val.toFixed(2)}` 
                    },
                    { 
                      title: '佣金状态', 
                      dataIndex: 'status',
                      width: 120
                    },
                    { 
                      title: '订单ID', 
                      dataIndex: 'orderId',
                      width: 120
                    },
                    { 
                      title: '订单状态', 
                      dataIndex: 'orderStatus',
                      width: 120
                    }
                  ]}
                  size="small"
                  pagination={false}
                  scroll={{ x: true }}
                />
              </>
            )}
          </div>
        )}
      </Space>
    </Card>
  );
};

const WithdrawalManagement: React.FC = () => {
  const { token } = useAuth();
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<Withdrawal | null>(null);
  const [remarks, setRemarks] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  // 获取所有提现记录
  const fetchWithdrawals = async () => {
    try {
      const response = await fetch(`${config.apiUrl}/api/withdrawals`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('获取提现记录失败');
      }

      const data = await response.json();
      setWithdrawals(data.withdrawals);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取提现记录失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (token) {
      fetchWithdrawals();
    }
  }, [token]);

  // 处理审核操作
  const handleApproval = async (status: 'approved' | 'rejected' | 'completed') => {
    if (!selectedWithdrawal) return;

    try {
      const response = await fetch(`${config.apiUrl}/api/withdrawals/${selectedWithdrawal.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          status,
          remarks
        })
      });

      if (!response.ok) {
        throw new Error('审核操作失败');
      }

      // 刷新提现记录
      await fetchWithdrawals();
      setIsModalOpen(false);
      setSelectedWithdrawal(null);
      setRemarks('');
    } catch (err) {
      setError(err instanceof Error ? err.message : '审核操作失败');
    }
  };

  const getStatusBadgeClass = (status: Withdrawal['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: Withdrawal['status']) => {
    const statusMap = {
      completed: '已完成',
      pending: '待审核',
      approved: '已审核',
      rejected: '已拒绝'
    };
    return statusMap[status];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div>
      <h2>提现管理</h2>
      <ConsistencyCheck />
      <div className="py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-semibold text-gray-900">提现管理</h1>

          {error && (
            <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}

          {/* 提现记录表格 */}
          <div className="mt-8">
            <div className="flex flex-col">
              <div className="responsive-table-container" style={{ overflowX: 'auto', width: '100%' }}>
                <div className="py-2 align-middle inline-block min-w-full">
                  <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200 responsive-table">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            申请人
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            提现信息
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            状态
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            申请时间
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            备注
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            操作
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {withdrawals.map((withdrawal) => (
                          <tr key={withdrawal.id}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">
                                {withdrawal.withdrawalAgent?.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {withdrawal.withdrawalAgent?.username}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900">
                                ¥{withdrawal.amount.toFixed(2)}
                              </div>
                              <div className="text-sm text-gray-500">
                                支付宝: {withdrawal.alipayAccount}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(withdrawal.status)}`}>
                                {getStatusText(withdrawal.status)}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {new Date(withdrawal.createdAt).toLocaleString()}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {withdrawal.remarks || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm">
                              {withdrawal.status === 'pending' && (
                                <div className="space-x-2">
                                  <button
                                    onClick={() => {
                                      setSelectedWithdrawal(withdrawal);
                                      setIsModalOpen(true);
                                    }}
                                    className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
                                  >
                                    审核
                                  </button>
                                </div>
                              )}
                              {withdrawal.status === 'approved' && (
                                <button
                                  onClick={() => {
                                    setSelectedWithdrawal(withdrawal);
                                    handleApproval('completed');
                                  }}
                                  className="bg-green-500 text-white px-3 py-1 rounded hover:bg-green-600"
                                >
                                  标记完成
                                </button>
                              )}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 审核弹窗 */}
          {isModalOpen && selectedWithdrawal && (
            <div className="fixed z-10 inset-0 overflow-y-auto">
              <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                  <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>

                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                  <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <h3 className="text-lg font-medium text-gray-900">审核提现申请</h3>
                    <div className="mt-4">
                      <div className="mb-4">
                        <label className="block text-sm font-medium text-gray-700">备注</label>
                        <textarea
                          rows={3}
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          value={remarks}
                          onChange={(e) => setRemarks(e.target.value)}
                          placeholder="请输入审核备注"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button
                      type="button"
                      onClick={() => handleApproval('approved')}
                      className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      通过
                    </button>
                    <button
                      type="button"
                      onClick={() => handleApproval('rejected')}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      拒绝
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        setIsModalOpen(false);
                        setSelectedWithdrawal(null);
                        setRemarks('');
                      }}
                      className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    >
                      取消
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WithdrawalManagement;
