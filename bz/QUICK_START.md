# 🚀 快速开始 - 5分钟集成八字查询组件

## 📋 最小化集成步骤

### 1. 安装核心依赖 (1分钟)

```bash
npm install react react-dom zustand axios clsx date-fns
npm install -D tailwindcss @types/react @types/react-dom typescript
```

### 2. 复制核心文件 (2分钟)

只需复制这4个文件到您的项目：

```
src/components/ui/SimpleModal.tsx      # 基础模态框
src/components/CalendarTypeSelector.tsx # 公历/农历选择
src/components/DateTimeSelector.tsx     # 日期时间选择
src/components/AreaSelector.tsx         # 地区选择
```

### 3. 添加样式 (1分钟)

将以下CSS添加到您的样式文件：

```css
.simple-modal-overlay {
  position: fixed; top: 0; left: 0; right: 0; bottom: 0;
  background-color: rgba(0, 0, 0, 0.5); display: flex;
  align-items: center; justify-content: center; z-index: 1000; padding: 20px;
}
.simple-modal-content {
  background: white; border-radius: 12px; max-width: 90vw; max-height: 90vh;
  overflow-y: auto; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}
.datetime-selectors { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 20px; }
.selector-group select { padding: 12px; border: 1px solid #ebedf0; border-radius: 6px; font-size: 16px; background: white; cursor: pointer; }
```

### 4. 使用组件 (1分钟)

```tsx
import React, { useState } from 'react';
import { CalendarTypeSelector } from './components/CalendarTypeSelector';
import { DateTimeSelector } from './components/DateTimeSelector';
import { AreaSelector } from './components/AreaSelector';

export const MyForm: React.FC = () => {
  const [showCalendar, setShowCalendar] = useState(false);
  const [showDateTime, setShowDateTime] = useState(false);
  const [showArea, setShowArea] = useState(false);
  const [calendarType, setCalendarType] = useState<'solar' | 'lunar'>('solar');

  return (
    <div>
      {/* 日期选择 */}
      <button onClick={() => setShowCalendar(true)}>选择生日</button>
      <CalendarTypeSelector
        isOpen={showCalendar}
        onClose={() => setShowCalendar(false)}
        onSelect={(type) => {
          setCalendarType(type);
          setShowCalendar(false);
          setShowDateTime(true);
        }}
      />
      <DateTimeSelector
        isOpen={showDateTime}
        onClose={() => setShowDateTime(false)}
        onConfirm={(data) => {
          console.log('生日:', data);
          setShowDateTime(false);
        }}
        calendarType={calendarType}
      />

      {/* 地区选择 */}
      <button onClick={() => setShowArea(true)}>选择地区</button>
      <AreaSelector
        isOpen={showArea}
        onClose={() => setShowArea(false)}
        onConfirm={(data) => {
          console.log('地区:', data);
          setShowArea(false);
        }}
      />
    </div>
  );
};
```

## ✅ 完成！

现在您已经成功集成了八字查询的核心组件：
- ✅ 公历/农历选择器
- ✅ 日期时间选择器（年月日时分）
- ✅ 地区选择器（省市区三级联动）

## 🎯 进阶集成

如果需要完整的表单功能，请参考：
- `MIGRATION_GUIDE.md` - 完整移植指南
- `README.md` - 详细项目文档
- `src/components/FormFields.tsx` - 完整表单实现

## 🔧 自定义配置

### 修改地区数据
编辑 `AreaSelector.tsx` 中的 `areaData` 对象

### 修改样式
使用TailwindCSS类名或自定义CSS

### 添加验证
使用React Hook Form + Zod进行表单验证

---

**5分钟快速集成完成！** 🎉
