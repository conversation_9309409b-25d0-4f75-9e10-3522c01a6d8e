import React from 'react';
import { Helmet } from 'react-helmet-async';

interface BaziSEOProps {
  productInfo?: {
    name: string;
    price: number;
    description: string;
  };
  pageType?: 'landing' | 'form' | 'report';
}

const BaziSEO: React.FC<BaziSEOProps> = ({ productInfo, pageType = 'landing' }) => {
  const getPageTitle = () => {
    switch (pageType) {
      case 'form':
        return `填写生辰信息 - ${productInfo?.name || '八字命理查询'} - 专业命理分析`;
      case 'report':
        return `命理报告 - ${productInfo?.name || '八字命理查询'} - 专业命理分析`;
      default:
        return `${productInfo?.name || '八字命理查询'} - 专业命理分析 - 千年传承东方智慧`;
    }
  };

  const getPageDescription = () => {
    switch (pageType) {
      case 'form':
        return '请准确填写您的出生信息，我们将基于传统命理学为您生成专业的八字分析报告，洞悉人生运势。';
      case 'report':
        return '您的专属八字命理报告已生成，包含详细的性格分析、运势预测、事业指导等专业内容。';
      default:
        return `${productInfo?.description || '专业八字命理分析服务，基于千年传承的东方智慧，运用现代AI技术，为您提供精准的命理解读和人生指导。'}`;
    }
  };

  const keywords = [
    '八字',
    '命理',
    '算命',
    '生辰八字',
    '命盘',
    '运势',
    '占卜',
    '易经',
    '风水',
    '命运',
    '预测',
    '五行',
    '天干地支',
    '紫微斗数',
    '命理分析',
    '人生指导',
    '事业运势',
    '财运',
    '婚姻运势',
    '健康运势'
  ];

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": productInfo?.name || "八字命理查询服务",
    "description": getPageDescription(),
    "provider": {
      "@type": "Organization",
      "name": "八字命理大师",
      "url": window.location.origin
    },
    "serviceType": "命理咨询",
    "areaServed": "中国",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "命理服务",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": productInfo?.name || "八字命理分析",
            "description": productInfo?.description || "专业八字命理分析服务"
          },
          "price": productInfo?.price || 0,
          "priceCurrency": "CNY"
        }
      ]
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "10000",
      "bestRating": "5",
      "worstRating": "1"
    },
    "review": [
      {
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "author": {
          "@type": "Person",
          "name": "张女士"
        },
        "reviewBody": "分析很准确，对我的人生规划很有帮助，专业性很强。"
      },
      {
        "@type": "Review",
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "author": {
          "@type": "Person",
          "name": "李先生"
        },
        "reviewBody": "服务很专业，报告详细准确，值得信赖的命理分析平台。"
      }
    ]
  };

  return (
    <Helmet>
      {/* 基础SEO */}
      <title>{getPageTitle()}</title>
      <meta name="description" content={getPageDescription()} />
      <meta name="keywords" content={keywords.join(', ')} />
      
      {/* Open Graph */}
      <meta property="og:title" content={getPageTitle()} />
      <meta property="og:description" content={getPageDescription()} />
      <meta property="og:type" content="website" />
      <meta property="og:url" content={window.location.href} />
      <meta property="og:site_name" content="八字命理大师" />
      <meta property="og:locale" content="zh_CN" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={getPageTitle()} />
      <meta name="twitter:description" content={getPageDescription()} />
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      
      {/* 搜索引擎优化 */}
      <meta name="robots" content="index, follow" />
      <meta name="googlebot" content="index, follow" />
      <meta name="baiduspider" content="index, follow" />
      
      {/* 本地化 */}
      <meta name="language" content="zh-CN" />
      <meta name="geo.region" content="CN" />
      <meta name="geo.country" content="China" />
      
      {/* 性能优化 */}
      <meta httpEquiv="X-UA-Compatible" content="IE=edge" />
      <meta name="renderer" content="webkit" />
      
      {/* 安全性 */}
      <meta httpEquiv="Content-Security-Policy" content="upgrade-insecure-requests" />
      
      {/* 结构化数据 */}
      <script type="application/ld+json">
        {JSON.stringify(structuredData)}
      </script>
      
      {/* 预加载关键资源 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* 页面特定的meta标签 */}
      {pageType === 'form' && (
        <>
          <meta name="step" content="information-input" />
          <meta name="conversion-step" content="1" />
        </>
      )}
      
      {pageType === 'report' && (
        <>
          <meta name="step" content="report-generated" />
          <meta name="conversion-step" content="3" />
        </>
      )}
      
      {productInfo && (
        <>
          <meta name="product:price:amount" content={productInfo.price.toString()} />
          <meta name="product:price:currency" content="CNY" />
          <meta name="product:availability" content="in stock" />
        </>
      )}
    </Helmet>
  );
};

export default BaziSEO;
