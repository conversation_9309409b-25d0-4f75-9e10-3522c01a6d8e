import React from 'react';
import { clsx } from '@/utils';

interface RadioProps {
  name: string;
  value: string;
  checked: boolean;
  onChange: (value: string) => void;
  children: React.ReactNode;
}

export const Radio: React.FC<RadioProps> = ({
  name,
  value,
  checked,
  onChange,
  children,
}) => {
  return (
    <label className="radio-item" style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      cursor: 'pointer',
    }}>
      <div
        className={clsx('radio-circle', checked && 'checked')}
        style={{
          width: '16px',
          height: '16px',
          borderRadius: '50%',
          border: '2px solid #b87c3d',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: checked ? '#b87c3d' : 'white',
        }}
      >
        {checked && (
          <div style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: 'white',
          }} />
        )}
      </div>
      <span style={{
        fontSize: '15px',
        color: '#3f2410',
      }}>{children}</span>
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={() => onChange(value)}
        className="hidden"
      />
    </label>
  );
};

interface RadioGroupProps {
  value: string;
  onChange: (value: string) => void;
  children: React.ReactNode;
  direction?: 'horizontal' | 'vertical';
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  value,
  onChange,
  children,
  direction = 'horizontal',
}) => {
  return (
    <div
      className={clsx('van-radio-group')}
      style={{
        display: 'flex',
        gap: direction === 'horizontal' ? '30px' : '12px',
        flexDirection: direction === 'vertical' ? 'column' : 'row',
      }}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, {
            checked: child.props.value === value,
            onChange,
          });
        }
        return child;
      })}
    </div>
  );
};
