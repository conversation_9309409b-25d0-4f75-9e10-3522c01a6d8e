import React, { useState } from 'react';
import { Card, Button, Steps, message, Typography, Space, Tag } from 'antd';
import { UserOutlined, FormOutlined, CreditCardOutlined, FileTextOutlined } from '@ant-design/icons';
import BaziForm from '../components/BaziForm';
import BaziReportEnhanced from '../components/BaziReportEnhanced';
import { BaZiQueryFormValues, BaZiReportData } from '../types/bazi';

const { Title, Paragraph } = Typography;

// 模拟的八字报告数据
const mockReportData: BaZiReportData = {
  命主信息: {
    姓名: "张三",
    性别: "男",
    生肖: "龙",
    星座: "天秤座",
    公历生日: "1988年10月15日 14时30分",
    农历生日: "戊辰年九月初五日未时",
    出生地: "北京市朝阳区",
    幸运秘诀: {
      幸运数字: "3, 8, 13",
      大凶数字: "2, 7, 12",
      吉祥颜色: "金色, 白色",
      忌讳颜色: "红色, 紫色",
      幸运花: "白玫瑰",
      幸运物品: "金属饰品"
    }
  },
  命主八字称骨信息: {
    总骨重: "4两8钱",
    年骨重: "1两2钱",
    月骨重: "1两8钱",
    日骨重: "1两6钱",
    时骨重: "2钱",
    称骨歌: "初年运道未曾亨，若是蹉跎再不兴。\n兄弟六亲皆无靠，一身事业晚年成。\n此命为人性躁，能随机应变，常近贵人。\n祖业无成，骨肉六亲少义，一个自立家计。"
  },
  命主姓名数理信息: {
    天格: { 数字: "12", 吉凶: "凶", 含义: "薄弱无力，孤立无援，外祥内苦，谋事难成" },
    人格: { 数字: "14", 吉凶: "凶", 含义: "沦落天涯，失意烦闷，家庭缘薄，多破兆" },
    地格: { 数字: "4", 吉凶: "凶", 含义: "万事休止，不足不满，难得平安" },
    总格: { 数字: "15", 吉凶: "吉", 含义: "福寿圆满，富贵荣誉，涵养雅量，德高望重" },
    外格: { 数字: "2", 吉凶: "凶", 含义: "混沌未开，分离破败，二数不足，难得平安" }
  },
  命主八字命盘信息: {
    八字信息: {
      年柱: "戊辰",
      月柱: "壬戌",
      日柱: "丁未",
      时柱: "丁未",
      命理综述: "此命五行土旺缺金，日主天干为火，生于秋季，必须有土助，但忌金太多。为人诚实憨厚，但任性逞强，防止受骗，有时缺乏耐力，办事易半途而废。专劲不够。"
    },
    十神信息: {
      年干: "偏印",
      年支: "比肩",
      月干: "正官",
      月支: "偏财",
      日干: "日主",
      日支: "食神",
      时干: "比肩",
      时支: "食神"
    }
  },
  命主五行和喜神信息: {
    五行统计: {
      金: 0,
      木: 1,
      水: 1,
      火: 2,
      土: 4
    },
    喜用神: "金水",
    忌神: "火土",
    五行缺失: ["金"],
    建议: "宜佩戴金属饰品，多接触水，避免过多接触火和土属性的事物。适合从事金融、水利等行业。"
  },
  命主日干论命信息: {
    日干: "丁火",
    性格特点: "为人诚实憨厚，忠厚老实，但性格倔强，有时显得固执。心地善良，乐于助人，但容易被人利用。",
    事业运势: "事业发展较为平稳，适合从事技术性工作或服务行业。中年后事业运势转好，有望获得成功。",
    财运分析: "财运一般，早年财运不佳，中年后逐渐好转。不宜投机取巧，应脚踏实地积累财富。",
    感情运势: "感情运势较好，容易遇到真心相待的伴侣。婚姻生活和睦，但需要注意沟通方式。",
    健康状况: "身体健康状况良好，但需要注意心血管和消化系统的保养。适当运动，保持良好作息。",
    总体建议: "为人诚实可靠，但需要增强自信心，学会坚持。在事业上要有耐心，感情上要主动表达。"
  }
};

const BaziDemo: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<BaZiQueryFormValues | null>(null);
  const [loading, setLoading] = useState(false);

  const steps = [
    {
      title: '产品介绍',
      icon: <UserOutlined />,
    },
    {
      title: '填写信息',
      icon: <FormOutlined />,
    },
    {
      title: '支付订单',
      icon: <CreditCardOutlined />,
    },
    {
      title: '查看报告',
      icon: <FileTextOutlined />,
    },
  ];

  const handleFormSubmit = async (values: BaZiQueryFormValues) => {
    setFormData(values);
    setLoading(true);
    
    // 模拟支付过程
    setTimeout(() => {
      setLoading(false);
      setCurrentStep(2);
      message.success('信息提交成功，正在跳转支付页面...');
      
      // 模拟支付成功
      setTimeout(() => {
        setCurrentStep(3);
        message.success('支付成功！正在生成您的八字报告...');
      }, 2000);
    }, 1000);
  };

  const handleNext = () => {
    setCurrentStep(currentStep + 1);
  };

  const handlePrev = () => {
    setCurrentStep(currentStep - 1);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <Card style={{ textAlign: 'center', maxWidth: '600px', margin: '0 auto' }}>
            <Space direction="vertical" size="large">
              <Title level={2} style={{ color: '#1890ff' }}>
                <UserOutlined style={{ marginRight: '12px' }} />
                玄易八字命盘
              </Title>
              <Paragraph style={{ fontSize: '16px', color: '#666' }}>
                专业八字命理分析，为您解读人生运势
              </Paragraph>
              
              <Card size="small" style={{ backgroundColor: '#f6ffed', border: '1px solid #b7eb8f' }}>
                <Space direction="vertical">
                  <Title level={4} style={{ margin: 0, color: '#52c41a' }}>
                    八字命盘分析服务
                  </Title>
                  <div style={{ color: '#389e0d' }}>
                    <strong>价格: ¥99</strong>
                  </div>
                  <Paragraph style={{ margin: 0, fontSize: '14px' }}>
                    包含命主信息、八字称骨、姓名数理、命盘分析、五行喜神、日干论命等全面解读
                  </Paragraph>
                </Space>
              </Card>

              <Space wrap>
                <Tag color="blue">专业分析</Tag>
                <Tag color="green">详细报告</Tag>
                <Tag color="orange">运势指导</Tag>
                <Tag color="purple">改运建议</Tag>
              </Space>

              <Button type="primary" size="large" onClick={handleNext}>
                开始八字查询
              </Button>
            </Space>
          </Card>
        );

      case 1:
        return (
          <div style={{ maxWidth: '800px', margin: '0 auto' }}>
            <BaziForm
              onSubmit={handleFormSubmit}
              loading={loading}
              productInfo={{
                name: "玄易八字命盘",
                price: 99,
                description: "专业八字命理分析服务"
              }}
            />
            <div style={{ textAlign: 'center', marginTop: '24px' }}>
              <Button onClick={handlePrev} style={{ marginRight: '12px' }}>
                上一步
              </Button>
            </div>
          </div>
        );

      case 2:
        return (
          <Card style={{ textAlign: 'center', maxWidth: '600px', margin: '0 auto' }}>
            <Space direction="vertical" size="large">
              <Title level={3} style={{ color: '#faad14' }}>
                <CreditCardOutlined style={{ marginRight: '12px' }} />
                支付订单
              </Title>
              
              {formData && (
                <Card size="small" style={{ backgroundColor: '#f0f9ff' }}>
                  <Paragraph>
                    <strong>客户姓名：</strong>{formData.name}
                  </Paragraph>
                  <Paragraph>
                    <strong>出生日期：</strong>{formData.birthDate.toLocaleDateString()}
                  </Paragraph>
                  <Paragraph>
                    <strong>出生地点：</strong>{formData.birthProvince} {formData.birthCity}
                  </Paragraph>
                </Card>
              )}

              <div style={{ fontSize: '18px', color: '#52c41a' }}>
                <strong>支付金额：¥99</strong>
              </div>

              <Paragraph style={{ color: '#666' }}>
                正在模拟支付过程，请稍候...
              </Paragraph>

              <div style={{ color: '#1890ff' }}>
                支付成功后将自动生成您的专属八字报告
              </div>
            </Space>
          </Card>
        );

      case 3:
        return (
          <div>
            <BaziReportEnhanced 
              data={mockReportData}
              customerName={formData?.name || "张三"}
            />
            <div style={{ textAlign: 'center', marginTop: '24px' }}>
              <Button onClick={() => setCurrentStep(0)} type="primary">
                重新开始
              </Button>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5', padding: '24px' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={1} style={{ color: '#1890ff' }}>
            八字命盘演示系统
          </Title>
          <Paragraph style={{ fontSize: '16px', color: '#666' }}>
            体验完整的八字查询流程 - 从信息填写到报告生成
          </Paragraph>
        </div>

        <Card style={{ marginBottom: '24px' }}>
          <Steps current={currentStep} items={steps} />
        </Card>

        {renderStepContent()}
      </div>
    </div>
  );
};

export default BaziDemo; 