import sequelize from '../config/database.js';
import { DataTypes } from 'sequelize';

/**
 * 创建 SystemConfigs 表的迁移脚本
 * 该表用于存储系统全局配置项，包括支付相关设置等
 */
const createSystemConfigTable = async () => {
  try {
    console.log('开始创建 SystemConfigs 表...');

    await sequelize.getQueryInterface().createTable('SystemConfigs', {
      key: {
        type: DataTypes.STRING,
        primaryKey: true,
        allowNull: false
      },
      value: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true
      },
      group: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'general'
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
      }
    });

    console.log('SystemConfigs 表创建成功!');
    
    // 创建初始配置项
    const initialConfigs = [
      // 支付宝基本配置
      {
        key: 'ALIPAY_APPID',
        value: process.env.ALIPAY_APPID || '',
        description: '支付宝应用ID',
        group: 'payment',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'ALIPAY_PRIVATE_KEY',
        value: process.env.ALIPAY_PRIVATE_KEY || '',
        description: '支付宝应用私钥',
        group: 'payment',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'ALIPAY_PUBLIC_KEY',
        value: process.env.ALIPAY_PUBLIC_KEY || '',
        description: '支付宝公钥',
        group: 'payment',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'ALIPAY_NOTIFY_URL',
        value: process.env.ALIPAY_NOTIFY_URL || '',
        description: '支付宝异步回调地址',
        group: 'payment',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'ALIPAY_RETURN_URL',
        value: process.env.ALIPAY_RETURN_URL || '',
        description: '支付宝同步回调地址',
        group: 'payment',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    // 插入初始配置
    await sequelize.getQueryInterface().bulkInsert('SystemConfigs', initialConfigs);
    console.log('初始支付配置创建成功!');
    
  } catch (error) {
    console.error('创建 SystemConfigs 表失败:', error);
    
    // 检查表是否已存在
    if (error.name === 'SequelizeUniqueConstraintError' || error.message.includes('already exists')) {
      console.log('SystemConfigs 表已存在，跳过创建');
    } else {
      throw error;
    }
  }
};

// 执行迁移
createSystemConfigTable()
  .then(() => {
    console.log('SystemConfigs表迁移完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('迁移失败:', error);
    process.exit(1);
  });
