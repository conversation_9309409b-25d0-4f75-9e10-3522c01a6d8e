import React, { useState, useEffect } from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  Layout as AntLayout, 
  Menu, 
  Button,
  Avatar,
  Dropdown,
  Typography,
  Space,
  Divider
} from 'antd';
import {
  HomeOutlined,
  ShoppingOutlined,
  FileDoneOutlined,
  WalletOutlined,
  TeamOutlined,
  UserOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  LogoutOutlined,
  BankOutlined,
  DashboardOutlined,
  AlipayOutlined,
  CustomerServiceOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Header, Sider, Content, Footer } = AntLayout;
const { Title, Text } = Typography;

const Layout: React.FC = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const location = useLocation();

  // 根据用户角色获取菜单项
  const getMenuItems = () => {
    const items = [
      {
        key: '/',
        icon: <HomeOutlined />,
        label: <Link to="/">首页</Link>,
      },
      {
        key: '/products',
        icon: <ShoppingOutlined />,
        label: <Link to="/products">产品列表</Link>,
      }
    ];

    if (isAuthenticated) {
      items.push({
        key: '/orders',
        icon: <FileDoneOutlined />,
        label: <Link to="/orders">订单管理</Link>,
      });

      if (user?.role !== 'admin') {
        items.push({
          key: '/withdrawals',
          icon: <WalletOutlined />,
          label: <Link to="/withdrawals">我的提现</Link>,
        });
      }

      if (user?.role === 'admin') {
        items.push({
          key: '/users',
          icon: <UserOutlined />,
          label: <Link to="/users">用户管理</Link>,
        });
        items.push({
          key: '/withdrawal-management',
          icon: <BankOutlined />,
          label: <Link to="/withdrawal-management">提现管理</Link>,
        });
        items.push({
          key: '/payment-management',
          icon: <AlipayOutlined />,
          label: <Link to="/payment-management">支付管理</Link>,
        });
        items.push({
          key: '/system-config',
          icon: <SettingOutlined />,
          label: <Link to="/system-config">系统配置</Link>,
        });
      }

      if (user?.role === 'user' && user?.level === 1) {
        items.push({
          key: '/team',
          icon: <TeamOutlined />,
          label: <Link to="/team">我的团队</Link>,
        });
      }
    }

    // 将联系我们添加到菜单的最后一行
    items.push({
      key: 'contact',
      icon: <CustomerServiceOutlined />,
      label: <a href="https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd" target="_blank" rel="noopener noreferrer">联系我们</a>,
    });

    return items;
  };

  // 用户菜单下拉项
  const userMenu = {
    items: [
      {
        key: 'profile',
        label: '个人资料',
        icon: <UserOutlined />,
      },
      {
        key: 'logout',
        label: '退出登录',
        icon: <LogoutOutlined />,
        danger: true,
      },
    ],
    onClick: ({ key }) => {
      if (key === 'logout') {
        logout();
      }
    },
  };

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      // 在移动端自动折叠菜单
      if (mobile) {
        setCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    handleResize(); // 初始化时执行一次
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 确保在路由切换时移动端收起菜单
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    }
  }, [location.pathname, isMobile]);

  return (
    <AntLayout style={{ minHeight: '100vh' }}>
      {/* 移动端遮罩层 */}
      {isAuthenticated && isMobile && !collapsed && (
        <div 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.45)',
            zIndex: 999,
          }}
          onClick={() => setCollapsed(true)}
        />
      )}
      
      {isAuthenticated && (
        <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={(value) => setCollapsed(value)}
          collapsedWidth={isMobile ? 0 : 80}
          trigger={null}
          theme="light"
          style={{
            overflow: 'auto',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            zIndex: 1001,
            transform: isMobile && collapsed ? 'translateX(-100%)' : 'translateX(0)',
            transition: 'transform 0.3s ease'
          }}
        >
          <div className="logo" style={{ height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center', margin: '16px 0' }}>
            <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
              {collapsed ? 'BM' : '佣金平台'}
            </Title>
          </div>
          <Menu
            theme="light"
            selectedKeys={[location.pathname]}
            mode="inline"
            items={getMenuItems()}
            onClick={() => {
              // 当在移动端时，点击菜单项自动收起侧边栏
              if (isMobile) {
                setCollapsed(true);
              }
            }}
          />
        </Sider>
      )}

      <AntLayout style={{ 
        marginLeft: isAuthenticated ? (!isMobile ? (collapsed ? 80 : 220) : 0) : 0, 
        transition: 'all 0.3s ease',
        position: 'relative'
      }}>
        <Header style={{ 
          padding: '0 16px',
          background: '#fff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          height: 64,
          position: 'sticky',
          top: 0,
          zIndex: 100,
          width: '100%'
        }}>
          <div className="header-left" style={{ display: 'flex', alignItems: 'center' }}>
            {isAuthenticated && (
              <Button
                type="text"
                icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                onClick={() => setCollapsed(!collapsed)}
                style={{ 
                  fontSize: '20px', 
                  width: 48, 
                  height: 48,
                  padding: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: '#1890ff',
                  marginRight: 8
                }}
              />
            )}
          </div>
          <div className="header-right">
            {isAuthenticated ? (
              <Dropdown menu={userMenu} placement="bottomRight">
                <div style={{ cursor: 'pointer', display: 'flex', alignItems: 'center' }}>
                  <Avatar icon={<UserOutlined />} style={{ marginRight: 8 }} />
                  <span className="user-name" style={{ 
                    display: 'inline-block',
                    maxWidth: '120px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}>{user?.name}</span>
                </div>
              </Dropdown>
            ) : (
              <Space>
                <Link to="/login">
                  <Button type="primary">登录</Button>
                </Link>
                <Link to="/register">
                  <Button>注册</Button>
                </Link>
              </Space>
            )}
          </div>
        </Header>

        <Content style={{ margin: '24px 16px', padding: 24, background: '#fff', minHeight: 280, borderRadius: 4 }}>
          <Outlet />
        </Content>

        <Footer style={{ textAlign: 'center', background: 'transparent' }}>
          <Text type="secondary"> 2025 佣金管理平台 - 提供卓越的佣金管理服务</Text>
        </Footer>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;