import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

/**
 * 支付配置模型
 * 用于存储代理的个人支付配置信息
 */
const PaymentConfig = sequelize.define('PaymentConfig', {
  // 唯一ID
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  
  // 关联的用户ID (一级代理)
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  
  // 支付宝APPID
  alipayAppId: {
    type: DataTypes.STRING,
    allowNull: false
  },
  
  // 支付宝私钥
  alipayPrivateKey: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  
  // 支付宝公钥
  alipayPublicKey: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  
  // 是否启用此配置
  isActive: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  
  // 备注说明
  description: {
    type: DataTypes.STRING,
    allowNull: true
  }
}, {
  tableName: 'PaymentConfigs',
  timestamps: true
});

export default PaymentConfig;
