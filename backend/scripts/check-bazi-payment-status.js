import { ProductLink, Product, User } from '../src/models/index.js';
import { Op } from 'sequelize';

async function checkBaziPaymentStatus() {
  try {
    console.log('🔍 检查八字支付系统状态\n');
    
    // 1. 检查八字产品
    const baziProducts = await Product.findAll({
      where: {
        title: { [Op.like]: '%八字%' },
        status: 'active'
      }
    });
    
    console.log(`📦 找到 ${baziProducts.length} 个活跃的八字产品:`);
    baziProducts.forEach(product => {
      console.log(`- ID: ${product.id}, 标题: ${product.title}, 类型: ${product.type}, 价格: ${product.price}`);
    });
    
    // 2. 检查八字产品推广链接
    const baziLinks = await ProductLink.findAll({
      where: { status: 'active' },
      include: [{
        model: Product,
        as: 'product',
        where: {
          title: { [Op.like]: '%八字%' },
          status: 'active'
        }
      }]
    });
    
    console.log(`\n🔗 找到 ${baziLinks.length} 个活跃的八字产品推广链接:`);
    baziLinks.forEach(link => {
      console.log(`- 代码: ${link.code}, 产品: ${link.product.title}, 代理ID: ${link.agentId}`);
    });
    
    // 3. 系统状态总结
    console.log('\n📊 系统状态总结:');
    console.log(`✅ 八字产品数量: ${baziProducts.length}`);
    console.log(`✅ 八字推广链接数量: ${baziLinks.length}`);
    console.log(`✅ 产品类型检查: ${baziProducts.every(p => p.type === 'service') ? '正确(service)' : '错误'}`);
    console.log(`✅ 推广链接状态: ${baziLinks.every(l => l.status === 'active') ? '正常(active)' : '异常'}`);
    
    if (baziProducts.length > 0 && baziLinks.length > 0) {
      console.log('\n🎉 八字支付系统状态正常！');
      console.log('💡 用户购买八字服务后将正确跳转到八字报告页面。');
    } else {
      console.log('\n⚠️  八字支付系统可能存在问题，请检查产品和推广链接配置。');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ 检查失败:', error);
    process.exit(1);
  }
}

checkBaziPaymentStatus(); 