import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Layout from './components/Layout';
import PrivateRoute from './components/PrivateRoute';
import ScrollToTop from './components/ScrollToTop';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Products from './pages/Products';
import Orders from './pages/Orders';
import Withdrawals from './pages/Withdrawals';
import ProductDetail from './pages/ProductDetail';
import UserManagement from './pages/UserManagement';
import TeamManagement from './pages/TeamManagement';
import WithdrawalManagement from './pages/WithdrawalManagement';
import PaymentManagement from './pages/PaymentManagement';
import BaziQuery from './pages/BaziQuery';

import BaziTestSimple from './pages/BaziTestSimple';
import BaziDemo from './pages/BaziDemo';
import BaziReport from './pages/BaziReport';
import BaziReportDemo from './pages/BaziReportDemo';
import NamingServiceSuccess from './pages/NamingServiceSuccess';
import SystemConfig from './pages/SystemConfig';

function App() {
  return (
    <Router>
      <AuthProvider>
        <ScrollToTop />
        <Routes>
          {/* 公共路由 - 不需要Layout */}
          <Route path="/product/:code" element={<ProductDetail />} />
          <Route path="/bazi/:code" element={<BaziQuery />} />
          <Route path="/bazi-query/:code" element={<BaziQuery />} />

          <Route path="/bazi-test-simple" element={<BaziTestSimple />} />
          <Route path="/bazi-demo" element={<BaziDemo />} />
          <Route path="/bazi-report-demo" element={<BaziReportDemo />} />
          <Route path="/report" element={<BaziReport />} />
          <Route path="/bazi-report" element={<BaziReport />} />
          <Route path="/naming-service-success" element={<NamingServiceSuccess />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* 需要Layout的路由 */}
          <Route path="/" element={<Layout />}>
            {/* 受保护路由 */}
            <Route element={<PrivateRoute />}>
              <Route index element={<Home />} />
              <Route path="products" element={<Products />} />
              <Route path="orders" element={<Orders />} />
              <Route path="withdrawals" element={<Withdrawals />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="team" element={<TeamManagement />} />
              {/* 管理员专用路由 */}
              <Route path="withdrawal-management" element={<WithdrawalManagement />} />
              <Route path="payment-management" element={<PaymentManagement />} />
              <Route path="system-config" element={<SystemConfig />} />
            </Route>
          </Route>
        </Routes>
      </AuthProvider>
    </Router>
  );
}

export default App;