import React from 'react';
import { clsx } from '@/utils';

interface TagProps {
  children: React.ReactNode;
  selected?: boolean;
  onClick?: () => void;
  color?: string;
  size?: 'small' | 'medium' | 'large';
  className?: string;
}

export const Tag: React.FC<TagProps> = ({
  children,
  selected = false,
  onClick,
  color = 'primary',
  size = 'medium',
  className,
}) => {
  const sizeClasses = {
    small: 'px-2 py-1 text-xs',
    medium: 'px-3 py-1 text-sm',
    large: 'px-4 py-2 text-base',
  };

  return (
    <button
      className={clsx(
        'tag-button',
        sizeClasses[size],
        selected ? 'selected' : 'unselected',
        className
      )}
      onClick={onClick}
    >
      {children}
    </button>
  );
};
