#!/bin/bash

# 快速修复远程服务器后端问题
REMOTE_HOST="root@************"

echo "🔧 快速修复远程服务器后端问题..."
echo "📝 请手动执行以下命令来修复问题："
echo ""

cat << 'EOF'
# 1. 连接到远程服务器
ssh root@************

# 2. 进入项目目录并检查状态
cd /data/commission-platform8z/backend
pwd
ls -la

# 3. 检查Node.js版本
echo "🔍 Node.js版本:"
node --version
npm --version

# 4. 检查PM2状态和日志
echo "🔍 PM2状态:"
pm2 status

echo "🔍 查看错误日志:"
pm2 logs commission-platform-api --lines 20 --nostream

# 5. 停止并清理现有服务
echo "🛑 停止现有服务..."
pm2 stop commission-platform-api 2>/dev/null || echo "服务未运行"
pm2 delete commission-platform-api 2>/dev/null || echo "服务不存在"

# 6. 清理端口
echo "🔧 清理端口3005..."
lsof -ti:3005 | xargs kill -9 2>/dev/null || echo "端口未被占用"

# 7. 重新安装依赖
echo "📦 重新安装依赖..."
npm install --production

# 8. 检查关键文件
echo "🔍 检查关键文件:"
ls -la start-production.js src/index.js

# 9. 创建环境变量文件（如果不存在）
if [ ! -f ".env.production" ]; then
    echo "📝 创建 .env.production 文件..."
    cat > .env.production << 'ENVEOF'
NODE_ENV=production
PORT=3005

# 数据库配置 - 请根据实际情况修改
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=commission_platform

# JWT密钥
JWT_SECRET=your_jwt_secret_key_here

# 支付宝配置
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=your_public_key

# 前端URL
FRONTEND_URL=https://ye.bzcy.xyz
ENVEOF
    echo "✅ .env.production 文件已创建，请根据实际情况修改配置"
fi

# 10. 尝试手动启动测试
echo "🧪 测试手动启动..."
timeout 10s node src/index.js &
TEST_PID=$!
sleep 5

if kill -0 $TEST_PID 2>/dev/null; then
    echo "✅ 手动启动测试成功"
    kill $TEST_PID
    
    # 11. 使用PM2启动
    echo "🚀 使用PM2启动服务..."
    pm2 start start-production.js --name commission-platform-api --log-date-format="YYYY-MM-DD HH:mm:ss"
    
else
    echo "❌ 手动启动测试失败，尝试直接用PM2启动..."
    # 备用方案：直接启动src/index.js
    pm2 start src/index.js --name commission-platform-api --env production
fi

# 12. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 13. 检查最终状态
echo "📊 检查最终状态:"
pm2 status

echo "🔍 查看最新日志:"
pm2 logs commission-platform-api --lines 10 --nostream

# 14. 测试API
echo "🧪 测试API连接:"
curl -f http://localhost:3005/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}' 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ API测试成功"
else
    echo "❌ API测试失败，请检查日志"
fi

# 15. 保存PM2配置
echo "💾 保存PM2配置..."
pm2 save

echo ""
echo "✅ 修复完成！"
echo ""
echo "📝 如果仍有问题，请检查:"
echo "  1. Node.js版本是否 >= 14.x"
echo "  2. .env.production 配置是否正确"
echo "  3. 数据库是否可连接"
echo "  4. 防火墙是否开放3005端口"

EOF

echo ""
echo "💡 提示："
echo "  1. 复制上面的命令到终端执行"
echo "  2. 或者直接运行: ssh root@************"
echo "  3. 然后逐步执行上面的命令"
echo ""
echo "🔍 快速检查命令："
echo "  ssh root@************ 'pm2 status'"
echo "  ssh root@************ 'pm2 logs commission-platform-api --lines 10'"
