import React from 'react';
import { Card as AntCard, Table as AntTable, But<PERSON> as AntButton, Typography } from 'antd';
import { TableProps } from 'antd/lib/table';
import styled from 'styled-components';

const { Title, Text } = Typography;

// 主题颜色定义
export const colors = {
  primary: '#1890ff',
  success: '#52c41a',
  warning: '#faad14',
  error: '#f5222d',
  dark: '#001529',
  light: '#ffffff',
  gray: '#f0f2f5',
  text: {
    primary: '#000000d9',
    secondary: '#00000073',
    disabled: '#00000040',
  },
  border: '#f0f0f0',
  background: {
    light: '#ffffff',
    gray: '#f0f2f5',
    card: '#ffffff',
  }
};

// 阴影样式
export const shadows = {
  small: '0 2px 4px rgba(0,0,0,0.05)',
  medium: '0 4px 8px rgba(0,0,0,0.1)',
  large: '0 6px 16px rgba(0,0,0,0.1)',
  hover: '0 8px 24px rgba(0,0,0,0.15)',
};

// 布局常量
export const layout = {
  borderRadius: '6px',
  headerHeight: 64,
  sidebarWidth: 220,
  sidebarCollapsedWidth: 80,
  contentPadding: 24,
};

// Card 组件样式增强
export const Card = styled(AntCard)`
  border-radius: ${layout.borderRadius};
  overflow: hidden;
  box-shadow: ${shadows.small};
  border: none;
  transition: all 0.3s;
  
  &:hover {
    box-shadow: ${shadows.medium};
  }
  
  .ant-card-head {
    border-bottom: 1px solid ${colors.border};
    min-height: 50px;
  }
  
  &.highlight-card {
    border-top: 3px solid ${colors.primary};
  }
  
  &.success-card {
    border-top: 3px solid ${colors.success};
  }
  
  &.warning-card {
    border-top: 3px solid ${colors.warning};
  }
  
  &.error-card {
    border-top: 3px solid ${colors.error};
  }
`;

// 统计数据卡片
export const StatsCard = styled(Card)`
  height: 100%;
  
  .stats-icon {
    font-size: 28px;
    padding: 10px;
    border-radius: 8px;
    display: inline-flex;
  }
  
  .stats-content {
    display: flex;
    flex-direction: column;
  }
  
  .stats-title {
    color: ${colors.text.secondary};
    font-size: 14px;
  }
  
  .stats-value {
    font-size: 24px;
    font-weight: 600;
    margin: 8px 0;
  }
  
  .stats-footer {
    margin-top: 10px;
    display: flex;
    align-items: center;
    font-size: 12px;
  }
`;

// 页面标题
export const PageTitle = styled(Title)`
  margin-bottom: 24px !important;
  display: flex;
  align-items: center;
  
  .icon {
    margin-right: 12px;
    font-size: 24px;
  }
`;

// 页面描述
export const PageDescription = styled(Text)`
  display: block;
  margin-bottom: 24px;
  color: ${colors.text.secondary};
`;

// 表格增强
export const Table = <T extends object>(props: TableProps<T>) => (
  <AntTable<T>
    {...props}
    className={`enhanced-table ${props.className || ''}`}
    rowClassName={(record, index) => (index % 2 === 0 ? 'even-row' : 'odd-row')}
    style={{ 
      borderRadius: layout.borderRadius,
      overflow: 'hidden',
      boxShadow: shadows.small,
      ...props.style
    }}
  />
);

// 按钮增强
export const Button = styled(AntButton)`
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  &.ant-btn-primary {
    background: ${colors.primary};
    border-color: ${colors.primary};
    box-shadow: 0 2px 0 rgba(24, 144, 255, 0.1);
    
    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }
  
  &.ant-btn-success {
    background: ${colors.success};
    border-color: ${colors.success};
    color: white;
    
    &:hover {
      background: #73d13d;
      border-color: #73d13d;
    }
  }
  
  &.ant-btn-warning {
    background: ${colors.warning};
    border-color: ${colors.warning};
    color: white;
    
    &:hover {
      background: #ffc53d;
      border-color: #ffc53d;
    }
  }
  
  &.ant-btn-danger {
    background: ${colors.error};
    border-color: ${colors.error};
    
    &:hover {
      background: #ff4d4f;
      border-color: #ff4d4f;
    }
  }
`;

// 表单区域容器
export const FormSection = styled.div`
  background: ${colors.background.light};
  border-radius: ${layout.borderRadius};
  padding: ${layout.contentPadding}px;
  margin-bottom: 24px;
  box-shadow: ${shadows.small};
`;

// 页面容器
export const PageContainer = styled.div`
  padding: 0;
  
  .page-header {
    margin-bottom: 24px;
  }
  
  .page-content {
    margin-bottom: 24px;
  }
  
  .page-footer {
    margin-top: 24px;
  }
`;

// 状态标签
export const StatusTag = styled.span<{ status: 'success' | 'warning' | 'error' | 'default' }>`
  display: inline-flex;
  align-items: center;
  padding: 0 8px;
  font-size: 12px;
  line-height: 20px;
  border-radius: 10px;
  
  background-color: ${({ status }) => {
    switch (status) {
      case 'success': return `rgba(82, 196, 26, 0.15)`;
      case 'warning': return `rgba(250, 173, 20, 0.15)`;
      case 'error': return `rgba(245, 34, 45, 0.15)`;
      default: return `rgba(0, 0, 0, 0.06)`;
    }
  }};
  
  color: ${({ status }) => {
    switch (status) {
      case 'success': return colors.success;
      case 'warning': return colors.warning;
      case 'error': return colors.error;
      default: return colors.text.secondary;
    }
  }};
`;

// 过滤区域容器
export const FilterContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 24px;
  gap: 12px;
  align-items: center;
  
  .filter-item {
    margin-right: 12px;
    min-width: 120px;
  }
  
  .filter-actions {
    margin-left: auto;
  }
`;

export default {
  colors,
  shadows,
  layout,
  Card,
  StatsCard,
  PageTitle,
  PageDescription,
  Table,
  Button,
  FormSection,
  PageContainer,
  StatusTag,
  FilterContainer
};
