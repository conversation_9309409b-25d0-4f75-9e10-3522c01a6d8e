import React, { useState, useEffect } from 'react';
import { SimpleModal } from './ui/SimpleModal';

interface AreaSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: { province: string; city: string; district?: string }) => void;
}

// 完整的中国地区数据
const areaData = {
  '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
  '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
  '天津市': ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '东丽区', '西青区', '津南区', '北辰区', '武清区', '宝坻区', '滨海新区', '宁河区', '静海区', '蓟州区'],
  '重庆市': ['万州区', '涪陵区', '渝中区', '大渡口区', '江北区', '沙坪坝区', '九龙坡区', '南岸区', '北碚区', '綦江区', '大足区', '渝北区', '巴南区', '黔江区', '长寿区', '江津区', '合川区', '永川区', '南川区', '璧山区', '铜梁区', '潼南区', '荣昌区', '开州区', '梁平区', '武隆区'],
  '河北省': {
    '石家庄市': ['长安区', '桥西区', '新华区', '井陉矿区', '裕华区', '藁城区', '鹿泉区', '栾城区', '井陉县', '正定县', '行唐县', '灵寿县', '高邑县', '深泽县', '赞皇县', '无极县', '平山县', '元氏县', '赵县', '辛集市', '晋州市', '新乐市'],
    '唐山市': ['路南区', '路北区', '古冶区', '开平区', '丰南区', '丰润区', '曹妃甸区', '滦州市', '滦南县', '乐亭县', '迁西县', '玉田县', '遵化市', '迁安市'],
    '秦皇岛市': ['海港区', '山海关区', '北戴河区', '抚宁区', '青龙满族自治县', '昌黎县', '卢龙县'],
    '邯郸市': ['邯山区', '丛台区', '复兴区', '峰峰矿区', '肥乡区', '永年区', '临漳县', '成安县', '大名县', '涉县', '磁县', '邱县', '鸡泽县', '广平县', '馆陶县', '魏县', '曲周县', '武安市'],
    '邢台市': ['桥东区', '桥西区', '邢台县', '临城县', '内丘县', '柏乡县', '隆尧县', '任县', '南和县', '宁晋县', '巨鹿县', '新河县', '广宗县', '平乡县', '威县', '清河县', '临西县', '南宫市', '沙河市'],
    '保定市': ['竞秀区', '莲池区', '满城区', '清苑区', '徐水区', '涞水县', '阜平县', '定兴县', '唐县', '高阳县', '容城县', '涞源县', '望都县', '安新县', '易县', '曲阳县', '蠡县', '顺平县', '博野县', '雄县', '涿州市', '定州市', '安国市', '高碑店市'],
    '张家口市': ['桥东区', '桥西区', '宣化区', '下花园区', '万全区', '崇礼区', '张北县', '康保县', '沽源县', '尚义县', '蔚县', '阳原县', '怀安县', '怀来县', '涿鹿县', '赤城县'],
    '承德市': ['双桥区', '双滦区', '鹰手营子矿区', '承德县', '兴隆县', '平泉市', '滦平县', '隆化县', '丰宁满族自治县', '宽城满族自治县', '围场满族蒙古族自治县'],
    '沧州市': ['新华区', '运河区', '沧县', '青县', '东光县', '海兴县', '盐山县', '肃宁县', '南皮县', '吴桥县', '献县', '孟村回族自治县', '泊头市', '任丘市', '黄骅市', '河间市'],
    '廊坊市': ['安次区', '广阳区', '固安县', '永清县', '香河县', '大城县', '文安县', '大厂回族自治县', '霸州市', '三河市'],
    '衡水市': ['桃城区', '冀州区', '枣强县', '武邑县', '武强县', '饶阳县', '安平县', '故城县', '景县', '阜城县', '深州市']
  },
  '山西省': {
    '太原市': ['小店区', '迎泽区', '杏花岭区', '尖草坪区', '万柏林区', '晋源区', '清徐县', '阳曲县', '娄烦县', '古交市'],
    '大同市': ['城区', '矿区', '南郊区', '新荣区', '阳高县', '天镇县', '广灵县', '灵丘县', '浑源县', '左云县', '大同县'],
    '阳泉市': ['城区', '矿区', '郊区', '平定县', '盂县'],
    '长治市': ['城区', '郊区', '长治县', '襄垣县', '屯留县', '平顺县', '黎城县', '壶关县', '长子县', '武乡县', '沁县', '沁源县', '潞城市'],
    '晋城市': ['城区', '沁水县', '阳城县', '陵川县', '泽州县', '高平市'],
    '朔州市': ['朔城区', '平鲁区', '山阴县', '应县', '右玉县', '怀仁市'],
    '晋中市': ['榆次区', '榆社县', '左权县', '和顺县', '昔阳县', '寿阳县', '太谷县', '祁县', '平遥县', '灵石县', '介休市'],
    '运城市': ['盐湖区', '临猗县', '万荣县', '闻喜县', '稷山县', '新绛县', '绛县', '垣曲县', '夏县', '平陆县', '芮城县', '永济市', '河津市'],
    '忻州市': ['忻府区', '定襄县', '五台县', '代县', '繁峙县', '宁武县', '静乐县', '神池县', '五寨县', '岢岚县', '河曲县', '保德县', '偏关县', '原平市'],
    '临汾市': ['尧都区', '曲沃县', '翼城县', '襄汾县', '洪洞县', '古县', '安泽县', '浮山县', '吉县', '乡宁县', '大宁县', '隰县', '永和县', '蒲县', '汾西县', '侯马市', '霍州市'],
    '吕梁市': ['离石区', '文水县', '交城县', '兴县', '临县', '柳林县', '石楼县', '岚县', '方山县', '中阳县', '交口县', '孝义市', '汾阳市']
  }
};

export const AreaSelector: React.FC<AreaSelectorProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [selectedProvince, setSelectedProvince] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedDistrict, setSelectedDistrict] = useState('');

  const provinces = Object.keys(areaData);
  
  const cities = selectedProvince && typeof areaData[selectedProvince as keyof typeof areaData] === 'object' 
    ? Object.keys(areaData[selectedProvince as keyof typeof areaData] as object)
    : [];
    
  const districts = selectedProvince && selectedCity
    ? (() => {
        const provinceData = areaData[selectedProvince as keyof typeof areaData];
        if (Array.isArray(provinceData)) {
          return provinceData; // 直辖市
        } else if (typeof provinceData === 'object' && selectedCity) {
          return (provinceData as any)[selectedCity] || [];
        }
        return [];
      })()
    : [];

  // 重置下级选择
  useEffect(() => {
    setSelectedCity('');
    setSelectedDistrict('');
  }, [selectedProvince]);

  useEffect(() => {
    setSelectedDistrict('');
  }, [selectedCity]);

  const handleConfirm = () => {
    if (!selectedProvince) {
      alert('请选择省份');
      return;
    }
    
    // 对于直辖市，city就是district
    if (Array.isArray(areaData[selectedProvince as keyof typeof areaData])) {
      if (!selectedCity) {
        alert('请选择区县');
        return;
      }
      onConfirm({
        province: selectedProvince,
        city: selectedProvince, // 直辖市的city就是省份名
        district: selectedCity // 实际选择的是区县
      });
    } else {
      if (!selectedCity) {
        alert('请选择城市');
        return;
      }
      if (!selectedDistrict) {
        alert('请选择区县');
        return;
      }
      onConfirm({
        province: selectedProvince,
        city: selectedCity,
        district: selectedDistrict
      });
    }
    onClose();
  };

  return (
    <SimpleModal isOpen={isOpen} onClose={onClose} title="选择地区">
      <div className="area-selector">
        <div className="area-selectors">
          <div className="selector-group">
            <label>省/直辖市</label>
            <select value={selectedProvince} onChange={(e) => setSelectedProvince(e.target.value)}>
              <option value="">请选择省/直辖市</option>
              {provinces.map(province => (
                <option key={province} value={province}>{province}</option>
              ))}
            </select>
          </div>

          {selectedProvince && (
            <div className="selector-group">
              <label>{Array.isArray(areaData[selectedProvince as keyof typeof areaData]) ? '区/县' : '市'}</label>
              <select value={selectedCity} onChange={(e) => setSelectedCity(e.target.value)}>
                <option value="">{Array.isArray(areaData[selectedProvince as keyof typeof areaData]) ? '请选择区/县' : '请选择市'}</option>
                {Array.isArray(areaData[selectedProvince as keyof typeof areaData]) 
                  ? (areaData[selectedProvince as keyof typeof areaData] as string[]).map(district => (
                      <option key={district} value={district}>{district}</option>
                    ))
                  : cities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))
                }
              </select>
            </div>
          )}

          {selectedProvince && selectedCity && !Array.isArray(areaData[selectedProvince as keyof typeof areaData]) && (
            <div className="selector-group">
              <label>区/县</label>
              <select value={selectedDistrict} onChange={(e) => setSelectedDistrict(e.target.value)}>
                <option value="">请选择区/县</option>
                {districts.map(district => (
                  <option key={district} value={district}>{district}</option>
                ))}
              </select>
            </div>
          )}
        </div>

        <div className="area-actions">
          <button className="cancel-button" onClick={onClose}>取消</button>
          <button className="confirm-button" onClick={handleConfirm}>确认</button>
        </div>
      </div>
    </SimpleModal>
  );
};
