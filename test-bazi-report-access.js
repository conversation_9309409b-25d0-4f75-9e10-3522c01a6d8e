// 测试八字报告页面访问和功能
import axios from 'axios';

const config = {
  FRONTEND_URL: 'http://localhost:3000',
  BACKEND_URL: 'http://localhost:3005'
};

async function testBaziReportAccess() {
  console.log('🧪 测试八字报告页面访问');
  console.log('='.repeat(50));
  
  try {
    // 1. 测试前端页面访问
    console.log('1️⃣ 测试前端页面访问');
    try {
      const frontendResponse = await axios.get(`${config.FRONTEND_URL}/bazi-report`);
      console.log('✅ 前端页面访问成功');
      console.log(`   状态码: ${frontendResponse.status}`);
    } catch (error) {
      console.log('❌ 前端页面访问失败:', error.message);
    }
    
    // 2. 测试后端API接口
    console.log('\n2️⃣ 测试后端API接口');
    try {
      const apiResponse = await axios.get(`${config.BACKEND_URL}/api/health`);
      console.log('✅ 后端API服务正常');
      console.log(`   状态码: ${apiResponse.status}`);
    } catch (error) {
      console.log('❌ 后端API服务异常:', error.message);
    }
    
    // 3. 测试八字报告API
    console.log('\n3️⃣ 测试八字报告API');
    const testApiParams = {
      xing: '张',
      ming: '三',
      sex: '男',
      yearType: '公历',
      year: '1990',
      month: '5',
      day: '15',
      hour: '10',
      minute: '30',
      address: '北京市'
    };
    
    try {
      const reportResponse = await axios.post(`${config.BACKEND_URL}/api/bazi/report`, testApiParams);
      console.log('✅ 八字报告API调用成功');
      console.log(`   状态码: ${reportResponse.status}`);
      console.log(`   响应数据: ${reportResponse.data.success ? '成功' : '失败'}`);
    } catch (error) {
      console.log('❌ 八字报告API调用失败:', error.message);
    }
    
    // 4. 测试订单报告获取
    console.log('\n4️⃣ 测试订单报告获取');
    const testOrderId = '250706013212214289'; // 使用之前的测试订单ID
    
    try {
      const orderReportResponse = await axios.get(`${config.BACKEND_URL}/api/bazi/orders/${testOrderId}/report`);
      console.log('✅ 订单报告获取成功');
      console.log(`   状态码: ${orderReportResponse.status}`);
      console.log(`   响应数据: ${orderReportResponse.data.success ? '成功' : '失败'}`);
    } catch (error) {
      console.log('❌ 订单报告获取失败:', error.message);
    }
    
    // 5. 测试支付跳转路径
    console.log('\n5️⃣ 测试支付跳转路径');
    const testOrderId2 = 'test-order-123';
    const redirectUrl = `${config.FRONTEND_URL}/bazi-report?orderId=${testOrderId2}`;
    
    console.log('📍 八字查询服务支付成功后的跳转地址:');
    console.log(`   ${redirectUrl}`);
    
    try {
      const redirectResponse = await axios.get(redirectUrl);
      console.log('✅ 支付跳转页面访问成功');
      console.log(`   状态码: ${redirectResponse.status}`);
    } catch (error) {
      console.log('❌ 支付跳转页面访问失败:', error.message);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('📋 测试总结:');
    console.log('📱 前端服务地址: ' + config.FRONTEND_URL);
    console.log('🔧 后端服务地址: ' + config.BACKEND_URL);
    console.log('📄 八字报告页面: ' + config.FRONTEND_URL + '/bazi-report');
    console.log('🔗 带订单ID的报告页面: ' + config.FRONTEND_URL + '/bazi-report?orderId={订单ID}');
    console.log('');
    console.log('🎯 用户支付八字查询服务成功后，将跳转到:');
    console.log('   ' + config.FRONTEND_URL + '/bazi-report?orderId={实际订单ID}');
    console.log('');
    console.log('📋 页面功能说明:');
    console.log('   • 支持通过订单ID获取已生成的八字报告');
    console.log('   • 支持通过URL参数直接生成八字报告（兼容旧方式）');
    console.log('   • 显示详细的八字命盘分析结果');
    console.log('   • 包含完整的命理解读信息');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

// 运行测试
testBaziReportAccess().catch(console.error); 