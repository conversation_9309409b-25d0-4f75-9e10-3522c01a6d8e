import React, { useState, useEffect } from 'react';
import { SimpleModal } from './ui/SimpleModal';

interface AreaSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: { province: string; city: string; district?: string }) => void;
}

// 简化的地区数据
const areaData = {
  '北京市': ['东城区', '西城区', '朝阳区', '丰台区', '石景山区', '海淀区', '门头沟区', '房山区', '通州区', '顺义区', '昌平区', '大兴区', '怀柔区', '平谷区', '密云区', '延庆区'],
  '上海市': ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '闵行区', '宝山区', '嘉定区', '浦东新区', '金山区', '松江区', '青浦区', '奉贤区', '崇明区'],
  '广东省': {
    '广州市': ['荔湾区', '越秀区', '海珠区', '天河区', '白云区', '黄埔区', '番禺区', '花都区', '南沙区', '从化区', '增城区'],
    '深圳市': ['罗湖区', '福田区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区', '光明区', '大鹏新区'],
    '珠海市': ['香洲区', '斗门区', '金湾区'],
    '汕头市': ['龙湖区', '金平区', '濠江区', '潮阳区', '潮南区', '澄海区', '南澳县'],
    '佛山市': ['禅城区', '南海区', '顺德区', '三水区', '高明区'],
    '韶关市': ['武江区', '浈江区', '曲江区', '始兴县', '仁化县', '翁源县', '乳源瑶族自治县', '新丰县', '乐昌市', '南雄市'],
    '湛江市': ['赤坎区', '霞山区', '坡头区', '麻章区', '遂溪县', '徐闻县', '廉江市', '雷州市', '吴川市'],
    '肇庆市': ['端州区', '鼎湖区', '高要区', '广宁县', '怀集县', '封开县', '德庆县', '四会市'],
    '江门市': ['蓬江区', '江海区', '新会区', '台山市', '开平市', '鹤山市', '恩平市'],
    '茂名市': ['茂南区', '电白区', '高州市', '化州市', '信宜市'],
    '惠州市': ['惠城区', '惠阳区', '博罗县', '惠东县', '龙门县'],
    '梅州市': ['梅江区', '梅县区', '大埔县', '丰顺县', '五华县', '平远县', '蕉岭县', '兴宁市'],
    '汕尾市': ['城区', '海丰县', '陆河县', '陆丰市'],
    '河源市': ['源城区', '紫金县', '龙川县', '连平县', '和平县', '东源县'],
    '阳江市': ['江城区', '阳东区', '阳西县', '阳春市'],
    '清远市': ['清城区', '清新区', '佛冈县', '阳山县', '连山壮族瑶族自治县', '连南瑶族自治县', '英德市', '连州市'],
    '东莞市': ['东莞市'],
    '中山市': ['中山市'],
    '潮州市': ['湘桥区', '潮安区', '饶平县'],
    '揭阳市': ['榕城区', '揭东区', '揭西县', '惠来县', '普宁市'],
    '云浮市': ['云城区', '云安区', '新兴县', '郁南县', '罗定市']
  },
  '江苏省': {
    '南京市': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区', '六合区', '溧水区', '高淳区'],
    '无锡市': ['锡山区', '惠山区', '滨湖区', '梁溪区', '新吴区', '江阴市', '宜兴市'],
    '徐州市': ['鼓楼区', '云龙区', '贾汪区', '泉山区', '铜山区', '丰县', '沛县', '睢宁县', '新沂市', '邳州市'],
    '常州市': ['天宁区', '钟楼区', '新北区', '武进区', '金坛区', '溧阳市'],
    '苏州市': ['虎丘区', '吴中区', '相城区', '姑苏区', '吴江区', '常熟市', '张家港市', '昆山市', '太仓市'],
    '南通市': ['崇川区', '港闸区', '通州区', '海安市', '如东县', '启东市', '如皋市', '海门市'],
    '连云港市': ['连云区', '海州区', '赣榆区', '东海县', '灌云县', '灌南县'],
    '淮安市': ['淮安区', '淮阴区', '清江浦区', '洪泽区', '涟水县', '盱眙县', '金湖县'],
    '盐城市': ['亭湖区', '盐都区', '大丰区', '响水县', '滨海县', '阜宁县', '射阳县', '建湖县', '东台市'],
    '扬州市': ['广陵区', '邗江区', '江都区', '宝应县', '仪征市', '高邮市'],
    '镇江市': ['京口区', '润州区', '丹徒区', '丹阳市', '扬中市', '句容市'],
    '泰州市': ['海陵区', '高港区', '姜堰区', '兴化市', '靖江市', '泰兴市'],
    '宿迁市': ['宿城区', '宿豫区', '沭阳县', '泗阳县', '泗洪县']
  }
};

export const AreaSelector: React.FC<AreaSelectorProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [selectedProvince, setSelectedProvince] = useState('');
  const [selectedCity, setSelectedCity] = useState('');
  const [selectedDistrict, setSelectedDistrict] = useState('');

  const provinces = Object.keys(areaData);
  
  const cities = selectedProvince && typeof areaData[selectedProvince as keyof typeof areaData] === 'object' 
    ? Object.keys(areaData[selectedProvince as keyof typeof areaData] as object)
    : [];
    
  const districts = selectedProvince && selectedCity
    ? (() => {
        const provinceData = areaData[selectedProvince as keyof typeof areaData];
        if (Array.isArray(provinceData)) {
          return provinceData; // 直辖市
        } else if (typeof provinceData === 'object' && selectedCity) {
          return (provinceData as any)[selectedCity] || [];
        }
        return [];
      })()
    : [];

  // 重置下级选择
  useEffect(() => {
    setSelectedCity('');
    setSelectedDistrict('');
  }, [selectedProvince]);

  useEffect(() => {
    setSelectedDistrict('');
  }, [selectedCity]);

  const handleConfirm = () => {
    if (!selectedProvince) {
      alert('请选择省份');
      return;
    }
    
    // 对于直辖市，city就是district
    if (Array.isArray(areaData[selectedProvince as keyof typeof areaData])) {
      if (!selectedCity) {
        alert('请选择区县');
        return;
      }
      onConfirm({
        province: selectedProvince,
        city: selectedProvince, // 直辖市的city就是省份名
        district: selectedCity // 实际选择的是区县
      });
    } else {
      if (!selectedCity) {
        alert('请选择城市');
        return;
      }
      if (!selectedDistrict) {
        alert('请选择区县');
        return;
      }
      onConfirm({
        province: selectedProvince,
        city: selectedCity,
        district: selectedDistrict
      });
    }
    onClose();
  };

  return (
    <SimpleModal isOpen={isOpen} onClose={onClose} title="选择地区">
      <div className="area-selector">
        <div className="area-selectors">
          <div className="selector-group">
            <label>省/直辖市</label>
            <select value={selectedProvince} onChange={(e) => setSelectedProvince(e.target.value)}>
              <option value="">请选择省/直辖市</option>
              {provinces.map(province => (
                <option key={province} value={province}>{province}</option>
              ))}
            </select>
          </div>

          {selectedProvince && (
            <div className="selector-group">
              <label>{Array.isArray(areaData[selectedProvince as keyof typeof areaData]) ? '区/县' : '市'}</label>
              <select value={selectedCity} onChange={(e) => setSelectedCity(e.target.value)}>
                <option value="">{Array.isArray(areaData[selectedProvince as keyof typeof areaData]) ? '请选择区/县' : '请选择市'}</option>
                {Array.isArray(areaData[selectedProvince as keyof typeof areaData]) 
                  ? (areaData[selectedProvince as keyof typeof areaData] as string[]).map(district => (
                      <option key={district} value={district}>{district}</option>
                    ))
                  : cities.map(city => (
                      <option key={city} value={city}>{city}</option>
                    ))
                }
              </select>
            </div>
          )}

          {selectedProvince && selectedCity && !Array.isArray(areaData[selectedProvince as keyof typeof areaData]) && (
            <div className="selector-group">
              <label>区/县</label>
              <select value={selectedDistrict} onChange={(e) => setSelectedDistrict(e.target.value)}>
                <option value="">请选择区/县</option>
                {districts.map(district => (
                  <option key={district} value={district}>{district}</option>
                ))}
              </select>
            </div>
          )}
        </div>

        <div className="area-actions">
          <button className="cancel-button" onClick={onClose}>取消</button>
          <button className="confirm-button" onClick={handleConfirm}>确认</button>
        </div>
      </div>
    </SimpleModal>
  );
};
