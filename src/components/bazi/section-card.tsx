import type { ReactNode } from 'react';
import type { LucideIcon } from 'lucide-react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { cn } from '../../lib/utils';
import '../../styles/BaziReportEnhanced.css';

interface SectionCardProps {
  title: string;
  icon?: LucideIcon;
  children: ReactNode;
  className?: string;
  contentClassName?: string;
}

export function SectionCard({ title, icon: Icon, children, className, contentClassName }: SectionCardProps) {
  return (
    <Card className={cn("enhanced-card bazi-section w-full shadow-lg text-card-foreground border-border/80 overflow-hidden", className)}>
      <CardHeader className="p-4 sm:p-6 pb-3 sm:pb-4 bg-gradient-to-r from-amber-50 via-orange-50 to-yellow-50 border-b border-amber-100/50">
        <CardTitle className="enhanced-title bazi-title text-lg sm:text-xl md:text-2xl font-semibold flex items-center">
          {Icon && (
            <div className="mr-2 sm:mr-3 p-2 bg-gradient-to-r from-amber-400 to-orange-400 rounded-lg shadow-sm">
              <Icon className="h-4 w-4 sm:h-5 sm:w-5 text-white shrink-0" />
            </div>
          )}
          <span className="bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
            {title}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className={cn("bazi-content p-4 sm:p-6 pt-4 text-sm sm:text-base leading-relaxed bg-gradient-to-br from-white to-amber-50/30", contentClassName)}>
        {children}
      </CardContent>
    </Card>
  );
}