import { User, Product, Order, Commission } from '../models/index.js';
import sequelize from '../config/database.js';

const cleanData = async () => {
  try {
    console.log('开始清理数据...');

    // 先删除所有佣金记录
    await Commission.destroy({
      where: {},
      force: true
    });
    console.log('佣金记录已清理');

    // 删除所有订单
    await Order.destroy({
      where: {},
      force: true
    });
    console.log('订单记录已清理');

    // 删除所有产品
    await Product.destroy({
      where: {},
      force: true
    });
    console.log('产品记录已清理');

    console.log('数据清理完成！');
    
  } catch (error) {
    console.error('数据清理失败:', error);
  } finally {
    process.exit();
  }
};

// 执行数据清理
console.log('3秒后开始清理数据...');
setTimeout(cleanData, 3000);
