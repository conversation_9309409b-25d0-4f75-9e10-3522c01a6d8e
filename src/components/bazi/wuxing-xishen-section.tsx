import type { MingZhuWuXingHeXiShenXinXi } from '../../types/bazi';
import { SectionCard } from './section-card';
import { InfoItem, ParagraphText } from './info-item';
import { Leaf, Circle } from 'lucide-react';

interface WuXingXiShenSectionProps {
  data: MingZhuWuXingHeXiShenXinXi;
}

export function WuXingXiShenSection({ data }: WuXingXiShenSectionProps) {
  return (
    <SectionCard title="命主五行和喜神信息" icon={Circle} contentClassName="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-primary mb-2 flex items-center">
          <Leaf className="mr-2 h-5 w-5 text-accent" />
          五行分数
        </h3>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-2 sm:gap-3 text-center">
          {data.五行分数.map((item) => (
            <div key={item.五行} className="p-3 border border-border/70 rounded-md bg-background/30 shadow-sm">
              <p className="font-medium text-sm sm:text-base">{item.五行}</p>
              <p className="text-xl sm:text-2xl font-bold text-accent">{item.分数}</p>
            </div>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="p-3 border border-border/70 rounded-md bg-background/30 shadow-sm">
          <h4 className="font-semibold text-primary mb-1 text-sm sm:text-base">
            同类 ({data.同类得分.同类五行})
          </h4>
          <p className="text-lg sm:text-xl">{data.同类得分.同类得分}</p>
        </div>
        <div className="p-3 border border-border/70 rounded-md bg-background/30 shadow-sm">
          <h4 className="font-semibold text-primary mb-1 text-sm sm:text-base">
            异类 ({data.异类得分.异类五行})
          </h4>
          <p className="text-lg sm:text-xl">{data.异类得分.异类得分}</p>
        </div>
      </div>
      
      <InfoItem 
        label="旺衰得分" 
        value={`${data.旺衰得分.得分} (${data.旺衰得分.评级})`} 
        className="p-3 border border-border/70 rounded-md bg-background/30 shadow-sm"
        labelClassName="text-sm sm:text-base"
        valueClassName="text-base sm:text-lg"
      />
      <InfoItem 
        label="喜用神" 
        value={<span className="bazi-highlight text-lg sm:text-xl">{data.喜用神}</span>} 
        className="p-4 border-2 border-accent/60 rounded-lg bg-gradient-to-r from-accent/15 to-accent/10 shadow-lg"
        labelClassName="text-sm sm:text-base text-primary font-semibold"
        valueClassName="text-lg sm:text-xl"
      />
      
      <div className="p-3 sm:p-4 border border-border/70 rounded-md bg-background/30 shadow-sm">
        <h3 className="text-lg font-semibold text-primary mb-2">
          八字喜神
        </h3>
        <ParagraphText text={data.八字喜神} />
      </div>

      <div className="p-3 sm:p-4 border border-border/70 rounded-md bg-background/30 shadow-sm">
        <h3 className="text-lg font-semibold text-primary mb-2">
          八字论命
        </h3>
        <ParagraphText text={data.八字论命} />
      </div>
    </SectionCard>
  );
} 