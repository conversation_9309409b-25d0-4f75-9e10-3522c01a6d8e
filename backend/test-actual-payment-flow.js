/**
 * 实际支付流程测试
 * 模拟真实的支付宝回调请求，验证八字产品跳转
 */

import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '.env') });

console.log('=== 实际支付流程测试 ===\n');

// 模拟支付宝回调数据
const mockAlipayCallback = {
    // 八字产品回调数据
    baziProduct: {
        out_trade_no: 'TEST_BAZI_' + Date.now(),
        trade_no: 'ALIPAY_' + Date.now(),
        trade_status: 'TRADE_SUCCESS',
        total_amount: '99.00',
        subject: '玄易八字命盘',
        body: '专业八字命理分析服务',
        extend_params: JSON.stringify({ productType: 'service' })
    },
    // 其他产品回调数据
    otherProduct: {
        out_trade_no: 'TEST_OTHER_' + Date.now(),
        trade_no: 'ALIPAY_' + Date.now(),
        trade_status: 'TRADE_SUCCESS',
        total_amount: '29.90',
        subject: '儿童纪录片合集',
        body: '高清儿童教育纪录片',
        extend_params: JSON.stringify({ productType: 'physical' })
    }
};

// 模拟支付回调处理逻辑
function simulatePaymentCallback(callbackData) {
    console.log(`处理支付回调:`);
    console.log(`订单号: ${callbackData.out_trade_no}`);
    console.log(`产品名称: ${callbackData.subject}`);
    console.log(`支付状态: ${callbackData.trade_status}`);
    
    if (callbackData.trade_status !== 'TRADE_SUCCESS') {
        console.log('❌ 支付未成功，不进行跳转');
        return null;
    }
    
    // 解析扩展参数
    let extendParams = {};
    try {
        extendParams = JSON.parse(callbackData.extend_params || '{}');
    } catch (e) {
        console.log('⚠️  扩展参数解析失败');
    }
    
    const productTitle = callbackData.subject;
    const productType = extendParams.productType || 'physical';
    
    console.log(`产品类型: ${productType}`);
    
    let redirectUrl;
    
    // 跳转逻辑（与 payment.js 保持一致）
    if (productType === 'service' && (productTitle.includes('八字') || productTitle.includes('命理'))) {
        // 八字服务跳转到报告页面
        redirectUrl = `${process.env.FRONTEND_URL}/bazi-report?orderId=${callbackData.out_trade_no}`;
        console.log(`✅ 八字产品 - 跳转到报告页面`);
    } else if (extendParams.course && extendParams.course.includes('deepseek')) {
        // Deepseek AI课程跳转
        redirectUrl = process.env.PAID_RETURN_URL2;
        console.log(`📚 Deepseek课程 - 跳转到课程页面`);
    } else {
        // 其他产品跳转到默认页面
        redirectUrl = process.env.PAID_RETURN_URL;
        console.log(`📦 其他产品 - 跳转到默认页面`);
    }
    
    console.log(`跳转URL: ${redirectUrl}`);
    
    return {
        orderId: callbackData.out_trade_no,
        productTitle,
        productType,
        redirectUrl,
        isCorrect: redirectUrl && redirectUrl.includes('/bazi-report')
    };
}

// 测试八字产品支付流程
console.log('1. 测试八字产品支付流程:');
const baziResult = simulatePaymentCallback(mockAlipayCallback.baziProduct);
console.log();

// 测试其他产品支付流程
console.log('2. 测试其他产品支付流程:');
const otherResult = simulatePaymentCallback(mockAlipayCallback.otherProduct);
console.log();

// 验证结果
console.log('3. 测试结果验证:');
if (baziResult && baziResult.isCorrect) {
    console.log('✅ 八字产品支付跳转测试通过');
    console.log(`   订单 ${baziResult.orderId} 正确跳转到报告页面`);
} else {
    console.log('❌ 八字产品支付跳转测试失败');
    if (baziResult) {
        console.log(`   订单 ${baziResult.orderId} 跳转到: ${baziResult.redirectUrl}`);
    }
}

if (otherResult && !otherResult.isCorrect) {
    console.log('✅ 其他产品支付跳转正常（非八字报告页面）');
} else {
    console.log('⚠️  其他产品支付跳转异常');
}

// 生成测试URL供手动验证
console.log('\n4. 手动验证链接:');
if (baziResult && baziResult.redirectUrl) {
    console.log(`八字报告页面: ${baziResult.redirectUrl}`);
    console.log('请在浏览器中打开上述链接验证页面是否正常显示');
}

// 检查前端服务状态
console.log('\n5. 前端服务状态检查:');
console.log(`前端地址: ${process.env.FRONTEND_URL}`);
console.log('请确保前端服务正在运行在 http://localhost:3002/');

console.log('\n=== 实际支付流程测试完成 ===');

// 输出最终结论
if (baziResult && baziResult.isCorrect) {
    console.log('\n🎉 结论: 本地环境八字产品支付后可以正确跳转到八字报告页面！');
    console.log('不会跳转到百度网盘地址。');
} else {
    console.log('\n❌ 结论: 本地环境八字产品支付跳转存在问题，需要进一步检查。');
}