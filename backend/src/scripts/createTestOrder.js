import { Order, User, Product } from '../models/index.js';
import { generateOrderNumber } from '../utils/orderUtils.js';
import { calculateCommissions } from '../utils/commissionUtils.js';

const createTestOrder = async () => {
  try {
    console.log('开始创建测试订单...\n');

    // 查找一级代理
    const agent = await User.findOne({
      where: { level: 1 }
    });

    if (!agent) {
      throw new Error('找不到一级代理');
    }

    // 查找产品
    const product = await Product.findOne();
    if (!product) {
      throw new Error('找不到产品');
    }

    // 计算佣金
    const commissions = calculateCommissions(
      product.price,
      agent.commissionRate * 100,  // 转换为百分比
      null  // 没有二级代理
    );

    // 创建订单
    const order = await Order.create({
      id: generateOrderNumber(),
      productId: product.id,
      amount: product.price,
      agentId: agent.id,
      status: 'paid',
      customerName: '测试客户',
      customerPhone: '13800000000',
      agentCommission: commissions.level1Commission,
      adminCommission: commissions.adminCommission,
      paidAt: new Date()
    });

    console.log('测试订单创建成功:', {
      orderId: order.id,
      amount: order.amount,
      adminCommission: order.adminCommission,
      agentCommission: order.agentCommission
    });

  } catch (error) {
    console.error('创建测试订单失败:', error);
  } finally {
    process.exit();
  }
};

// 执行创建
console.log('开始创建测试订单...');
createTestOrder();
