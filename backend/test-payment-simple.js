import { createPaymentUrl } from './src/services/alipay.js';

console.log('测试支付链接生成...');

try {
  const result = await createPaymentUrl({
    orderId: 'TEST' + Date.now(),
    amount: 99,
    productName: '测试八字报告',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    agentId: null,
    isDeepseekCourse: false,
    isBaziService: true,
    isNamingService: false
  });
  
  console.log('支付链接生成成功:');
  console.log('URL长度:', result.length);
  console.log('URL前100字符:', result.substring(0, 100));
  
  // 检查URL是否包含必要的参数
  if (result.includes('alipay.trade.page.pay')) {
    console.log('✅ 包含正确的支付方法');
  } else {
    console.log('❌ 缺少支付方法');
  }
  
  if (result.includes('out_trade_no')) {
    console.log('✅ 包含订单号');
  } else {
    console.log('❌ 缺少订单号');
  }
  
} catch (error) {
  console.error('支付链接生成失败:', error.message);
  console.error('错误详情:', error);
}
