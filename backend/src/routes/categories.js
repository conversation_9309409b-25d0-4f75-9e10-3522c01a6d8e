import express from 'express';
import { auth, adminAuth } from '../middleware/auth.js';

const router = express.Router();

// 获取所有分类 - 返回空数组，因为我们不使用分类功能
router.get('/', auth, async (req, res) => {
  try {
    console.log('分类API - 返回空数组');
    // 返回空数组，表示没有分类
    res.json([]);
  } catch (error) {
    console.error('获取分类列表失败:', error);
    res.status(500).json({ 
      message: '获取分类列表失败',
      error: error.message 
    });
  }
});

// 管理员创建分类
router.post('/', adminAuth, async (req, res) => {
  res.status(200).json({ message: '分类功能已禁用' });
});

// 管理员更新分类
router.put('/:id', adminAuth, async (req, res) => {
  res.status(200).json({ message: '分类功能已禁用' });
});

// 管理员删除分类
router.delete('/:id', adminAuth, async (req, res) => {
  res.status(200).json({ message: '分类功能已禁用' });
});

export default router;
