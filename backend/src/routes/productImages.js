import express from 'express';
import { Op } from 'sequelize';
import { auth, adminAuth } from '../middleware/auth.js';
import { ProductImage } from '../models/index.js';

const router = express.Router();

// 获取产品的所有图片
router.get('/product/:productId', auth, async (req, res) => {
  try {
    const { productId } = req.params;
    
    // 验证参数
    if (!productId || isNaN(Number(productId))) {
      return res.status(400).json({ message: '无效的产品ID' });
    }
    
    // 查询产品图片
    const images = await ProductImage.findAll({
      where: { productId },
      order: [['isPrimary', 'DESC'], ['order', 'ASC']]
    });
    
    console.log(`获取到产品ID ${productId} 的 ${images.length} 张图片`);
    
    res.json(images);
  } catch (error) {
    console.error('获取产品图片失败:', error);
    res.status(500).json({ 
      message: '获取产品图片失败',
      error: error.message 
    });
  }
});

// 添加产品图片 - 仅管理员
router.post('/', adminAuth, async (req, res) => {
  try {
    const { productId, url, isPrimary = false, order = 0 } = req.body;
    
    // 验证必填字段
    if (!productId || !url) {
      return res.status(400).json({ message: '产品ID和图片URL为必填项' });
    }
    
    // 如果设置为主图，先将该产品的其他图片设为非主图
    if (isPrimary) {
      await ProductImage.update(
        { isPrimary: false },
        { where: { productId } }
      );
    }
    
    // 创建产品图片
    const productImage = await ProductImage.create({
      productId,
      url,
      isPrimary,
      order
    });
    
    console.log('产品图片添加成功:', {
      id: productImage.id,
      productId: productImage.productId,
      url: productImage.url
    });
    
    res.status(201).json(productImage);
  } catch (error) {
    console.error('添加产品图片失败:', error);
    res.status(400).json({ 
      message: '添加产品图片失败',
      error: error.message 
    });
  }
});

// 更新产品图片 - 仅管理员
router.put('/:id', adminAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { url, isPrimary, order } = req.body;
    
    // 查找图片
    const productImage = await ProductImage.findByPk(id);
    if (!productImage) {
      return res.status(404).json({ message: '图片不存在' });
    }
    
    // 如果设置为主图，先将该产品的其他图片设为非主图
    if (isPrimary) {
      await ProductImage.update(
        { isPrimary: false },
        { 
          where: { 
            productId: productImage.productId,
            id: { [Op.ne]: id }
          } 
        }
      );
    }
    
    // 更新图片
    await productImage.update({
      url: url || productImage.url,
      isPrimary: isPrimary !== undefined ? isPrimary : productImage.isPrimary,
      order: order !== undefined ? order : productImage.order
    });
    
    console.log('产品图片更新成功:', {
      id: productImage.id,
      url: productImage.url
    });
    
    res.json(productImage);
  } catch (error) {
    console.error('更新产品图片失败:', error);
    res.status(400).json({ 
      message: '更新产品图片失败',
      error: error.message 
    });
  }
});

// 删除产品图片 - 仅管理员
router.delete('/:id', adminAuth, async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找图片
    const productImage = await ProductImage.findByPk(id);
    if (!productImage) {
      return res.status(404).json({ message: '图片不存在' });
    }
    
    // 删除图片
    await productImage.destroy();
    
    console.log('产品图片删除成功:', { id });
    
    res.json({ message: '产品图片删除成功' });
  } catch (error) {
    console.error('删除产品图片失败:', error);
    res.status(400).json({ 
      message: '删除产品图片失败',
      error: error.message 
    });
  }
});

export default router;
