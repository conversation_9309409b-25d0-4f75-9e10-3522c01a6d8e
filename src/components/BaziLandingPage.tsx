import React, { useState, useEffect } from 'react';
import { Button, Card, Row, Col, Typography, Statistic, Timeline, Avatar } from 'antd';
import { 
  StarOutlined, 
  SafetyOutlined, 
  ThunderboltOutlined, 
  TrophyOutlined,
  UserOutlined,
  CheckCircleOutlined,
  GiftOutlined
} from '@ant-design/icons';

const { Title, Paragraph } = Typography;

interface BaziLandingPageProps {
  onStartQuery: () => void;
  productInfo?: {
    name: string;
    price: number;
    description: string;
  };
}

const BaziLandingPage: React.FC<BaziLandingPageProps> = ({ onStartQuery, productInfo }) => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      name: "张女士",
      age: "32岁",
      location: "北京",
      content: "八字分析非常准确，特别是关于事业发展的建议，让我在职场上更有方向感。",
      rating: 5,
      avatar: "👩‍💼"
    },
    {
      name: "李先生", 
      age: "28岁",
      location: "上海",
      content: "婚姻运势分析很到位，帮助我理解了感情中的问题，现在关系更和谐了。",
      rating: 5,
      avatar: "👨‍💻"
    },
    {
      name: "王女士",
      age: "35岁", 
      location: "深圳",
      content: "财运分析让我重新规划了投资方向，收益明显提升，非常感谢！",
      rating: 5,
      avatar: "👩‍🎨"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 4000);
    return () => clearInterval(timer);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        opacity: 0.3
      }} />

      <div style={{ position: 'relative', zIndex: 1, padding: '2rem 1rem' }}>
        {/* 头部区域 */}
        <div style={{ textAlign: 'center', marginBottom: '4rem', color: 'white' }}>
          <div style={{
            fontSize: '3.5rem',
            fontWeight: 'bold',
            marginBottom: '1rem',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)',
            background: 'linear-gradient(45deg, #FFD700, #FFA500)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            ✨ 八字命理大师 ✨
          </div>
          <div style={{ fontSize: '1.5rem', marginBottom: '1rem', opacity: 0.9 }}>
            千年传承 · 精准解析 · 洞悉命运
          </div>
          <div style={{ fontSize: '1.1rem', opacity: 0.8 }}>
            已为 <span style={{ color: '#FFD700', fontWeight: 'bold' }}>100,000+</span> 用户提供专业命理服务
          </div>
        </div>

        {/* 核心价值主张 */}
        <div style={{ maxWidth: '1200px', margin: '0 auto', marginBottom: '4rem' }}>
          <Row gutter={[32, 32]}>
            <Col xs={24} md={8}>
              <Card style={{
                textAlign: 'center',
                borderRadius: '20px',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(10px)',
                border: 'none',
                height: '100%'
              }}>
                <ThunderboltOutlined style={{ fontSize: '3rem', color: '#667eea', marginBottom: '1rem' }} />
                <Title level={4}>即时生成</Title>
                <Paragraph>
                  AI智能算法结合传统命理学，3秒内生成专业八字分析报告
                </Paragraph>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card style={{
                textAlign: 'center',
                borderRadius: '20px',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(10px)',
                border: 'none',
                height: '100%'
              }}>
                <TrophyOutlined style={{ fontSize: '3rem', color: '#667eea', marginBottom: '1rem' }} />
                <Title level={4}>专业权威</Title>
                <Paragraph>
                  基于《易经》《子平真诠》等经典典籍，传承千年命理智慧
                </Paragraph>
              </Card>
            </Col>
            <Col xs={24} md={8}>
              <Card style={{
                textAlign: 'center',
                borderRadius: '20px',
                background: 'rgba(255,255,255,0.95)',
                backdropFilter: 'blur(10px)',
                border: 'none',
                height: '100%'
              }}>
                <SafetyOutlined style={{ fontSize: '3rem', color: '#667eea', marginBottom: '1rem' }} />
                <Title level={4}>隐私保护</Title>
                <Paragraph>
                  严格保护用户隐私，所有信息仅用于生成个人命理报告
                </Paragraph>
              </Card>
            </Col>
          </Row>
        </div>

        {/* 产品信息 */}
        {productInfo && (
          <div style={{ maxWidth: '800px', margin: '0 auto', marginBottom: '4rem' }}>
            <Card style={{
              borderRadius: '20px',
              background: 'rgba(255,255,255,0.95)',
              backdropFilter: 'blur(10px)',
              border: 'none',
              textAlign: 'center'
            }}>
              <div style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                padding: '2rem',
                borderRadius: '15px',
                marginBottom: '2rem'
              }}>
                <Title level={2} style={{ color: 'white', marginBottom: '1rem' }}>
                  {productInfo.name}
                </Title>
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  gap: '2rem',
                  flexWrap: 'wrap',
                  marginBottom: '1rem'
                }}>
                  <div style={{
                    background: 'rgba(255,255,255,0.2)',
                    padding: '1rem 2rem',
                    borderRadius: '25px',
                    fontSize: '1.5rem',
                    fontWeight: 'bold'
                  }}>
                    ¥{productInfo.price}
                  </div>
                  <div style={{
                    background: 'rgba(255,215,0,0.2)',
                    padding: '1rem 2rem',
                    borderRadius: '25px',
                    fontSize: '1rem',
                    border: '1px solid rgba(255,215,0,0.3)'
                  }}>
                    🔥 限时优惠价
                  </div>
                </div>
                <Paragraph style={{ color: 'rgba(255,255,255,0.9)', fontSize: '1.1rem', margin: 0 }}>
                  {productInfo.description}
                </Paragraph>
              </div>

              <Button
                type="primary"
                size="large"
                onClick={onStartQuery}
                style={{
                  height: '60px',
                  fontSize: '20px',
                  fontWeight: 'bold',
                  borderRadius: '15px',
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  border: 'none',
                  boxShadow: '0 8px 20px rgba(102, 126, 234, 0.3)',
                  padding: '0 3rem'
                }}
              >
                <GiftOutlined style={{ marginRight: '8px' }} />
                立即开始八字查询
              </Button>
            </Card>
          </div>
        )}

        {/* 用户评价轮播 */}
        <div style={{ maxWidth: '600px', margin: '0 auto', marginBottom: '4rem' }}>
          <Card style={{
            borderRadius: '20px',
            background: 'rgba(255,255,255,0.95)',
            backdropFilter: 'blur(10px)',
            border: 'none',
            textAlign: 'center'
          }}>
            <Title level={3} style={{ marginBottom: '2rem' }}>用户真实反馈</Title>
            <div style={{ minHeight: '150px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <div>
                <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>
                  {testimonials[currentTestimonial].avatar}
                </div>
                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>
                  {'⭐'.repeat(testimonials[currentTestimonial].rating)}
                </div>
                <Paragraph style={{ fontSize: '1.1rem', fontStyle: 'italic', marginBottom: '1rem' }}>
                  "{testimonials[currentTestimonial].content}"
                </Paragraph>
                <div style={{ color: '#666' }}>
                  <strong>{testimonials[currentTestimonial].name}</strong> · 
                  {testimonials[currentTestimonial].age} · 
                  {testimonials[currentTestimonial].location}
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* 底部CTA */}
        <div style={{ textAlign: 'center', color: 'white' }}>
          <Title level={2} style={{ color: 'white', marginBottom: '1rem' }}>
            开启您的命运探索之旅
          </Title>
          <Paragraph style={{ fontSize: '1.2rem', color: 'rgba(255,255,255,0.9)', marginBottom: '2rem' }}>
            千年智慧，现代科技，为您解读人生密码
          </Paragraph>
          <Button
            type="primary"
            size="large"
            onClick={onStartQuery}
            style={{
              height: '60px',
              fontSize: '18px',
              fontWeight: 'bold',
              borderRadius: '15px',
              background: 'rgba(255,255,255,0.2)',
              border: '2px solid rgba(255,255,255,0.3)',
              color: 'white',
              padding: '0 3rem'
            }}
          >
            立即开始查询 →
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BaziLandingPage;
