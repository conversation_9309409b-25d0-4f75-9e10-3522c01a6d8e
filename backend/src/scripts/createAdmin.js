import { User, Product, Order, Commission, Withdrawal } from '../models/index.js';
import bcrypt from 'bcryptjs';
import sequelize from '../config/database.js';

const createAdmin = async () => {
  try {
    // 强制同步数据库（这将创建所有表）
    console.log('开始同步数据库...');
    await sequelize.sync({ force: true });
    console.log('数据库同步完成');

    // 创建管理员账号
    const adminUser = await User.create({
      username: 'admin',
      password: 'admin123',  // 密码会通过 setter 自动加密
      name: '系统管理员',
      phone: '13800000000',
      role: 'admin',
      status: 'approved',
      level: 0  // 管理员 level 为 0
    });

    console.log('管理员账号创建成功:', {
      id: adminUser.id,
      username: adminUser.username,
      name: adminUser.name,
      role: adminUser.role,
      status: adminUser.status
    });

    // 验证创建的账号
    const verifyAdmin = await User.findOne({ 
      where: { username: 'admin' },
      raw: true 
    });
    console.log('验证创建的管理员账号:', verifyAdmin);

    // 测试密码验证
    const testAdmin = await User.findOne({ where: { username: 'admin' } });
    const isPasswordValid = testAdmin.comparePassword('admin123');
    console.log('密码验证测试:', isPasswordValid ? '成功' : '��败');

  } catch (error) {
    console.error('创建管理员账号失败:', error);
  } finally {
    await sequelize.close();
    process.exit();
  }
};

createAdmin(); 