/**
 * 模拟支付成功流程
 * 用于测试八字产品的完整流程
 */

import { Order, BaziOrder } from './src/models/index.js';
import sequelize from './src/config/database.js';
import baziService from './src/services/baziService.js';

async function simulatePaymentSuccess() {
  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    const orderId = '250708172851162124';
    
    // 1. 查找订单
    console.log('\n1️⃣ 查找订单...');
    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder'
      }]
    });
    
    if (!order) {
      console.log('❌ 订单不存在');
      return;
    }
    
    console.log(`订单状态: ${order.status}`);
    console.log(`八字订单: ${order.baziOrder ? '存在' : '不存在'}`);
    
    // 2. 模拟支付成功
    console.log('\n2️⃣ 模拟支付成功...');
    await order.update({
      status: 'paid',
      paidAt: new Date()
    });
    console.log('✅ 订单状态已更新为已支付');
    
    // 3. 生成八字报告
    console.log('\n3️⃣ 生成八字报告...');

    try {
      const result = await baziService.generateBaziReportForOrder(orderId);
      console.log('✅ 八字报告生成成功');
      console.log('报告数据长度:', JSON.stringify(result).length, '字符');
    } catch (error) {
      console.log('❌ 八字报告生成失败:', error.message);
      
      // 如果API调用失败，创建一个模拟报告用于测试
      console.log('\n🔧 创建模拟报告用于测试...');
      const mockReportData = {
        "命主信息": {
          "姓名": "测试",
          "性别": "男",
          "公历生日": "2025年07月01日 06时05分",
          "农历生日": "乙巳年 五月 初六 卯时",
          "出生地": "河北省 保定市"
        },
        "命主八字命盘信息": {
          "年柱": "乙巳",
          "月柱": "壬午", 
          "日柱": "戊申",
          "时柱": "乙卯",
          "日主": "戊土",
          "格局": "正财格"
        },
        "五行分析": {
          "木": 2,
          "火": 2,
          "土": 1,
          "金": 1,
          "水": 1
        },
        "命理解析": {
          "性格特点": "戊土日主，性格稳重踏实，有责任心，做事认真负责。",
          "事业运势": "正财格局，适合从事稳定的工作，财运较好。",
          "感情运势": "感情方面较为稳定，容易遇到合适的伴侣。",
          "健康运势": "身体健康状况良好，注意脾胃保养。"
        }
      };
      
      await order.baziOrder.update({
        reportData: mockReportData,
        reportStatus: 'generated',
        errorMessage: null
      });
      
      console.log('✅ 模拟报告创建成功');
    }
    
    // 4. 验证结果
    console.log('\n4️⃣ 验证结果...');
    const updatedOrder = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder'
      }]
    });
    
    console.log(`订单状态: ${updatedOrder.status}`);
    console.log(`报告状态: ${updatedOrder.baziOrder.reportStatus}`);
    console.log(`报告数据: ${updatedOrder.baziOrder.reportData ? '有数据' : '无数据'}`);
    
    if (updatedOrder.baziOrder.reportData) {
      console.log('报告预览:', JSON.stringify(updatedOrder.baziOrder.reportData, null, 2).substring(0, 200) + '...');
    }
    
    console.log('\n🎉 模拟支付成功流程完成！');
    console.log('现在可以访问八字报告页面查看结果：');
    console.log(`http://localhost:3000/bazi-report?orderId=${orderId}`);
    
  } catch (error) {
    console.error('❌ 模拟支付失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行模拟
simulatePaymentSuccess().catch(console.error);
