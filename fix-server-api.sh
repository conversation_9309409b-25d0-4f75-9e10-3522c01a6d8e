#!/bin/bash

# 服务器API修复脚本
# 用于解决登录API 404错误问题

echo "🔧 开始修复服务器API问题..."
echo "======================================"

# 服务器信息
SERVER_IP="************"
SERVER_USER="root"
APP_NAME="commission-platform-api"
APP_PATH="/root/commission-platform-api"
APP_ENTRY="src/index.js"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 执行SSH命令的函数
execute_ssh() {
    local command="$1"
    local description="$2"
    
    print_status "$description"
    echo "执行命令: $command"
    
    # 这里需要手动执行，因为需要SSH密码
    echo "请手动执行以下命令:"
    echo "ssh $SERVER_USER@$SERVER_IP '$command'"
    echo ""
}

# 1. 检查当前PM2状态
print_status "步骤1: 检查PM2进程状态"
execute_ssh "pm2 status" "查看当前PM2进程"

# 2. 停止现有服务
print_status "步骤2: 停止现有服务"
execute_ssh "pm2 delete $APP_NAME || echo '服务不存在，跳过删除'" "删除现有PM2进程"

# 3. 检查端口占用
print_status "步骤3: 检查端口占用情况"
execute_ssh "netstat -tlnp | grep :3005 || echo '端口3005未被占用'" "检查3005端口"
execute_ssh "lsof -ti:3005 | xargs kill -9 2>/dev/null || echo '没有进程占用3005端口'" "清理端口占用"

# 4. 检查应用目录
print_status "步骤4: 检查应用目录和文件"
execute_ssh "ls -la $APP_PATH" "检查应用目录"
execute_ssh "ls -la $APP_PATH/$APP_ENTRY" "检查入口文件"

# 5. 检查环境配置
print_status "步骤5: 检查环境配置"
execute_ssh "cd $APP_PATH && ls -la .env*" "检查环境配置文件"

# 6. 启动新服务
print_status "步骤6: 启动后端服务"
execute_ssh "cd $APP_PATH && NODE_ENV=production pm2 start $APP_ENTRY --name $APP_NAME" "启动PM2服务"

# 7. 保存PM2配置
print_status "步骤7: 保存PM2配置"
execute_ssh "pm2 save" "保存PM2配置"

# 8. 检查服务状态
print_status "步骤8: 验证服务启动"
execute_ssh "pm2 status" "检查PM2状态"
execute_ssh "pm2 logs $APP_NAME --lines 20" "查看服务日志"

# 9. 检查端口监听
print_status "步骤9: 验证端口监听"
execute_ssh "netstat -tlnp | grep :3005" "确认3005端口监听"

# 10. 测试本地API
print_status "步骤10: 测试本地API连接"
execute_ssh "curl -I http://localhost:3005/api/auth/login" "测试本地API"

# 11. 重启Nginx
print_status "步骤11: 重启Nginx服务"
execute_ssh "nginx -t && systemctl reload nginx" "重新加载Nginx配置"

# 12. 最终验证
print_status "步骤12: 最终API验证"
echo "请在本地执行以下命令验证API:"
echo "curl -I https://ye.bzcy.xyz/api/auth/login"
echo ""

print_success "修复脚本准备完成！"
print_warning "注意: 由于需要SSH密码验证，请手动执行上述命令。"
print_status "建议按顺序执行每个步骤，并检查每步的输出结果。"

echo ""
echo "======================================"
echo "🚀 快速修复命令（复制粘贴执行）:"
echo "======================================"
echo "ssh root@************ << 'EOF'"
echo "pm2 delete commission-platform-api 2>/dev/null || true"
echo "lsof -ti:3005 | xargs kill -9 2>/dev/null || true"
echo "cd /root/commission-platform-api"
echo "NODE_ENV=production pm2 start src/index.js --name commission-platform-api"
echo "pm2 save"
echo "pm2 status"
echo "netstat -tlnp | grep :3005"
echo "nginx -t && systemctl reload nginx"
echo "curl -I http://localhost:3005/api/auth/login"
echo "EOF"