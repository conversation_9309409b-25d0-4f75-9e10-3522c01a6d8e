import React from 'react';
import { <PERSON>rk<PERSON>, <PERSON>, Crown } from 'lucide-react';
import { cn } from '../../lib/utils';
import '../../styles/BaziReportEnhanced.css';

interface EnhancedLoadingProps {
  title?: string;
  subtitle?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function EnhancedLoading({ 
  title = "正在生成您的八字报告", 
  subtitle = "请稍候，我们正在为您分析命理信息...",
  className,
  size = 'md'
}: EnhancedLoadingProps) {
  const sizeClasses = {
    sm: {
      container: "p-6",
      icon: "h-8 w-8",
      title: "text-lg",
      subtitle: "text-sm"
    },
    md: {
      container: "p-8",
      icon: "h-12 w-12",
      title: "text-xl",
      subtitle: "text-base"
    },
    lg: {
      container: "p-12",
      icon: "h-16 w-16",
      title: "text-2xl",
      subtitle: "text-lg"
    }
  };

  const currentSize = sizeClasses[size];

  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 flex items-center justify-center",
      className
    )}>
      <div className={cn(
        "enhanced-card max-w-md w-full mx-4 text-center space-y-6",
        currentSize.container
      )}>
        {/* 主要加载图标 */}
        <div className="relative">
          <div className="relative bg-white rounded-full p-6 shadow-xl mx-auto w-fit">
            <Sparkles className={cn(
              "text-amber-500 animate-spin mx-auto",
              currentSize.icon
            )} />
          </div>
          
          {/* 环绕的星星 */}
          <div className="absolute inset-0 animate-pulse">
            <Star className="h-4 w-4 text-orange-400 absolute top-2 left-1/4 animate-bounce" style={{ animationDelay: '0s' }} />
            <Star className="h-3 w-3 text-yellow-400 absolute top-1/4 right-2 animate-bounce" style={{ animationDelay: '0.5s' }} />
            <Star className="h-4 w-4 text-amber-400 absolute bottom-2 right-1/4 animate-bounce" style={{ animationDelay: '1s' }} />
            <Star className="h-3 w-3 text-orange-300 absolute bottom-1/4 left-2 animate-bounce" style={{ animationDelay: '1.5s' }} />
          </div>
        </div>

        {/* 标题 */}
        <div className="space-y-3">
          <h2 className={cn(
            "font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent",
            currentSize.title
          )}>
            {title}
          </h2>
          
          <p className={cn(
            "text-amber-700 leading-relaxed",
            currentSize.subtitle
          )}>
            {subtitle}
          </p>
        </div>

        {/* 进度条 */}
        <div className="space-y-2">
          <div className="w-full bg-amber-100 rounded-full h-2 overflow-hidden">
            <div className="h-full bg-gradient-to-r from-amber-400 to-orange-400 rounded-full animate-pulse"></div>
          </div>
          <p className="text-xs text-amber-600">
            正在分析您的生辰八字...
          </p>
        </div>

        {/* 装饰性元素 */}
        <div className="flex justify-center space-x-2 opacity-60">
          <div className="w-2 h-2 bg-amber-400 rounded-full animate-bounce" style={{ animationDelay: '0s' }}></div>
          <div className="w-2 h-2 bg-orange-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
          <div className="w-2 h-2 bg-yellow-400 rounded-full animate-bounce" style={{ animationDelay: '0.4s' }}></div>
        </div>
      </div>
    </div>
  );
}

// 简化版加载组件
export function SimpleLoading({ className }: { className?: string }) {
  return (
    <div className={cn("flex items-center justify-center p-4", className)}>
      <div className="relative">
        <Sparkles className="h-8 w-8 text-amber-500 animate-spin" />
        <div className="absolute inset-0 animate-ping">
          <div className="w-8 h-8 border-2 border-amber-300 rounded-full opacity-20"></div>
        </div>
      </div>
    </div>
  );
}

// 内联加载组件
export function InlineLoading({ text = "加载中..." }: { text?: string }) {
  return (
    <div className="flex items-center space-x-2 text-amber-600">
      <div className="w-4 h-4 border-2 border-amber-400 border-t-transparent rounded-full animate-spin"></div>
      <span className="text-sm font-medium">{text}</span>
    </div>
  );
}
