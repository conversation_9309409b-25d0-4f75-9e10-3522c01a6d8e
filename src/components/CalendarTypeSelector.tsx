import React from 'react';
import { SimpleModal } from './ui/SimpleModal';

interface CalendarTypeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (type: 'solar' | 'lunar') => void;
}

export const CalendarTypeSelector: React.FC<CalendarTypeSelectorProps> = ({
  isOpen,
  onClose,
  onSelect,
}) => {
  const handleSelect = (type: 'solar' | 'lunar') => {
    onSelect(type);
    onClose();
  };

  return (
    <SimpleModal isOpen={isOpen} onClose={onClose} title="选择生日类型">
      <div className="calendar-type-selector">
        <button 
          className="calendar-type-button solar"
          onClick={() => handleSelect('solar')}
        >
          <div className="button-title">使用公历生日</div>
          <div className="button-desc">点击选择此选项将使用公历生日进行测算</div>
        </button>
        
        <button 
          className="calendar-type-button lunar"
          onClick={() => handleSelect('lunar')}
        >
          <div className="button-title">使用农历生日</div>
          <div className="button-desc">点击选择此选项将使用农历生日进行测算</div>
        </button>
        
        <button className="cancel-button" onClick={onClose}>
          取消
        </button>
      </div>
    </SimpleModal>
  );
};
