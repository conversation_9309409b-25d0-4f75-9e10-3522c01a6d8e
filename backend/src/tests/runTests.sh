#!/bin/bash

# 测试运行脚本
# 用于运行所有测试用例

echo "===== 开始运行测试 ====="

# 确保服务器正在运行
echo "检查服务器状态..."
if ! curl -s http://localhost:3005/api/health > /dev/null; then
  echo "服务器未运行，正在启动..."
  cd ..
  npm run dev &
  SERVER_PID=$!
  echo "服务器进程ID: $SERVER_PID"
  
  # 等待服务器启动
  echo "等待服务器启动..."
  sleep 5
else
  echo "服务器已运行"
fi

# 安装测试依赖
echo "检查测试依赖..."
if ! npm list | grep -q "node-fetch"; then
  echo "安装 node-fetch..."
  npm install node-fetch@2 --save-dev
fi

# 运行 Deepseek 课程支付跳转测试
echo -e "\n===== 运行 Deepseek 课程支付跳转测试 ====="
node src/tests/testDeepseekRedirect.js

# 如果是我们启动的服务器，则关闭它
if [ ! -z "$SERVER_PID" ]; then
  echo "关闭服务器进程..."
  kill $SERVER_PID
fi

echo -e "\n===== 测试完成 ====="
