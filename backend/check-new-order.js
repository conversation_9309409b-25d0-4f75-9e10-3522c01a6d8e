import { Order, BaziOrder } from './src/models/index.js';
import sequelize from './src/config/database.js';

async function checkNewOrder() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    const orderId = '250711145529834645';
    console.log(`查找订单: ${orderId}`);
    
    // 查找订单
    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder'
      }]
    });
    
    if (!order) {
      console.log('❌ 订单不存在');
      return;
    }
    
    console.log('📋 订单信息:');
    console.log('  ID:', order.id);
    console.log('  状态:', order.status);
    console.log('  客户姓名:', order.customerName);
    console.log('  支付时间:', order.paidAt);
    console.log('  创建时间:', order.createdAt);
    
    if (order.baziOrder) {
      console.log('\n🔮 八字订单信息:');
      console.log('  姓名:', order.baziOrder.name);
      console.log('  性别:', order.baziOrder.gender);
      console.log('  出生年:', order.baziOrder.birthYear);
      console.log('  出生月:', order.baziOrder.birthMonth);
      console.log('  出生日:', order.baziOrder.birthDay);
      console.log('  出生时:', order.baziOrder.birthHour);
      console.log('  出生分:', order.baziOrder.birthMinute);
      console.log('  出生省:', order.baziOrder.birthProvince);
      console.log('  出生市:', order.baziOrder.birthCity);
      console.log('  报告状态:', order.baziOrder.reportStatus);
      console.log('  错误信息:', order.baziOrder.errorMessage);
      
      if (order.baziOrder.reportData) {
        console.log('\n✅ 报告数据已生成');
        const chengguInfo = order.baziOrder.reportData['命主八字称骨信息'];
        if (chengguInfo && chengguInfo.称骨重量) {
          console.log('  称骨重量:', chengguInfo.称骨重量);
          console.log('  称骨歌诀:', chengguInfo.称骨重量_歌诀 ? '已生成' : '未生成');
        } else {
          console.log('  称骨信息: 未生成');
        }
      } else {
        console.log('\n❌ 报告数据未生成');
      }
    } else {
      console.log('\n❌ 八字订单不存在');
    }
    
  } catch (error) {
    console.error('检查失败:', error.message);
  } finally {
    await sequelize.close();
  }
}

checkNewOrder();
