#!/bin/bash

# 检查远程服务器状态
REMOTE_HOST="root@************"
PASSWORD="Noending5@"

echo "🔍 检查远程服务器状态..."

# 使用expect来自动输入密码
expect << EOF
set timeout 30
spawn ssh -o StrictHostKeyChecking=no $REMOTE_HOST

expect {
    "password:" {
        send "$PASSWORD\r"
        exp_continue
    }
    "# " {
        send "cd /data/commission-platform8z/backend\r"
        expect "# "
        
        send "echo '=== 当前目录 ==='\r"
        expect "# "
        send "pwd\r"
        expect "# "
        
        send "echo '=== Node.js版本 ==='\r"
        expect "# "
        send "node --version\r"
        expect "# "
        send "npm --version\r"
        expect "# "
        
        send "echo '=== 文件检查 ==='\r"
        expect "# "
        send "ls -la start-production.js src/index.js .env.production 2>/dev/null || echo '某些文件不存在'\r"
        expect "# "
        
        send "echo '=== PM2状态 ==='\r"
        expect "# "
        send "pm2 status\r"
        expect "# "
        
        send "echo '=== PM2日志 ==='\r"
        expect "# "
        send "pm2 logs commission-platform-api --lines 20 --nostream 2>/dev/null || echo 'PM2日志获取失败'\r"
        expect "# "
        
        send "echo '=== 端口检查 ==='\r"
        expect "# "
        send "lsof -i :3005 2>/dev/null || echo '端口3005未被占用'\r"
        expect "# "
        
        send "echo '=== 环境变量文件检查 ==='\r"
        expect "# "
        send "if [ -f .env.production ]; then echo '.env.production 存在'; head -5 .env.production; else echo '.env.production 不存在'; fi\r"
        expect "# "
        
        send "exit\r"
    }
    timeout {
        puts "连接超时"
        exit 1
    }
}
expect eof
EOF
