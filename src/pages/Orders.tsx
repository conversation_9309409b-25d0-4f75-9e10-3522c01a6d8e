import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import { 
  Spin, Space, Button, DatePicker, Select, Input, Pagination, Empty, Tag, Badge, Tooltip,
  Row, Col, Typography
} from 'antd';
import {
  FileDoneOutlined, SearchOutlined, FilterOutlined, ReloadOutlined,
  CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined,
  DollarOutlined, UserOutlined, PhoneOutlined, ShoppingOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { LoadingOutlined } from '@ant-design/icons'; // 增加LoadingOutlined图标
import BaziOrderManagement from '../components/BaziOrderManagement';

// 导入主题组件
import {
  PageContainer,
  PageTitle,
  PageDescription,
  Card,
  StatsCard,
  Table,
  StatusTag,
  FilterContainer,
  colors
} from '../styles/AdminTheme';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Text } = Typography;

interface Order {
  id: number;
  amount: number;
  status: 'pending' | 'paid' | 'cancelled' | 'completed';
  orderType: 'physical' | 'service'; // 订单类型
  customerName: string;
  customerPhone: string;
  agentCommission: number;
  parentAgentCommission: number;
  adminCommission: number;
  createdAt: string;
  paidAt: string | null;
  product: {
    title: string;
    price: number;
    type?: 'physical' | 'service'; // 产品类型
  } | null;
  agent: {
    id: number;
    name: string;
    username: string;
    level: number;
  } | null;
}

const Orders: React.FC = () => {
  const { token, user } = useAuth();
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalOrders, setTotalOrders] = useState(0);
  const [totalPaidOrders, setTotalPaidOrders] = useState(0);
  const [filter, setFilter] = useState({
    status: '',
    dateRange: null,
    search: ''
  });
  const [searchLoading, setSearchLoading] = useState(false);
  const [baziOrderVisible, setBaziOrderVisible] = useState(false);
  const [selectedOrderId, setSelectedOrderId] = useState<string | undefined>();

  // 计算订单统计信息
  const orderStats = {
    total: totalOrders,
    paid: totalPaidOrders,
    today: {
      amount: orders
        .filter(order => {
          if (!order?.createdAt || (order.status !== 'paid' && order.status !== 'completed')) return false;
          const orderDate = new Date(order.createdAt);
          const today = new Date();
          return orderDate.getDate() === today.getDate() &&
                 orderDate.getMonth() === today.getMonth() &&
                 orderDate.getFullYear() === today.getFullYear();
        })
        .reduce((sum, order) => sum + (order?.amount || 0), 0),
      commission: orders
        .filter(order => {
          if (!order?.createdAt || (order.status !== 'paid' && order.status !== 'completed')) return false;
          const orderDate = new Date(order.createdAt);
          const today = new Date();
          return orderDate.getDate() === today.getDate() &&
                 orderDate.getMonth() === today.getMonth() &&
                 orderDate.getFullYear() === today.getFullYear();
        })
        .reduce((sum, order) => {
          if (!order) return sum;
          if (user?.role === 'admin') {
            return sum + (order.adminCommission || 0);
          } else if (user?.role === 'user' && user?.level === 1) {
            if (order.agent?.id === user.id) {
              return sum + (order.agentCommission || 0);
            } else {
              return sum + (order.parentAgentCommission || 0);
            }
          } else {
            return sum + (order.agentCommission || 0);
          }
        }, 0)
    }
  };

  // 监听currentPage和filter的变化
  useEffect(() => {
    console.log('Orders页面 - useEffect触发', { currentPage, filter });
    if (token) {
      fetchOrders();
    }
  }, [token, currentPage, filter]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      
      if (!token) {
        console.error('Orders页面 - 用户令牌不存在');
        setError('请先登录');
        return;
      }
      
      // 构建查询参数
      let url = `${config.apiUrl}/api/orders?page=${currentPage}&pageSize=${pageSize}&sort=createdAt,desc`;
      
      if (filter.status) {
        url += `&status=${filter.status}`;
        console.log('Orders页面 - 添加状态筛选:', filter.status);
      }
      
      if (filter.dateRange && filter.dateRange[0] && filter.dateRange[1]) {
        const startDate = filter.dateRange[0].format('YYYY-MM-DD');
        const endDate = filter.dateRange[1].format('YYYY-MM-DD');
        url += `&startDate=${startDate}&endDate=${endDate}`;
        console.log('Orders页面 - 添加日期筛选:', { startDate, endDate });
      }
      
      if (filter.search && filter.search.trim()) {
        const searchTerm = encodeURIComponent(filter.search.trim());
        url += `&search=${searchTerm}`;
        console.log('Orders页面 - 添加搜索条件:', searchTerm);
      }
      
      console.log('Orders页面 - 最终API请求URL:', url);
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Orders页面 - API响应错误:', response.status, errorText);
        throw new Error(`获取订单失败 (${response.status}): ${errorText || '未知错误'}`);
      }

      // 获取原始响应文本
      const responseText = await response.text();
      console.log('Orders页面 - 原始响应:', responseText.substring(0, 200) + '...');
      
      // 解析响应JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        console.error('Orders页面 - JSON解析错误:', e);
        throw new Error('响应数据格式错误');
      }
      
      console.log('Orders页面 - 解析后的订单数据:', data ? '有数据' : '无数据');
      console.log('Orders页面 - 分页信息:', data.pagination ? data.pagination : '无分页数据');
      
      // 设置订单数据
      if (Array.isArray(data.orders)) {
        console.log('Orders页面 - 收到订单数量:', data.orders.length);
        setOrders(data.orders);
      } else if (Array.isArray(data)) {
        console.log('Orders页面 - 使用data作为数组, 长度:', data.length);
        setOrders(data);
      } else {
        console.error('Orders页面 - 订单数据格式不正确:', data);
        setOrders([]);
      }
      
      // 正确设置分页数据
      if (data.pagination) {
        console.log('Orders页面 - 设置分页数据:', data.pagination);
        setTotalPages(data.pagination.totalPages || 1);
        setTotalOrders(data.pagination.total || 0);
        setTotalPaidOrders(data.pagination.totalPaid || 0);
      } else {
        // 兼容旧格式
        console.log('Orders页面 - 使用旧格式分页数据');
        setTotalPages(data.totalPages || 1);
        setTotalOrders(data.totalOrders || 0);
        setTotalPaidOrders(data.totalPaidOrders || 0);
      }
    } catch (err) {
      console.error('获取订单失败:', err);
      setError(err instanceof Error ? err.message : '获取订单失败');
      setOrders([]);
      setTotalOrders(0);
      setTotalPaidOrders(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
      setSearchLoading(false); // 确保搜索加载状态也被重置
    }
  };

  const handlePageChange = (page: number) => {
    console.log('页码变化:', page);
    setCurrentPage(page);
  };

  const handleFilterChange = (key: string, value: any) => {
    console.log('Orders页面 - 筛选条件变更:', key, value);
    
    // 如果是搜索框，我们单独处理
    if (key === 'search') {
      setSearchLoading(true);
      setFilter(prev => ({ ...prev, [key]: value }));
      setCurrentPage(1); // 重置到第一页
    } else {
      // 其他筛选条件正常处理
      setFilter(prev => ({ ...prev, [key]: value }));
      setCurrentPage(1); // 重置到第一页
    }
  };

  const handleResetFilters = () => {
    console.log('Orders页面 - 重置所有筛选条件');
    setFilter({
      status: '',
      dateRange: null,
      search: ''
    });
    setCurrentPage(1);
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'paid':
        return '已支付';
      case 'completed':
        return '已完成';
      case 'pending':
        return '待支付';
      case 'cancelled':
        return '已取消';
      default:
        return '未知状态';
    }
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'paid':
        return <StatusTag status="success">{getStatusText(status)}</StatusTag>;
      case 'completed':
        return <StatusTag status="success">{getStatusText(status)}</StatusTag>;
      case 'pending':
        return <StatusTag status="warning">{getStatusText(status)}</StatusTag>;
      case 'cancelled':
        return <StatusTag status="error">{getStatusText(status)}</StatusTag>;
      default:
        return <StatusTag status="default">{getStatusText(status)}</StatusTag>;
    }
  };

  const getOrderSource = (order: Order) => {
    // 如果没有agent信息
    if (!order.agent) {
      return order.customerName || '未知客户';
    }

    // 如果是管理员或一级代理
    if (user?.role === 'admin' || (user?.role === 'user' && user?.level === 1)) {
      // 如果订单是自己的
      if (order.agent.id === user.id) {
        return '我的订单';
      }
      // 如果是下级代理的订单
      return order.agent.name || '未知代理';
    }
    
    // 对于二级代理，显示"我的订单"
    if (user?.role === 'user' && user?.level === 2) {
      return '我的订单';
    }
    
    // 对于其他用户，显示客户信息
    return order.customerName || '未知客户';
  };

  const getCommissionText = () => {
    if (user?.role === 'admin') {
      return '管理员佣金';
    }
    return '我的佣金';
  };

  const getCommissionAmount = (order: Order) => {
    // 如果是管理员
    if (user?.role === 'admin') {
      return order.adminCommission || 0;
    }
    
    // 如果是一级代理
    if (user?.role === 'user' && user?.level === 1) {
      // 检查order.agent是否存在
      if (!order.agent) {
        return order.agentCommission || 0;
      }
      // 如果订单是自己的
      if (order.agent.id === user.id) {
        return order.agentCommission || 0;
      }
      // 如果是下级代理的订单
      return order.parentAgentCommission || 0;
    }
    
    // 如果是二级代理（只能看到自己的佣金）
    return order.agentCommission || 0;
  };

  // 构建表格列
  const columns = [
    {
      title: '订单信息',
      key: 'orderInfo',
      render: (order: Order) => (
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <span style={{ fontWeight: 'bold' }}>{order.product?.title || '未知产品'}</span>
          <Space>
            <Tag color="blue">订单号: {order.id}</Tag>
            <Tag color="cyan">下单时间: {new Date(order.createdAt).toLocaleDateString()}</Tag>
          </Space>
        </Space>
      )
    },
    {
      title: '客户信息',
      key: 'customer',
      render: (order: Order) => (
        <Space direction="vertical" size="small">
          <span>{getOrderSource(order)}</span>
          {!(user?.role === 'admin' || (user?.role === 'user' && user?.level === 1)) && order.customerPhone && (
            <span style={{ color: colors.text.secondary }}>{order.customerPhone}</span>
          )}
        </Space>
      )
    },
    {
      title: '订单金额',
      key: 'amount',
      render: (order: Order) => (
        <span style={{ fontWeight: 'bold' }}>¥{(order.amount || 0).toFixed(2)}</span>
      )
    },
    {
      title: getCommissionText(),
      key: 'commission',
      render: (order: Order) => (
        <span style={{ color: colors.warning, fontWeight: 'bold' }}>¥{getCommissionAmount(order).toFixed(2)}</span>
      )
    },
    {
      title: '状态',
      key: 'status',
      render: (order: Order) => (
        <Space direction="vertical" size="small">
          {getStatusTag(order.status)}
          {order.paidAt && (
            <span style={{ color: colors.text.secondary, fontSize: '12px' }}>
              支付时间: {new Date(order.paidAt).toLocaleString()}
            </span>
          )}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (order: Order) => (
        <Space>
          {(order.orderType === 'service' || order.product?.type === 'service') && (
            <Tooltip title="查看八字详情">
              <Button
                type="primary"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => {
                  setSelectedOrderId(order.id.toString());
                  setBaziOrderVisible(true);
                }}
              >
                八字详情
              </Button>
            </Tooltip>
          )}
          {order.product?.type === 'naming' && (
            <Tooltip title="查看起名订单详情">
              <Button
                type="default"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => {
                  // 这里可以添加查看起名订单详情的逻辑
                  console.log('查看起名订单详情:', order.id);
                }}
              >
                起名详情
              </Button>
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <PageContainer>
      <div className="page-header">
        <PageTitle level={2}>
          <FileDoneOutlined className="icon" />
          订单管理
        </PageTitle>
        <PageDescription>
          查看并管理所有订单信息，包括订单状态、金额和佣金
        </PageDescription>
      </div>

      {/* 统计卡片 */}
      <div className="mb-6">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <StatsCard>
              <div className="stats-content">
                <Text className="stats-title">订单总数</Text>
                <span className="stats-value">{orderStats.total}</span>
                <div className="stats-footer">
                  <FileDoneOutlined style={{ color: colors.primary }} />
                  <Text type="secondary" style={{ marginLeft: 8 }}>所有订单记录</Text>
                </div>
              </div>
            </StatsCard>
          </Col>
          <Col xs={24} sm={8}>
            <StatsCard>
              <div className="stats-content">
                <Text className="stats-title">今日销售额</Text>
                <span className="stats-value">¥{orderStats.today.amount.toFixed(2)}</span>
                <div className="stats-footer">
                  <ShoppingOutlined style={{ color: colors.success }} />
                  <Text type="secondary" style={{ marginLeft: 8 }}>今日已支付订单总额</Text>
                </div>
              </div>
            </StatsCard>
          </Col>
          <Col xs={24} sm={8}>
            <StatsCard>
              <div className="stats-content">
                <Text className="stats-title">今日佣金</Text>
                <span className="stats-value">¥{orderStats.today.commission.toFixed(2)}</span>
                <div className="stats-footer">
                  <DollarOutlined style={{ color: colors.warning }} />
                  <Text type="secondary" style={{ marginLeft: 8 }}>今日订单佣金收入</Text>
                </div>
              </div>
            </StatsCard>
          </Col>
        </Row>
      </div>

      <Card className="mb-6">
        <FilterContainer>
          <div className="filter-item">
            <Select
              placeholder="订单状态"
              style={{ width: '100%' }}
              allowClear
              value={filter.status || undefined}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="pending">待支付</Option>
              <Option value="paid">已支付</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </div>
          <div className="filter-item" style={{ minWidth: 280 }}>
            <RangePicker
              placeholder={['开始日期', '结束日期']}
              value={filter.dateRange}
              onChange={(dates) => {
                console.log('Orders页面 - 日期选择变更:', dates);
                handleFilterChange('dateRange', dates);
              }}
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
            />
          </div>
          <div className="filter-item" style={{ minWidth: 200, flex: 1 }}>
            <Input
              placeholder="订单号/客户名称/手机号/产品名称"
              value={filter.search}
              onChange={(e) => {
                console.log('Orders页面 - 搜索框内容变更:', e.target.value);
                handleFilterChange('search', e.target.value);
              }}
              onPressEnter={(e) => {
                console.log('Orders页面 - 搜索框回车键触发搜索');
                e.preventDefault();
                fetchOrders();
              }}
              prefix={searchLoading ? <LoadingOutlined /> : <SearchOutlined />}
              allowClear
              style={{ width: '100%' }}
            />
          </div>
          <div className="filter-actions">
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleResetFilters}
            >
              重置筛选
            </Button>
          </div>
        </FilterContainer>

        {loading || searchLoading ? (
          <div style={{ textAlign: 'center', padding: '50px 0' }}>
            <Spin size="large">
              <div className="content">
                <div style={{ marginTop: 16 }}>加载订单数据...</div>
              </div>
            </Spin>
          </div>
        ) : error ? (
          <div style={{ textAlign: 'center', padding: '30px 0' }}>
            <Empty 
              description={<span style={{ color: colors.error }}>{error}</span>}
              image={Empty.PRESENTED_IMAGE_SIMPLE} 
            />
          </div>
        ) : orders.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '30px 0' }}>
            <Empty description="暂无订单数据" />
          </div>
        ) : (
          <>
            <div className="responsive-table-container" style={{ overflowX: 'auto', width: '100%' }}>
              <Table
                dataSource={orders}
                columns={columns}
                rowKey="id"
                pagination={false}
                scroll={{ x: 'max-content' }}
                className="responsive-table"
                size={window.innerWidth < 768 ? "small" : "middle"}
              />
            </div>
            
            <div style={{ textAlign: 'right', marginTop: 16 }}>
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalOrders}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total) => `共 ${total} 条记录`}
              />
            </div>
          </>
        )}
      </Card>

      {/* 八字订单管理弹窗 */}
      <BaziOrderManagement
        visible={baziOrderVisible}
        onClose={() => {
          setBaziOrderVisible(false);
          setSelectedOrderId(undefined);
        }}
        orderId={selectedOrderId}
      />
    </PageContainer>
  );
};

export default Orders;