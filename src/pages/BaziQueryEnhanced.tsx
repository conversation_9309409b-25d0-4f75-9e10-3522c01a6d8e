import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { BaziFormEnhanced } from '../components/BaziFormEnhanced';
import '../styles/BaziReportEnhanced.css';

const BaziQueryEnhanced: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [productCode, setProductCode] = useState<string>('');

  useEffect(() => {
    // 从URL参数获取产品代码
    const code = searchParams.get('code') || searchParams.get('productCode') || '';
    setProductCode(code);
  }, [searchParams]);

  return (
    <div style={{ 
      minHeight: '100vh', 
      background: '#ecedee',
      padding: '0'
    }}>
      {/* 顶部动画区域 */}
      <div className="top-animate">
        <div className="compass">
          <div className="outside"></div>
          <div className="inside"></div>
          <div className="cover"></div>
        </div>
      </div>
      
      {/* 表单区域 */}
      <BaziFormEnhanced 
        productCode={productCode}
        showExampleReport={true}
        showCustomerService={true}
      />
      
      {/* 底部说明 */}
      <div style={{ 
        textAlign: 'center', 
        padding: '20px',
        color: '#666',
        fontSize: '12px',
        lineHeight: '1.5'
      }}>
        <div>🔮 基于传统八字命理学，结合现代AI技术</div>
        <div>📱 支持公历农历，精确到分钟的专业测算</div>
        <div>🎯 新一代八字查询系统 - 更准确，更专业</div>
      </div>
    </div>
  );
};

export default BaziQueryEnhanced;
