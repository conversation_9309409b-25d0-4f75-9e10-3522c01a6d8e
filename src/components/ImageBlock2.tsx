import React from 'react';

// 导入图片
const a8 = '/assets/theme/2/8.jpg';
const a9 = '/assets/theme/2/9.jpg';
const a10 = '/assets/theme/2/10.jpg';
const a11 = '/assets/theme/2/11.jpg';
const a12 = '/assets/theme/2/12.jpg';

export const ImageBlock2: React.FC = () => {
  return (
    <>
      <section className="block a8">
        <img src={a8} alt="八字精批示例1" />
      </section>
      <section className="block">
        <img src={a9} alt="八字精批示例2" />
      </section>
      <section className="block">
        <img src={a10} alt="八字精批示例3" />
      </section>
      <section className="block">
        <img src={a11} alt="八字精批示例4" />
      </section>
      <section className="block">
        <img src={a12} alt="八字精批示例5" />
      </section>
    </>
  );
};
