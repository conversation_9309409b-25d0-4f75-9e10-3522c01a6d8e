import express from 'express';
import { auth, adminAuth } from '../middleware/auth.js';
import { Withdrawal, Commission, User } from '../models/index.js';
import sequelize from '../config/database.js';

const router = express.Router();

// 获取提现列表
router.get('/', auth, async (req, res) => {
  try {
    let withdrawals;
    let availableAmount = 0;

    const includeOptions = [
      { 
        model: User, 
        as: 'withdrawalAgent',
        attributes: ['id', 'username', 'name']
      },
      { 
        model: User, 
        as: 'withdrawalApprover',
        attributes: ['id', 'username', 'name']
      },
      { 
        model: Commission, 
        as: 'commissions',
        attributes: ['id', 'amount', 'status']
      }
    ];

    if (req.user.role === 'admin') {
      // 管理员可以看到所有提现记录
      withdrawals = await Withdrawal.findAll({
        include: includeOptions,
        order: [['createdAt', 'DESC']]
      });

      // 计算所有可提现金额
      const result = await Commission.findOne({
        where: { status: 'available' },
        attributes: [
          [sequelize.fn('sum', sequelize.col('amount')), 'total']
        ],
        raw: true
      });
      availableAmount = Number(result?.total) || 0;

    } else {
      // 代理商只能看到自己的提现记录
      withdrawals = await Withdrawal.findAll({
        where: {
          agentId: req.user.id
        },
        include: includeOptions,
        order: [['createdAt', 'DESC']]
      });

      // 计算个人可提现金额
      const result = await Commission.findOne({
        where: { 
          agentId: req.user.id,
          status: 'available'
        },
        attributes: [
          [sequelize.fn('sum', sequelize.col('amount')), 'total']
        ],
        raw: true
      });
      availableAmount = Number(result?.total) || 0;
    }

    // 格式化数据
    const formattedWithdrawals = withdrawals.map(withdrawal => {
      const plainWithdrawal = withdrawal.get({ plain: true });
      return {
        ...plainWithdrawal,
        amount: Number(plainWithdrawal.amount || 0),
        commissions: plainWithdrawal.commissions?.map(commission => ({
          ...commission,
          amount: Number(commission.amount || 0)
        }))
      };
    });

    res.json({
      withdrawals: formattedWithdrawals,
      availableAmount
    });
  } catch (error) {
    console.error('获取提现列表失败:', error);
    res.status(500).json({ 
      message: '获取提现列表失败',
      error: error.message 
    });
  }
});

// 获取单个提现详情
router.get('/:id', auth, async (req, res) => {
    try {
        // TODO: 实现获取单个提现详情逻辑
        res.json({ message: "获取提现详情" });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 申请提现
router.post('/', auth, async (req, res) => {
  try {
    const { amount, alipayAccount } = req.body;

    // 检查可提现金额
    const availableCommissions = await Commission.findAll({
      where: {
        agentId: req.user.id,
        status: 'available'
      }
    });

    const totalAvailable = availableCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);

    if (amount > totalAvailable) {
      return res.status(400).json({ message: '可提现金额不足' });
    }

    const withdrawal = await Withdrawal.create({
      amount,
      alipayAccount,
      agentId: req.user.id,
      status: 'pending'
    });

    // 更新相关佣金状态
    let remainingAmount = amount;
    for (const commission of availableCommissions) {
      if (remainingAmount <= 0) break;

      const commissionAmount = Number(commission.amount);
      if (commissionAmount <= remainingAmount) {
        await commission.update({
          status: 'withdrawn',
          withdrawalId: withdrawal.id
        });
        remainingAmount -= commissionAmount;
      } else {
        // 如果佣金金额大于剩余所需金额，需要拆分
        await commission.update({
          amount: commissionAmount - remainingAmount
        });

        await Commission.create({
          amount: remainingAmount,
          status: 'withdrawn',
          orderId: commission.orderId,
          agentId: commission.agentId,
          withdrawalId: withdrawal.id
        });

        remainingAmount = 0;
      }
    }

    res.status(201).json(withdrawal);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// 审核提现 - 仅管理员
router.put('/:id', adminAuth, async (req, res) => {
  try {
    const withdrawal = await Withdrawal.findByPk(req.params.id);
    if (!withdrawal) {
      return res.status(404).json({ message: '提现记录不存在' });
    }

    const { status, remarks } = req.body;
    
    if (status === 'approved') {
      await withdrawal.update({
        status,
        remarks,
        approvedAt: new Date(),
        approvedBy: req.user.id
      });
    } else if (status === 'rejected') {
      // 如果拒绝提现，需要将相关佣金状态改回可提现
      await Commission.update(
        { status: 'available', withdrawalId: null },
        { where: { withdrawalId: withdrawal.id } }
      );

      await withdrawal.update({
        status,
        remarks,
        approvedAt: new Date(),
        approvedBy: req.user.id
      });
    } else if (status === 'completed') {
      await withdrawal.update({
        status,
        completedAt: new Date()
      });
    }

    res.json(withdrawal);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

export default router; 