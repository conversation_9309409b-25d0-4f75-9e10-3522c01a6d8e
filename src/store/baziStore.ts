import { create } from 'zustand';

export interface BaziFormData {
  name: string;
  sex: '1' | '2'; // 1: 男, 2: 女
  birthday: string;
  address: string;
  yearType: '1' | '2'; // 1: 公历, 2: 农历
  phone?: string;
  // 详细的日期时间数据
  dateTime?: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  };
  // 详细的地区数据
  area?: {
    province: string;
    city: string;
    district?: string;
  };
}

export interface BaziStoreState {
  // 表单数据
  formData: BaziFormData;
  
  // UI状态
  showCalendarTypeSelector: boolean;
  showDateTimeSelector: boolean;
  showAreaSelector: boolean;
  selectedCalendarType: 'solar' | 'lunar';
  
  // 加载状态
  loading: boolean;
  submitting: boolean;
  
  // 产品信息
  productCode?: string;
  
  // Actions
  setFormData: (data: Partial<BaziFormData>) => void;
  setDateTime: (dateTime: BaziFormData['dateTime']) => void;
  setArea: (area: BaziFormData['area']) => void;
  
  // UI Actions
  setShowCalendarTypeSelector: (show: boolean) => void;
  setShowDateTimeSelector: (show: boolean) => void;
  setShowAreaSelector: (show: boolean) => void;
  setSelectedCalendarType: (type: 'solar' | 'lunar') => void;
  
  // Loading Actions
  setLoading: (loading: boolean) => void;
  setSubmitting: (submitting: boolean) => void;
  
  // Product Actions
  setProductCode: (code: string) => void;
  
  // Reset
  resetForm: () => void;
}

const initialFormData: BaziFormData = {
  name: '',
  sex: '1',
  birthday: '',
  address: '',
  yearType: '1',
  phone: ''
};

export const useBaziStore = create<BaziStoreState>((set, get) => ({
  // 初始状态
  formData: initialFormData,
  showCalendarTypeSelector: false,
  showDateTimeSelector: false,
  showAreaSelector: false,
  selectedCalendarType: 'solar',
  loading: false,
  submitting: false,
  productCode: undefined,
  
  // Actions
  setFormData: (data) => set((state) => ({
    formData: { ...state.formData, ...data }
  })),
  
  setDateTime: (dateTime) => {
    if (!dateTime) return;
    
    const birthdayStr = `${dateTime.year}-${dateTime.month.toString().padStart(2, '0')}-${dateTime.day.toString().padStart(2, '0')} ${dateTime.hour.toString().padStart(2, '0')}:${dateTime.minute.toString().padStart(2, '0')}`;
    
    set((state) => ({
      formData: {
        ...state.formData,
        birthday: birthdayStr,
        yearType: dateTime.calendarType === 'solar' ? '1' : '2',
        dateTime
      }
    }));
  },
  
  setArea: (area) => {
    if (!area) return;
    
    const address = area.district 
      ? `${area.province} ${area.city} ${area.district}` 
      : `${area.province} ${area.city}`;
    
    set((state) => ({
      formData: {
        ...state.formData,
        address,
        area
      }
    }));
  },
  
  // UI Actions
  setShowCalendarTypeSelector: (show) => set({ showCalendarTypeSelector: show }),
  setShowDateTimeSelector: (show) => set({ showDateTimeSelector: show }),
  setShowAreaSelector: (show) => set({ showAreaSelector: show }),
  setSelectedCalendarType: (type) => set({ selectedCalendarType: type }),
  
  // Loading Actions
  setLoading: (loading) => set({ loading }),
  setSubmitting: (submitting) => set({ submitting }),
  
  // Product Actions
  setProductCode: (code) => set({ productCode: code }),
  
  // Reset
  resetForm: () => set({
    formData: initialFormData,
    showCalendarTypeSelector: false,
    showDateTimeSelector: false,
    showAreaSelector: false,
    selectedCalendarType: 'solar',
    loading: false,
    submitting: false
  })
}));
