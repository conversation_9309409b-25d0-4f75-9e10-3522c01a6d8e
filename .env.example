# 环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# 应用环境
NODE_ENV=development

# API配置
VITE_API_BASE_URL=http://localhost:3005

# 支付配置
VITE_ALIPAY_APP_ID=your_alipay_app_id
VITE_ALIPAY_PUBLIC_KEY=your_alipay_public_key
VITE_WECHAT_APP_ID=your_wechat_app_id
VITE_WECHAT_MCH_ID=your_wechat_mch_id

# 分析服务
VITE_GA_ID=your_google_analytics_id
VITE_BAIDU_ANALYTICS_ID=your_baidu_analytics_id
VITE_MIXPANEL_TOKEN=your_mixpanel_token

# 监控服务
VITE_SENTRY_DSN=your_sentry_dsn
VITE_LOGROCKET_APP_ID=your_logrocket_app_id

# CDN配置
VITE_CDN_IMAGES=https://cdn.example.com/images
VITE_CDN_ASSETS=https://cdn.example.com/assets

# 数据库配置（后端使用）
DATABASE_URL=postgresql://username:password@localhost:5432/bazi_db

# Redis配置（后端使用）
REDIS_URL=redis://localhost:6379

# JWT密钥（后端使用）
JWT_SECRET=your_jwt_secret_key

# 邮件服务（后端使用）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# 短信服务（后端使用）
SMS_ACCESS_KEY=your_sms_access_key
SMS_SECRET_KEY=your_sms_secret_key

# 文件存储（后端使用）
OSS_ACCESS_KEY=your_oss_access_key
OSS_SECRET_KEY=your_oss_secret_key
OSS_BUCKET=your_oss_bucket
OSS_REGION=your_oss_region
