import type { MingZhuBaZiMingPanXinXi, BaZiPillar, BaZiDa<PERSON>un, <PERSON><PERSON>, WeiLaiYiNian<PERSON>un<PERSON>heng } from '../../types/bazi';
import { SectionCard } from './section-card';
import { InfoItem, ParagraphText } from './info-item';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '../../components/ui/accordion';
import { BarChartBig, Brain, CalendarCheck2, CircleDollarSign, DraftingCompass, Heart, LineChart, LucideIcon, Stethoscope, UserCog } from 'lucide-react';

interface MingPanInfoSectionProps {
  data: MingZhuBaZiMingPanXinXi;
}

const PillarDisplay: React.FC<{ title: string, pillar: BaZiPillar }> = ({ title, pillar }) => (
  <div className="p-3 border border-border/70 rounded-md bg-background/30 shadow mb-3 sm:mb-0">
    <h4 className="text-md font-semibold text-primary mb-1.5">{title} ({pillar.乾造})</h4>
    <InfoItem label="十神" value={pillar.十神} />
    <InfoItem label="纳音" value={pillar.纳音} />
    <InfoItem label="星运" value={pillar.星运} />
    <InfoItem label="自坐" value={pillar.自坐} />
    <InfoItem label="空亡" value={pillar.空亡} />
    <InfoItem label="藏干" value={pillar.藏干.map(cg => cg.join('')).join(', ')} />
    <InfoItem label="神煞" value={pillar.神煞.join(', ')} />
  </div>
);

const LiuNianDisplay: React.FC<{ liunian: LiuNianJiXiong }> = ({ liunian }) => (
 <div className="p-3 border border-border/50 rounded-md mb-3 bg-muted/30 shadow-sm">
    <p className="font-semibold text-primary text-sm sm:text-base">{liunian.流年} ({liunian.十神})</p>
    <InfoItem label="星运" value={liunian.星运} />
    <InfoItem label="自坐" value={liunian.自坐} />
    <InfoItem label="纳音" value={liunian.纳音} />
    {liunian.吉凶 && <InfoItem label="吉凶" value={liunian.吉凶} />}
    <InfoItem label="神煞" value={liunian.神煞.join(', ')} />
    <InfoItem label="天干留意" value={liunian.天干留意} />
    <InfoItem label="地支留意" value={liunian.地支留意} />
  </div>
);

const DaYunDisplay: React.FC<{ dayun: BaZiDaYun, index: number }> = ({ dayun, index }) => (
  <AccordionItem value={`dayun-${index}`} className="mb-2 border-0">
    <AccordionTrigger className="bg-muted/50 hover:bg-muted/70 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">
      大运: {dayun.年份} (虚岁 {dayun.虚岁}) - {dayun.大运.join(' ')}
    </AccordionTrigger>
    <AccordionContent className="pt-3 px-3 sm:px-4 text-sm">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-3">
        <InfoItem label="纳音" value={dayun.纳音} />
        <InfoItem label="空亡" value={dayun.空亡} />
        <InfoItem label="星运" value={dayun.星运} />
        <InfoItem label="自坐" value={dayun.自坐} />
      </div>
      <InfoItem label="藏干" value={dayun.藏干.map(cg => cg.join('')).join(', ')} />
      <InfoItem label="神煞" value={dayun.神煞.join(', ')} />
      <h5 className="font-semibold mt-3 mb-2 text-primary text-sm sm:text-base">流年吉凶:</h5>
      <div className="max-h-80 sm:max-h-96 overflow-y-auto space-y-3 pr-2">
        {dayun.流年吉凶.map((ln, i) => (
          <LiuNianDisplay key={i} liunian={ln} />
        ))}
      </div>
    </AccordionContent>
  </AccordionItem>
);

const DetailSection: React.FC<{title: string, content: string | { [key: string]: string }, icon: LucideIcon}> = ({ title, content, icon: Icon }) => {
  return (
    <AccordionItem value={title} className="border-b-0 mb-2">
      <AccordionTrigger className="bg-muted/50 hover:bg-muted/70 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">
        <Icon className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-accent shrink-0"/> {title}
      </AccordionTrigger>
      <AccordionContent className="pt-3 px-3 sm:px-4 text-sm space-y-3">
        {typeof content === 'string' ? <ParagraphText text={content} /> : 
          Object.entries(content).map(([key, value]) => (
            <InfoItem key={key} label={key} value={<ParagraphText text={value}/>} direction="col" />
          ))
        }
      </AccordionContent>
    </AccordionItem>
  )
};

const YearlyLuckItem: React.FC<{ luck: WeiLaiYiNianYunCheng }> = ({ luck }) => (
  <div className="p-3 border border-border/70 rounded-md bg-background/30 shadow-sm mb-4">
    <h5 className="font-semibold text-primary text-sm sm:text-base">{luck.日期}</h5>
    <InfoItem label="运势" value={<ParagraphText text={luck.运势}/>} direction="col" />
    <InfoItem label="十神" value={<ParagraphText text={luck.十神}/>} direction="col" />
  </div>
);

export function MingPanInfoSection({ data }: MingPanInfoSectionProps) {
  const { 八字信息, 八字精批详解, 八字_年柱, 八字_月柱, 八字_日柱, 八字_时柱, 八字_大运 } = data;

  return (
    <SectionCard title="命主八字命盘信息" icon={DraftingCompass} contentClassName="space-y-6">
      <Accordion type="multiple" className="w-full space-y-3" defaultValue={['bazi-info']}>
        <AccordionItem value="bazi-info" className="border-b-0">
          <AccordionTrigger className="bg-muted/50 hover:bg-muted/70 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">八字基本信息</AccordionTrigger>
          <AccordionContent className="pt-3 px-3 sm:px-4 text-sm space-y-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4">
              <InfoItem label="公历" value={`${八字信息.公历.年}${八字信息.公历.月}${八字信息.公历.日}${八字信息.公历.时}`} />
              <InfoItem label="农历" value={`${八字信息.农历.年}${八字信息.农历.月}${八字信息.农历.日}${八字信息.农历.时}`} />
            </div>
            <InfoItem label="节气" value={八字信息.节气.join(' | ')} />
            <InfoItem label="起运" value={八字信息.起运} />
            <InfoItem label="交运" value={八字信息.交运} />
            <InfoItem label="胎元" value={八字信息.胎元} />
            <InfoItem label="命宫" value={八字信息.命宫} />
            <InfoItem label="天干留意" value={八字信息.天干留意} />
            <InfoItem label="地支留意" value={八字信息.地支留意} />
            <InfoItem label="三命通会" value={八字信息.三命通会.map((text, i) => <ParagraphText key={i} text={text} />)} />
            <InfoItem label="月柱命理" value={<ParagraphText text={八字信息.月柱命理} />} />
            <InfoItem label="日柱命理" value={<ParagraphText text={八字信息.日柱命理} />} />
            <InfoItem label="时柱命理" value={八字信息.时柱命理.map((text, i) => <ParagraphText key={i} text={text} />)} />
            <InfoItem label="命理综述" value={<ParagraphText text={八字信息.命理综述}/>} />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="pillars" className="border-b-0">
          <AccordionTrigger className="bg-muted/50 hover:bg-muted/70 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">四柱信息</AccordionTrigger>
          <AccordionContent className="pt-3 px-3 sm:px-4 text-sm">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <PillarDisplay title="年柱" pillar={八字_年柱} />
              <PillarDisplay title="月柱" pillar={八字_月柱} />
              <PillarDisplay title="日柱" pillar={八字_日柱} />
              <PillarDisplay title="时柱" pillar={八字_时柱} />
            </div>
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="detailed-analysis" className="border-b-0">
          <AccordionTrigger className="bg-muted/50 hover:bg-muted/70 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">
            <Brain className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-accent shrink-0"/> 八字精批详解
          </AccordionTrigger>
          <AccordionContent className="pt-3 px-3 sm:px-4 text-sm">
            <Accordion type="multiple" className="w-full space-y-2" defaultValue={['details-性格']}>
              <DetailSection title="您的性格分析" content={八字精批详解.您的性格分析} icon={UserCog}/>
              <DetailSection title="您的财运分析" content={八字精批详解.您的财运分析} icon={CircleDollarSign}/>
              <DetailSection title="您的爱情恋爱建议" content={八字精批详解.您的爱情恋爱建议.爱情分析} icon={Heart}/>
              <DetailSection title="您的健康建议" content={八字精批详解.您的健康建议} icon={Stethoscope}/>
              <DetailSection title="您的事业成就" content={八字精批详解.您的事业成就} icon={BarChartBig}/>
            </Accordion>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="yearly-luck" className="border-b-0">
          <AccordionTrigger className="bg-muted/50 hover:bg-muted/70 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">
           <CalendarCheck2 className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-accent shrink-0"/> 未来一年运程
          </AccordionTrigger>
          <AccordionContent className="pt-3 px-3 sm:px-4 text-sm">
            <div className="max-h-[400px] sm:max-h-[500px] overflow-y-auto pr-2 space-y-3">
              {八字精批详解.未来一年运程.map((luck, i) => (
                <YearlyLuckItem key={i} luck={luck} />
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="da-yun" className="border-b-0">
          <AccordionTrigger className="bg-muted/50 hover:bg-muted/70 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">
            <LineChart className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-accent shrink-0"/> 八字大运
          </AccordionTrigger>
          <AccordionContent className="pt-3 px-3 sm:px-4 text-sm">
            <Accordion type="single" collapsible className="w-full space-y-2">
              {八字_大运.map((dayun, i) => (
                <DaYunDisplay key={i} dayun={dayun} index={i} />
              ))}
            </Accordion>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </SectionCard>
  );
} 