'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 首先移除重复的唯一索引
    try {
      await queryInterface.removeIndex('ProductLinks', 'product_links_code');
    } catch (error) {
      console.log('索引 product_links_code 可能不存在，忽略错误');
    }
    
    try {
      await queryInterface.removeIndex('ProductLinks', 'code_2');
    } catch (error) {
      console.log('索引 code_2 可能不存在，忽略错误');
    }
    
    // 确保只保留一个code字段的唯一索引
    const tableInfo = await queryInterface.describeTable('ProductLinks');
    const hasCodeIndex = tableInfo.code && tableInfo.code.unique;
    
    if (!hasCodeIndex) {
      // 如果不存在code的唯一索引，则添加一个
      await queryInterface.addIndex('ProductLinks', ['code'], {
        name: 'product_links_code_unique',
        unique: true
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // 回滚操作不需要做特殊处理，因为我们只是清理重复索引
    return Promise.resolve();
  }
};
