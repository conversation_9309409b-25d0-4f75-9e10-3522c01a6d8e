import React, { useEffect, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, Row, Col, Statistic, Spin, Typography, Divider, Badge, Tooltip } from 'antd';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import { 
  LineChart, Line, BarChart, Bar, PieChart, Pie, 
  XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, 
  Legend, ResponsiveContainer, Cell 
} from 'recharts';
import { 
  ArrowUpOutlined, ArrowDownOutlined, ShoppingOutlined, 
  CheckCircleOutlined, MoneyCollectOutlined, PercentageOutlined,
  FileTextOutlined, DollarOutlined, FundOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

// 定义图表颜色
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

interface DashboardStats {
  today: {
    totalOrders: number;
    paidOrders: number;
    totalCommission: number;
    conversionRate: number;
  };
  yesterday: {
    totalOrders: number;
    paidOrders: number;
    totalCommission: number;
    conversionRate: number;
  };
  allTime: {
    totalOrders: number;
    paidOrders: number;
    totalCommission: number;
    conversionRate: number;
  };
}

const Home: React.FC = () => {
  const { isAuthenticated, token } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchStats = async () => {
      if (!isAuthenticated) return;
      
      setLoading(true);
      try {
        const response = await fetch(`${config.apiUrl}/api/dashboard/stats`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error('获取统计数据失败');
        }

        const data = await response.json();
        setStats(data);
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [isAuthenticated, token]);

  // 未登录时显示的内容
  if (!isAuthenticated) {
    return (
      <div className="relative bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
            <main className="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
              <div className="sm:text-center lg:text-left">
                <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                  <span className="block xl:inline">佣金管理平台</span>{' '}
                  <span className="block text-blue-600 xl:inline">轻松管理您的收益</span>
                </h1>
                <p className="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                  加入我们的代理团队，享受灵活的佣金分成。我们提供完整的订单跟踪、佣金计算和提现管理功能，让您专注于业务发展。
                </p>
                <div className="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                  <div className="rounded-md shadow">
                    <Link
                      to="/register"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10"
                    >
                      立即注册
                    </Link>
                  </div>
                  <div className="mt-3 sm:mt-0 sm:ml-3">
                    <Link
                      to="/products"
                      className="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 md:py-4 md:text-lg md:px-10"
                    >
                      浏览产品
                    </Link>
                  </div>
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>
    );
  }

  // 计算今日与昨日比较的变化率
  const getTrendInfo = (today: number, yesterday: number) => {
    if (yesterday === 0) return { percent: 0, isIncrease: true };
    const change = today - yesterday;
    const percent = yesterday === 0 ? 100 : Math.round((change / yesterday) * 100);
    return { percent: Math.abs(percent), isIncrease: change >= 0 };
  };

  // 为图表准备数据
  const getPerformanceData = () => {
    if (!stats) return [];
    return [
      { name: '今日', 订单数: stats.today.totalOrders, 成交订单: stats.today.paidOrders, 收益: stats.today.totalCommission },
      { name: '昨日', 订单数: stats.yesterday.totalOrders, 成交订单: stats.yesterday.paidOrders, 收益: stats.yesterday.totalCommission },
    ];
  };

  const getConversionData = () => {
    if (!stats) return [];
    
    const todayData = [
      { name: '成交', value: stats.today.paidOrders },
      { name: '未成交', value: stats.today.totalOrders - stats.today.paidOrders }
    ];
    
    return todayData.filter(item => item.value > 0);
  };

  // 登录后显示的数据看板
  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <Title level={2} className="mb-0">
            <FundOutlined className="mr-2" />数据看板
          </Title>
          <Text type="secondary">
            实时掌握您的业务表现，数据每日更新
          </Text>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center" style={{ height: '60vh' }}>
            <Spin size="large">
              <div className="content">
                <div style={{ marginTop: 16 }}>加载数据中...</div>
              </div>
            </Spin>
          </div>
        ) : (
          <>
            {/* 关键指标卡片 - 亮眼的头部区域 */}
            <div className="mb-8">
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Card 
                    className="h-full shadow-md hover:shadow-lg transition-shadow duration-300"
                    style={{ borderTop: '4px solid #1890ff' }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <Text type="secondary">今日订单数量</Text>
                        <Title level={3} className="mb-0 mt-1">{stats?.today.totalOrders || 0}</Title>
                        {stats && (
                          <div className="mt-2">
                            {getTrendInfo(stats.today.totalOrders, stats.yesterday.totalOrders).isIncrease ? (
                              <Text type="success">
                                <ArrowUpOutlined /> {getTrendInfo(stats.today.totalOrders, stats.yesterday.totalOrders).percent}%
                              </Text>
                            ) : (
                              <Text type="danger">
                                <ArrowDownOutlined /> {getTrendInfo(stats.today.totalOrders, stats.yesterday.totalOrders).percent}%
                              </Text>
                            )}
                            <Text type="secondary" className="ml-1">较昨日</Text>
                          </div>
                        )}
                      </div>
                      <ShoppingOutlined style={{ fontSize: 36, color: '#1890ff' }} />
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card 
                    className="h-full shadow-md hover:shadow-lg transition-shadow duration-300"
                    style={{ borderTop: '4px solid #52c41a' }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <Text type="secondary">今日成交订单</Text>
                        <Title level={3} className="mb-0 mt-1">{stats?.today.paidOrders || 0}</Title>
                        {stats && (
                          <div className="mt-2">
                            {getTrendInfo(stats.today.paidOrders, stats.yesterday.paidOrders).isIncrease ? (
                              <Text type="success">
                                <ArrowUpOutlined /> {getTrendInfo(stats.today.paidOrders, stats.yesterday.paidOrders).percent}%
                              </Text>
                            ) : (
                              <Text type="danger">
                                <ArrowDownOutlined /> {getTrendInfo(stats.today.paidOrders, stats.yesterday.paidOrders).percent}%
                              </Text>
                            )}
                            <Text type="secondary" className="ml-1">较昨日</Text>
                          </div>
                        )}
                      </div>
                      <CheckCircleOutlined style={{ fontSize: 36, color: '#52c41a' }} />
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card 
                    className="h-full shadow-md hover:shadow-lg transition-shadow duration-300"
                    style={{ borderTop: '4px solid #fa8c16' }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <Text type="secondary">今日订单收益</Text>
                        <Title level={3} className="mb-0 mt-1">¥{stats?.today.totalCommission?.toFixed(2) || '0.00'}</Title>
                        {stats && (
                          <div className="mt-2">
                            {getTrendInfo(stats.today.totalCommission, stats.yesterday.totalCommission).isIncrease ? (
                              <Text type="success">
                                <ArrowUpOutlined /> {getTrendInfo(stats.today.totalCommission, stats.yesterday.totalCommission).percent}%
                              </Text>
                            ) : (
                              <Text type="danger">
                                <ArrowDownOutlined /> {getTrendInfo(stats.today.totalCommission, stats.yesterday.totalCommission).percent}%
                              </Text>
                            )}
                            <Text type="secondary" className="ml-1">较昨日</Text>
                          </div>
                        )}
                      </div>
                      <MoneyCollectOutlined style={{ fontSize: 36, color: '#fa8c16' }} />
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card 
                    className="h-full shadow-md hover:shadow-lg transition-shadow duration-300"
                    style={{ borderTop: '4px solid #722ed1' }}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <Text type="secondary">今日转化率</Text>
                        <Title level={3} className="mb-0 mt-1">{stats?.today.conversionRate?.toFixed(2) || '0.00'}%</Title>
                        {stats && (
                          <div className="mt-2">
                            {getTrendInfo(stats.today.conversionRate, stats.yesterday.conversionRate).isIncrease ? (
                              <Text type="success">
                                <ArrowUpOutlined /> {getTrendInfo(stats.today.conversionRate, stats.yesterday.conversionRate).percent}%
                              </Text>
                            ) : (
                              <Text type="danger">
                                <ArrowDownOutlined /> {getTrendInfo(stats.today.conversionRate, stats.yesterday.conversionRate).percent}%
                              </Text>
                            )}
                            <Text type="secondary" className="ml-1">较昨日</Text>
                          </div>
                        )}
                      </div>
                      <PercentageOutlined style={{ fontSize: 36, color: '#722ed1' }} />
                    </div>
                  </Card>
                </Col>
              </Row>
            </div>

            {/* 绩效图表区域 */}
            <div className="mb-8">
              <Card className="shadow-md">
                <Title level={4} className="mb-4">
                  <FundOutlined className="mr-2" />业绩趋势对比
                </Title>
                <div style={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getPerformanceData()}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <RechartsTooltip />
                      <Legend />
                      <Bar dataKey="订单数" fill="#1890ff" name="订单数量" />
                      <Bar dataKey="成交订单" fill="#52c41a" name="成交订单" />
                      <Bar dataKey="收益" fill="#fa8c16" name="订单收益(元)" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </Card>
            </div>

            {/* 二分区域：转化率图表 + 总计数据 */}
            <Row gutter={[16, 16]}>
              {/* 转化率饼图 */}
              <Col xs={24} md={12}>
                <Card className="shadow-md h-full">
                  <Title level={4} className="mb-4">
                    <PercentageOutlined className="mr-2" />转化率分析
                  </Title>
                  <div style={{ height: 300 }}>
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={getConversionData()}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        >
                          {getConversionData().map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <RechartsTooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </Card>
              </Col>

              {/* 总计数据卡片 */}
              <Col xs={24} md={12}>
                <Card className="shadow-md h-full">
                  <Title level={4} className="mb-4">
                    <FileTextOutlined className="mr-2" />总计数据
                  </Title>
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Card bordered={false} className="bg-gray-50">
                        <div className="text-center">
                          <ShoppingOutlined style={{ fontSize: 24, color: '#1890ff' }} />
                          <Statistic
                            title="总订单数量"
                            value={stats?.allTime.totalOrders || 0}
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </div>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card bordered={false} className="bg-gray-50">
                        <div className="text-center">
                          <CheckCircleOutlined style={{ fontSize: 24, color: '#52c41a' }} />
                          <Statistic
                            title="总成交订单"
                            value={stats?.allTime.paidOrders || 0}
                            valueStyle={{ color: '#52c41a' }}
                          />
                        </div>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card bordered={false} className="bg-gray-50">
                        <div className="text-center">
                          <DollarOutlined style={{ fontSize: 24, color: '#fa8c16' }} />
                          <Statistic
                            title="总订单收益"
                            value={stats?.allTime.totalCommission || 0}
                            precision={2}
                            prefix="¥"
                            valueStyle={{ color: '#fa8c16' }}
                          />
                        </div>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card bordered={false} className="bg-gray-50">
                        <div className="text-center">
                          <PercentageOutlined style={{ fontSize: 24, color: '#722ed1' }} />
                          <Statistic
                            title="总转化率"
                            value={stats?.allTime.conversionRate || 0}
                            precision={2}
                            suffix="%"
                            valueStyle={{ color: '#722ed1' }}
                          />
                        </div>
                      </Card>
                    </Col>
                  </Row>
                </Card>
              </Col>
            </Row>

            <div className="mt-8 text-center">
              <Text type="secondary">数据最后更新时间: {new Date().toLocaleString()}</Text>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Home;