#!/usr/bin/env node

// 动态检测前端服务端口的脚本
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// 检测指定端口是否有服务运行
async function checkPort(port) {
  try {
    const { stdout } = await execAsync(`lsof -i :${port}`);
    return stdout.includes('node') || stdout.includes('npm') || stdout.includes('vite');
  } catch (error) {
    return false;
  }
}

// 检测前端服务端口
async function detectFrontendPort() {
  const commonPorts = [3000, 3001, 5173, 5174, 8080];
  
  console.log('🔍 检测前端服务端口...');
  
  for (const port of commonPorts) {
    const isRunning = await checkPort(port);
    if (isRunning) {
      console.log(`✅ 发现前端服务运行在端口 ${port}`);
      return port;
    }
  }
  
  console.log('⚠️ 未发现前端服务，使用默认端口 3000');
  return 3000;
}

// 更新环境变量文件
async function updateEnvFile(port) {
  const fs = await import('fs');
  const path = await import('path');
  
  const envFile = path.join(process.cwd(), '.env.develop');
  
  try {
    let content = fs.readFileSync(envFile, 'utf8');
    const frontendUrl = `http://localhost:${port}`;
    
    // 更新或添加FRONTEND_URL
    if (content.includes('FRONTEND_URL=')) {
      content = content.replace(/FRONTEND_URL=.*/, `FRONTEND_URL=${frontendUrl}`);
    } else {
      content += `\nFRONTEND_URL=${frontendUrl}\n`;
    }
    
    fs.writeFileSync(envFile, content);
    console.log(`✅ 已更新 .env.develop: FRONTEND_URL=${frontendUrl}`);
  } catch (error) {
    console.error('❌ 更新环境文件失败:', error.message);
  }
}

// 主函数
async function main() {
  try {
    const port = await detectFrontendPort();
    await updateEnvFile(port);
    
    console.log('\n📋 支付跳转配置:');
    console.log(`   八字报告支付成功后将跳转到: http://localhost:${port}/bazi-report?orderId=xxx&status=success`);
    console.log('\n💡 提示: 如需手动修改，请编辑 backend/.env.develop 文件中的 FRONTEND_URL 配置');
  } catch (error) {
    console.error('❌ 检测失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { detectFrontendPort, updateEnvFile };
