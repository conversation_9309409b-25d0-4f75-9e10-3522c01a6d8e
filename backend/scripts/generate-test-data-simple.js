/**
 * 佣金系统测试数据生成脚本
 * 
 * 功能：
 * 1. 生成测试订单和佣金数据
 * 2. 验证佣金计算是否正确
 * 3. 测试佣金提现功能
 * 
 * 使用方法：
 * node scripts/generate-test-data-simple.js
 */

import dotenv from 'dotenv';
import { Sequelize, DataTypes } from 'sequelize';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// 初始化环境变量
dotenv.config();

// 设置颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}[信息]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[成功]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[警告]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[错误]${colors.reset} ${msg}`),
  result: (msg) => console.log(`${colors.magenta}[结果]${colors.reset} ${msg}`),
  test: (msg) => console.log(`${colors.cyan}[测试]${colors.reset} ${msg}`)
};

// 连接数据库
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: false
  }
);

// 定义模型
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    allowNull: false,
    autoIncrement: true
  },
  username: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: false
  },
  role: {
    type: DataTypes.ENUM('admin', 'user'),
    defaultValue: 'user'
  },
  level: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected'),
    defaultValue: 'pending'
  },
  parentAgentId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  commissionRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 80.00
  }
}, {
  timestamps: true,
  tableName: 'Users'
});

const Product = sequelize.define('Product', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('price');
      return value === null ? null : Number(value);
    }
  },
  baseCommissionRate: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active'
  }
}, {
  timestamps: true,
  tableName: 'Products'
});

// 生成订单号
const generateOrderNumber = () => {
  const now = new Date();
  const year = now.getFullYear().toString().slice(2); // 年份后两位
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hours = now.getHours().toString().padStart(2, '0');
  const minutes = now.getMinutes().toString().padStart(2, '0');
  const seconds = now.getSeconds().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  
  return `${year}${month}${day}${hours}${minutes}${seconds}${random}`;
};

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.STRING(20),
    primaryKey: true,
    allowNull: false,
    defaultValue: generateOrderNumber
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('amount');
      return value === null ? null : Number(value);
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid', 'cancelled'),
    defaultValue: 'pending'
  },
  customerName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customerPhone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  agentId: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  parentAgentId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  agentCommission: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    get() {
      const value = this.getDataValue('agentCommission');
      return value === null ? 0 : Number(value);
    }
  },
  parentAgentCommission: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    get() {
      const value = this.getDataValue('parentAgentCommission');
      return value === null ? 0 : Number(value);
    }
  },
  paidAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'Orders'
});

const Commission = sequelize.define('Commission', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('amount');
      return value === null ? 0 : Number(value);
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'available', 'withdrawn'),
    defaultValue: 'pending'
  },
  orderId: {
    type: DataTypes.STRING(20),
    allowNull: false
  },
  agentId: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  withdrawalId: {
    type: DataTypes.INTEGER,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'Commissions'
});

const Withdrawal = sequelize.define('Withdrawal', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('amount');
      return value === null ? 0 : Number(value);
    }
  },
  alipayAccount: {
    type: DataTypes.STRING,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'completed'),
    defaultValue: 'pending'
  },
  agentId: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  approvedBy: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  approvedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  remarks: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'Withdrawals'
});

// 随机工具函数
const random = {
  // 生成订单号
  orderId: () => generateOrderNumber(),
  
  // 生成随机金额
  amount: (min = 100, max = 1000) => parseFloat((Math.random() * (max - min) + min).toFixed(2)),
  
  // 生成随机日期
  date: (daysAgo = 30) => {
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * daysAgo));
    return date;
  },
  
  // 生成随机手机号
  phone: () => `1${Math.floor(Math.random() * 9) + 1}${Array(9).fill(0).map(() => Math.floor(Math.random() * 10)).join('')}`,
  
  // 生成随机客户名
  name: () => {
    const surnames = ['张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴'];
    const names = ['明', '华', '强', '伟', '芳', '娜', '静', '秀', '丽', '军', '杰', '涛', '敏', '燕'];
    return surnames[Math.floor(Math.random() * surnames.length)] + names[Math.floor(Math.random() * names.length)];
  },
  
  // 从数组中随机选择一项
  pick: (array) => array[Math.floor(Math.random() * array.length)]
};

// 清理测试数据
async function cleanupTestData(agentIds) {
  log.info('清理之前的测试数据...');
  
  // 删除这些代理的提现记录
  await Withdrawal.destroy({
    where: {
      agentId: agentIds
    }
  });
  
  // 删除这些代理的佣金记录
  await Commission.destroy({
    where: {
      agentId: agentIds
    }
  });
  
  // 删除这些代理的订单
  await Order.destroy({
    where: {
      agentId: agentIds
    }
  });
  
  log.success('测试数据清理完成');
}

// 生成订单和佣金数据
async function generateOrdersAndCommissions(agents, products) {
  const ordersByAgent = {};
  
  for (const agent of agents) {
    log.info(`为代理 ${agent.username} (ID: ${agent.id}) 生成订单和佣金...`);
    ordersByAgent[agent.id] = [];
    
    // 为每个代理生成5-10个订单
    const orderCount = Math.floor(Math.random() * 6) + 5;
    
    for (let i = 0; i < orderCount; i++) {
      // 随机选择一个产品
      const product = random.pick(products);
      const orderAmount = random.amount();
      
      // 计算佣金
      const agentCommissionRate = agent.commissionRate / 100; // 转换为小数
      const agentCommission = parseFloat((orderAmount * agentCommissionRate).toFixed(2));
      
      let parentAgentCommission = 0;
      let parentAgentId = null;
      
      if (agent.parentAgentId) {
        const parentAgent = await User.findByPk(agent.parentAgentId);
        if (parentAgent) {
          parentAgentId = parentAgent.id;
          // 上级代理获得的是差额佣金
          const parentCommissionRate = (parentAgent.commissionRate - agent.commissionRate) / 100;
          if (parentCommissionRate > 0) {
            parentAgentCommission = parseFloat((orderAmount * parentCommissionRate).toFixed(2));
          }
        }
      }
      
      // 创建订单
      const order = await Order.create({
        id: random.orderId(),
        amount: orderAmount,
        status: 'paid', // 已支付状态
        customerName: random.name(),
        customerPhone: random.phone(),
        productId: product.id,
        agentId: agent.id,
        parentAgentId: parentAgentId,
        agentCommission: agentCommission,
        parentAgentCommission: parentAgentCommission,
        paidAt: random.date()
      });
      
      log.success(`创建订单: ${order.id}, 金额: ¥${orderAmount}, 代理佣金: ¥${agentCommission}`);
      ordersByAgent[agent.id].push(order);
      
      // 创建代理佣金记录
      const commission = await Commission.create({
        amount: agentCommission,
        status: Math.random() > 0.3 ? 'available' : 'pending', // 70%为可提现状态
        orderId: order.id,
        agentId: agent.id
      });
      
      log.success(`创建佣金记录: ID ${commission.id}, 金额: ¥${agentCommission}, 状态: ${commission.status}`);
      
      // 如果有上级代理，也创建上级代理的佣金记录
      if (parentAgentId && parentAgentCommission > 0) {
        const parentCommission = await Commission.create({
          amount: parentAgentCommission,
          status: Math.random() > 0.3 ? 'available' : 'pending',
          orderId: order.id,
          agentId: parentAgentId
        });
        
        log.success(`创建上级代理佣金记录: ID ${parentCommission.id}, 金额: ¥${parentAgentCommission}, 状态: ${parentCommission.status}`);
      }
    }
  }
  
  return ordersByAgent;
}

// 测试佣金提现功能
async function testWithdrawal(agent) {
  log.test(`测试代理 ${agent.username} 的佣金提现功能...`);
  
  // 获取可提现佣金总额
  const availableCommissions = await Commission.findAll({
    where: {
      agentId: agent.id,
      status: 'available'
    }
  });
  
  const totalAvailable = availableCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
  log.info(`代理 ${agent.username} 可提现佣金总额: ¥${totalAvailable.toFixed(2)}`);
  
  if (totalAvailable <= 0) {
    log.warning(`代理 ${agent.username} 没有可提现佣金，跳过提现测试`);
    return null;
  }
  
  // 提现一半的可用佣金
  const withdrawAmount = parseFloat((totalAvailable / 2).toFixed(2));
  
  try {
    // 创建提现记录
    const withdrawal = await Withdrawal.create({
      amount: withdrawAmount,
      alipayAccount: `${agent.username}@alipay.com`,
      agentId: agent.id,
      status: 'pending'
    });
    
    log.success(`成功创建提现记录，金额: ¥${withdrawAmount}`);
    
    // 更新佣金状态
    let remainingAmount = withdrawAmount;
    for (const commission of availableCommissions) {
      if (remainingAmount <= 0) break;
      
      const commissionAmount = Number(commission.amount);
      
      if (commissionAmount <= remainingAmount) {
        // 如果佣金金额小于等于剩余所需金额，全部提现
        await commission.update({
          status: 'withdrawn',
          withdrawalId: withdrawal.id
        });
        remainingAmount -= commissionAmount;
      } else {
        // 如果佣金金额大于剩余所需金额，需要拆分
        await commission.update({
          amount: commissionAmount - remainingAmount
        });
        
        // 创建新的已提现佣金记录
        await Commission.create({
          amount: remainingAmount,
          status: 'withdrawn',
          orderId: commission.orderId,
          agentId: commission.agentId,
          withdrawalId: withdrawal.id
        });
        
        remainingAmount = 0;
      }
    }
    
    // 验证佣金状态变更
    const withdrawnCommissions = await Commission.findAll({
      where: {
        agentId: agent.id,
        status: 'withdrawn',
        withdrawalId: withdrawal.id
      }
    });
    
    const totalWithdrawn = withdrawnCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
    
    if (Math.abs(totalWithdrawn - withdrawAmount) > 0.01) {
      log.error(`佣金状态更新错误: 期望 ¥${withdrawAmount}, 实际 ¥${totalWithdrawn}`);
    } else {
      log.success(`佣金状态更新正确: ¥${totalWithdrawn}`);
    }
    
    // 测试审核提现
    await withdrawal.update({
      status: 'approved',
      remarks: '测试审核通过',
      approvedAt: new Date()
    });
    
    log.success('提现审核通过');
    
    // 测试完成提现
    await withdrawal.update({
      status: 'completed',
      completedAt: new Date()
    });
    
    log.success('提现完成');
    
    return withdrawal;
  } catch (error) {
    log.error(`提现测试失败: ${error.message}`);
    return null;
  }
}

// 验证提现后的数据一致性
async function verifyDataConsistency(agent) {
  log.test(`验证代理 ${agent.username} 的数据一致性...`);
  
  // 获取所有佣金记录
  const allCommissions = await Commission.findAll({
    where: { agentId: agent.id }
  });
  
  // 按状态分类
  const commissionsByStatus = {
    pending: allCommissions.filter(c => c.status === 'pending'),
    available: allCommissions.filter(c => c.status === 'available'),
    withdrawn: allCommissions.filter(c => c.status === 'withdrawn')
  };
  
  // 计算各状态佣金总额
  const totalByStatus = {
    pending: commissionsByStatus.pending.reduce((sum, c) => sum + Number(c.amount), 0),
    available: commissionsByStatus.available.reduce((sum, c) => sum + Number(c.amount), 0),
    withdrawn: commissionsByStatus.withdrawn.reduce((sum, c) => sum + Number(c.amount), 0)
  };
  
  // 获取该代理的所有提现记录
  const withdrawals = await Withdrawal.findAll({
    where: { agentId: agent.id }
  });
  
  // 计算提现总额
  const totalWithdrawn = withdrawals.reduce((sum, w) => sum + Number(w.amount), 0);
  
  // 验证提现总额与已提现佣金总额是否一致
  if (Math.abs(totalWithdrawn - totalByStatus.withdrawn) > 0.01) {
    log.error(`数据不一致: 提现总额 ¥${totalWithdrawn} 与已提现佣金总额 ¥${totalByStatus.withdrawn} 不匹配`);
  } else {
    log.success(`数据一致: 提现总额 ¥${totalWithdrawn} 与已提现佣金总额 ¥${totalByStatus.withdrawn} 匹配`);
  }
  
  // 输出最终统计
  log.result(`\n代理 ${agent.username} 佣金统计:`);
  log.result(`- 总佣金: ¥${(totalByStatus.pending + totalByStatus.available + totalByStatus.withdrawn).toFixed(2)}`);
  log.result(`- 待结算佣金: ¥${totalByStatus.pending.toFixed(2)}`);
  log.result(`- 可提现佣金: ¥${totalByStatus.available.toFixed(2)}`);
  log.result(`- 已提现佣金: ¥${totalByStatus.withdrawn.toFixed(2)}`);
  log.result(`- 提现记录数: ${withdrawals.length}`);
}

// 主函数
async function main() {
  try {
    console.log('\n==================== 佣金系统测试 ====================\n');
    
    // 测试数据库连接
    log.info('测试数据库连接...');
    await sequelize.authenticate();
    log.success('数据库连接成功');
    
    // 1. 查找测试用户
    log.info('查找测试用户...');
    let agent1 = await User.findOne({ where: { username: 'agent1' } });
    let agent2 = await User.findOne({ where: { username: 'agent2' } });
    
    // 如果找不到用户，创建测试用户
    if (!agent1) {
      log.info('创建测试用户 agent1...');
      agent1 = await User.create({
        username: 'agent1',
        password: '$2a$10$JvHgiA6F1BkKRU5xJ5JYQ.tz0Uu.JqZhiuqkAhHWIRMTEYJtrTNkO', // password123
        name: '测试代理1',
        phone: '13800138001',
        role: 'user',
        level: 1,
        status: 'approved',
        commissionRate: 80.00
      });
      log.success(`创建成功: ${agent1.username} (ID: ${agent1.id})`);
    } else {
      log.success(`找到测试用户: ${agent1.username} (ID: ${agent1.id})`);
    }
    
    if (!agent2) {
      log.info('创建测试用户 agent2...');
      agent2 = await User.create({
        username: 'agent2',
        password: '$2a$10$JvHgiA6F1BkKRU5xJ5JYQ.tz0Uu.JqZhiuqkAhHWIRMTEYJtrTNkO', // password123
        name: '测试代理2',
        phone: '13800138002',
        role: 'user',
        level: 1,
        status: 'approved',
        commissionRate: 70.00,
        parentAgentId: agent1.id
      });
      log.success(`创建成功: ${agent2.username} (ID: ${agent2.id})`);
    } else {
      log.success(`找到测试用户: ${agent2.username} (ID: ${agent2.id})`);
    }
    
    const testAgents = [agent1, agent2];
    
    // 2. 查找可用的产品
    log.info('查找可用产品...');
    let products = await Product.findAll({ where: { status: 'active' } });
    
    if (products.length === 0) {
      log.warning('找不到可用的产品，创建测试产品...');
      
      // 创建测试产品
      const product1 = await Product.create({
        title: '测试产品1',
        description: '这是一个用于测试的产品',
        price: 199.99,
        baseCommissionRate: 0.8,
        status: 'active'
      });
      
      const product2 = await Product.create({
        title: '测试产品2',
        description: '这是另一个用于测试的产品',
        price: 299.99,
        baseCommissionRate: 0.7,
        status: 'active'
      });
      
      products = [product1, product2];
      log.success(`创建了 ${products.length} 个测试产品`);
    } else {
      log.success(`找到 ${products.length} 个可用产品`);
    }
    
    // 3. 清理之前的测试数据
    await cleanupTestData(testAgents.map(agent => agent.id));
    
    // 4. 生成订单和佣金数据
    await generateOrdersAndCommissions(testAgents, products);
    
    // 5. 测试佣金提现功能
    for (const agent of testAgents) {
      await testWithdrawal(agent);
    }
    
    // 6. 验证提现后的数据一致性
    for (const agent of testAgents) {
      await verifyDataConsistency(agent);
    }
    
    console.log('\n==================== 测试完成 ====================\n');
    
  } catch (error) {
    log.error(`测试过程中出错: ${error.message}`);
    console.error(error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行主函数
main();
