export const up = async (queryInterface, Sequelize) => {
  // 添加起名业务相关的系统配置
  return queryInterface.bulkInsert('SystemConfigs', [
    {
      key: 'naming_service_min_price',
      value: '99',
      description: '起名业务最低价格（元）',
      group: 'naming',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      key: 'naming_service_max_price',
      value: '299',
      description: '起名业务最高价格（元）',
      group: 'naming',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      key: 'naming_service_commission_rate',
      value: '20',
      description: '起名业务佣金比例（%）',
      group: 'naming',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      key: 'naming_service_enabled',
      value: 'true',
      description: '是否启用起名业务',
      group: 'naming',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]);
};

export const down = async (queryInterface, Sequelize) => {
  // 删除起名业务相关的系统配置
  return queryInterface.bulkDelete('SystemConfigs', {
    key: {
      [Sequelize.Op.in]: [
        'naming_service_min_price',
        'naming_service_max_price', 
        'naming_service_commission_rate',
        'naming_service_enabled'
      ]
    }
  });
};
