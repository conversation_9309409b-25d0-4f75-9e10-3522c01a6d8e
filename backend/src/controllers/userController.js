import { User, Order } from '../models/index.js';
import { Op } from 'sequelize';
import sequelize from '../config/database.js';
import { generateInviteLink } from '../utils/inviteLinkUtils.js';

// 获取用户列表
export const getUsers = async (req, res) => {
  try {
    const { status, page = 1, pageSize = 50 } = req.query; // 增大默认分页大小为50
    // 不再使用请求头中的域名，让inviteLinkUtils自动选择正确的域名
    
    const where = {};
    if (status) {
      where.status = status;
    }

    const offset = (page - 1) * pageSize;
    
    // 使用子查询获取每个用户的订单数
    const userOrderCounts = await Order.findAll({
      attributes: [
        ['agentId', 'agentId'],
        [sequelize.fn('COUNT', sequelize.col('Order.id')), 'orderCount']
      ],
      group: ['Order.agentId'],
      raw: true
    });

    // 创建订单数映射
    const orderCountMap = {};
    userOrderCounts.forEach(count => {
      orderCountMap[count.agentId] = parseInt(count.orderCount);
    });

    // 获取用户列表
    const { count, rows } = await User.findAndCountAll({
      where,
      attributes: [
        'id', 'username', 'name', 'phone', 'status', 'role', 
        'level', 'createdAt', 'parentAgentId', 'commissionRate'
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(pageSize)
    });

    // 为每个用户生成邀请链接
    const usersWithOrderCount = rows.map(user => {
      const userData = user.toJSON();
      // 只为管理员生成邀请链接
      if (userData.role === 'admin' && userData.status === 'approved') {
        // 只传递邀请人ID和角色，不再传递baseUrl参数
        userData.inviteLink = generateInviteLink(userData.id, userData.role);
      }
      return {
        ...userData,
        orderCount: orderCountMap[userData.id] || 0
      };
    });

    res.json({
      total: count,
      users: usersWithOrderCount,
      currentPage: parseInt(page),
      totalPages: Math.ceil(count / pageSize)
    });
  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({ message: '获取用户列表失败' });
  }
};

// 更新用户信息
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reason, commissionRate } = req.body;

    const user = await User.findByPk(id);
    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 如果是停用用户，检查是否有未完成的订单
    if (status === 'disabled') {
      const pendingOrders = await Order.count({
        where: {
          agentId: id,
          status: {
            [Op.in]: ['pending', 'processing']
          }
        }
      });

      if (pendingOrders > 0) {
        return res.status(400).json({ 
          message: '该用户还有未完成的订单，无法停用' 
        });
      }
    }

    // 准备更新的数据
    const updateData = {};
    
    // 如果提供了状态，则更新状态
    if (status) {
      updateData.status = status;
      updateData.statusReason = reason;
    }

    // 如果提供了佣金比例，则更新佣金比例
    if (commissionRate !== undefined) {
      // 验证佣金比例是否在有效范围内
      if (commissionRate < 0 || commissionRate > 100) {
        return res.status(400).json({ 
          message: '佣金比例必须在0-100之间' 
        });
      }
      updateData.commissionRate = commissionRate;
    }

    
    // 如果是通过审核且没有设置佣金比例，则设置默认佣金比例
    if (status === 'approved' && !user.commissionRate && commissionRate === undefined) {
      // 一级代理默认80%,二级代理默认60%
      updateData.commissionRate = user.level === 1 ? 0.8 : 0.6;
    }

    await user.update(updateData);

    // 获取该用户的订单数
    const orderCount = await Order.count({
      where: { agentId: id }
    });

    res.json({ 
      message: '用户信息更新成功', 
      user: {
        ...user.toJSON(),
        orderCount
      }
    });
  } catch (error) {
    console.error('更新用户信息失败:', error);
    res.status(500).json({ message: '更新用户信息失败' });
  }
};

// 搜索用户
export const searchUsers = async (req, res) => {
  try {
    const { query, page = 1, pageSize = 10 } = req.query;
    
    const where = {
      [Op.or]: [
        { username: { [Op.like]: `%${query}%` } },
        { name: { [Op.like]: `%${query}%` } },
        { phone: { [Op.like]: `%${query}%` } }
      ]
    };

    const offset = (page - 1) * pageSize;

    // 使用子查询获取每个用户的订单数
    const userOrderCounts = await Order.findAll({
      attributes: [
        ['agentId', 'agentId'],
        [sequelize.fn('COUNT', sequelize.col('Order.id')), 'orderCount']
      ],
      group: ['Order.agentId'],
      raw: true
    });

    const orderCountMap = {};
    userOrderCounts.forEach(count => {
      orderCountMap[count.agentId] = parseInt(count.orderCount);
    });
    
    const { count, rows } = await User.findAndCountAll({
      where,
      attributes: [
        'id', 'username', 'name', 'phone', 'status', 'role', 
        'level', 'createdAt', 'parentAgentId', 'commissionRate'
      ],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(pageSize)
    });

    const usersWithOrderCount = rows.map(user => ({
      ...user.toJSON(),
      orderCount: orderCountMap[user.id] || 0
    }));

    res.json({
      total: count,
      users: usersWithOrderCount,
      currentPage: parseInt(page),
      totalPages: Math.ceil(count / pageSize)
    });
  } catch (error) {
    console.error('搜索用户失败:', error);
    res.status(500).json({ message: '搜索用户失败' });
  }
};

// 获取佣金统计
export const getCommissionStats = async (req, res) => {
  try {
    const user = req.user;
    
    // 只有一级代理可以查看佣金统计
    if (user.level !== 1) {
      return res.status(403).json({ 
        message: 'Only level-1 agents can view commission statistics' 
      });
    }

    // 查询当前用户的下级代理
    const subAgents = await User.findAll({
      where: { 
        parentAgentId: user.id,
        level: 2
      },
      attributes: ['id', 'name', 'commissionRate']
    });

    // 如果没有下级代理，返回空数据
    if (!subAgents || subAgents.length === 0) {
      return res.json({
        commissions: [],
        totalCommission: 0
      });
    }

    const subAgentIds = subAgents.map(agent => agent.id);

    // 查询下级代理的订单
    const orders = await Order.findAll({
      where: {
        agentId: subAgentIds,
        status: 'paid'  // 只计算已支付的订单
      },
      include: [
        {
          model: User,
          as: 'orderAgent',
          attributes: ['id', 'name', 'commissionRate']
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: 100
    });

    // 格式化佣金数据
    const commissions = orders.map(order => {
      const commissionRate = order.orderAgent.commissionRate || 0;
      const commissionAmount = parseFloat(((order.amount * commissionRate) / 100).toFixed(2));
      
      return {
        id: order.id,
        orderId: order.id,
        orderAmount: order.amount,
        agentName: order.orderAgent.name,
        commissionRate: commissionRate,
        commissionAmount: commissionAmount,
        status: order.status,
        createdAt: order.createdAt
      };
    });
    
    // 计算总佣金
    const totalCommission = commissions.reduce((sum, item) => 
      sum + (item.commissionAmount || 0), 0
    );
    
    res.json({
      commissions,
      totalCommission: parseFloat(totalCommission.toFixed(2))
    });
  } catch (error) {
    console.error('获取佣金统计失败:', error);
    res.status(500).json({ message: error.message });
  }
};
