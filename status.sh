#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 定义变量
APP_NAME="commission-platform"
PM2_APP_NAME="commission-platform-api"

echo -e "${BLUE}========== 系统状态检查 ===========${NC}"

# 检查系统资源
echo -e "${BLUE}===== 系统资源 =====${NC}"
echo -e "${GREEN}CPU使用率:${NC}"
top -bn1 | grep "Cpu(s)" | awk '{print $2 + $4 "%"}'

echo -e "${GREEN}内存使用:${NC}"
free -h | grep Mem

echo -e "${GREEN}磁盘使用:${NC}"
df -h | grep -E '/$|/var'

# 检查PM2进程
echo -e "${BLUE}===== PM2进程状态 =====${NC}"
pm2 status $PM2_APP_NAME

# 检查Nginx状态
echo -e "${BLUE}===== Nginx状态 =====${NC}"
if systemctl is-active --quiet nginx; then
  echo -e "${GREEN}Nginx: 运行中${NC}"
else
  echo -e "${RED}Nginx: 已停止${NC}"
fi

# 检查端口监听
echo -e "${BLUE}===== 监听端口 =====${NC}"
echo -e "${GREEN}HTTP/HTTPS 端口:${NC}"
sudo netstat -tlnp | grep -E ':80|:443'

echo -e "${GREEN}后端 API 端口:${NC}"
sudo netstat -tlnp | grep ':3005'

# 检查最近的错误日志
echo -e "${BLUE}===== 最近错误日志 =====${NC}"
echo -e "${GREEN}Nginx 错误日志 (最近 5 行):${NC}"
sudo tail -n 5 /var/log/nginx/${APP_NAME}.error.log 2>/dev/null || echo "日志文件不存在"

echo -e "${GREEN}PM2 错误日志 (最近 5 行):${NC}"
tail -n 5 ~/.pm2/logs/${PM2_APP_NAME}-error.log 2>/dev/null || echo "日志文件不存在"

# 检查SSL证书
echo -e "${BLUE}===== SSL证书状态 =====${NC}"
if command -v certbot > /dev/null && sudo ls /etc/letsencrypt/live/ > /dev/null 2>&1; then
  sudo certbot certificates | grep -A 2 "Certificate Name:"
else
  echo -e "${YELLOW}未找到SSL证书或Certbot不可用${NC}"
fi

echo -e "${BLUE}========== 检查完成 ===========${NC}"
