import React, { useState } from 'react';
import { Modal } from './ui/Modal';

interface PickerColumnProps {
  options: Array<{ label: string; value: string }>;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
}

const PickerColumn: React.FC<PickerColumnProps> = ({ options, value, onChange, disabled = false }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [scrollTop, setScrollTop] = useState(0);

  const itemHeight = 44;
  const visibleItems = 5;
  const containerHeight = itemHeight * visibleItems;

  // 计算当前选中项的索引
  const selectedIndex = options.findIndex(option => option.value === value);
  const currentScrollTop = Math.max(0, selectedIndex * itemHeight - (containerHeight - itemHeight) / 2);

  React.useEffect(() => {
    setScrollTop(currentScrollTop);
  }, [currentScrollTop]);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (disabled) return;
    setIsDragging(true);
    setStartY(e.clientY);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || disabled) return;

    const deltaY = e.clientY - startY;
    const newScrollTop = Math.max(0, Math.min(scrollTop - deltaY, Math.max(0, (options.length - 1) * itemHeight)));
    setScrollTop(newScrollTop);
  };

  const handleMouseUp = () => {
    if (!isDragging || disabled) return;

    setIsDragging(false);

    // 计算最接近的项目
    const nearestIndex = Math.round(scrollTop / itemHeight);
    const clampedIndex = Math.max(0, Math.min(nearestIndex, options.length - 1));

    if (options[clampedIndex]) {
      onChange(options[clampedIndex].value);
    }
  };

  const handleItemClick = (option: { label: string; value: string }) => {
    if (!disabled) {
      onChange(option.value);
    }
  };

  return (
    <div className="van-picker-column" style={{ flex: 1 }}>
      <div
        className="van-picker-column__wrapper"
        style={{
          height: `${containerHeight}px`,
          overflow: 'hidden',
          position: 'relative',
          cursor: disabled ? 'not-allowed' : (isDragging ? 'grabbing' : 'grab'),
          opacity: disabled ? 0.5 : 1
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* 选中区域指示器 */}
        <div
          style={{
            position: 'absolute',
            top: `${(containerHeight - itemHeight) / 2}px`,
            left: 0,
            right: 0,
            height: `${itemHeight}px`,
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
            borderTop: '1px solid #ebedf0',
            borderBottom: '1px solid #ebedf0',
            pointerEvents: 'none',
            zIndex: 1
          }}
        />

        {/* 选项列表 */}
        <div
          style={{
            transform: `translateY(-${scrollTop}px)`,
            transition: isDragging ? 'none' : 'transform 0.3s ease',
            paddingTop: `${(containerHeight - itemHeight) / 2}px`,
            paddingBottom: `${(containerHeight - itemHeight) / 2}px`
          }}
        >
          {options.map((option, index) => {
            const isSelected = option.value === value;
            const distance = Math.abs(index * itemHeight - scrollTop - (containerHeight - itemHeight) / 2);
            const opacity = disabled ? 0.5 : Math.max(0.3, 1 - distance / (itemHeight * 2));

            return (
              <div
                key={option.value}
                className={`van-picker-column__item ${isSelected ? 'van-picker-column__item--selected' : ''} ${disabled ? 'van-picker-column__item--disabled' : ''}`}
                style={{
                  height: `${itemHeight}px`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '16px',
                  color: disabled ? '#c8c9cc' : (isSelected ? '#323233' : '#969799'),
                  fontWeight: isSelected ? '500' : 'normal',
                  cursor: disabled ? 'not-allowed' : 'pointer',
                  opacity,
                  transition: isDragging ? 'none' : 'opacity 0.3s ease'
                }}
                onClick={() => handleItemClick(option)}
              >
                {option.label}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

// 简化的地区数据（实际项目中应该使用完整的地区数据）
const areaData = {
  '110000': {
    name: '北京市',
    children: {
      '110100': {
        name: '北京市',
        children: {
          '110101': { name: '东城区' },
          '110102': { name: '西城区' },
          '110105': { name: '朝阳区' },
          '110106': { name: '丰台区' },
          '110107': { name: '石景山区' },
          '110108': { name: '海淀区' },
        }
      }
    }
  },
  '310000': {
    name: '上海市',
    children: {
      '310100': {
        name: '上海市',
        children: {
          '310101': { name: '黄浦区' },
          '310104': { name: '徐汇区' },
          '310105': { name: '长宁区' },
          '310106': { name: '静安区' },
          '310107': { name: '普陀区' },
          '310109': { name: '虹口区' },
        }
      }
    }
  },
  '440000': {
    name: '广东省',
    children: {
      '440100': {
        name: '广州市',
        children: {
          '440103': { name: '荔湾区' },
          '440104': { name: '越秀区' },
          '440105': { name: '海珠区' },
          '440106': { name: '天河区' },
          '440111': { name: '白云区' },
        }
      },
      '440300': {
        name: '深圳市',
        children: {
          '440303': { name: '罗湖区' },
          '440304': { name: '福田区' },
          '440305': { name: '南山区' },
          '440306': { name: '宝安区' },
          '440307': { name: '龙岗区' },
        }
      }
    }
  }
};

interface AreaPickerProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (selectedOptions: Array<{ text: string; value: string }>) => void;
}

export const AreaPicker: React.FC<AreaPickerProps> = ({
  isOpen,
  onClose,
  onConfirm,
}) => {
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [selectedCity, setSelectedCity] = useState<string>('');
  const [selectedDistrict, setSelectedDistrict] = useState<string>('');

  const provinces = Object.entries(areaData);
  const cities = selectedProvince ? Object.entries(areaData[selectedProvince]?.children || {}) : [];
  const districts = selectedCity ? Object.entries(areaData[selectedProvince]?.children?.[selectedCity]?.children || {}) : [];

  const handleConfirm = () => {
    if (!selectedProvince || !selectedCity || !selectedDistrict) {
      return;
    }

    const selectedOptions = [
      {
        text: areaData[selectedProvince].name,
        value: selectedProvince,
      },
      {
        text: areaData[selectedProvince].children[selectedCity].name,
        value: selectedCity,
      },
      {
        text: areaData[selectedProvince].children[selectedCity].children[selectedDistrict].name,
        value: selectedDistrict,
      },
    ];

    onConfirm(selectedOptions);
  };

  const handleProvinceChange = (provinceCode: string) => {
    setSelectedProvince(provinceCode);
    setSelectedCity('');
    setSelectedDistrict('');
  };

  const handleCityChange = (cityCode: string) => {
    setSelectedCity(cityCode);
    setSelectedDistrict('');
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} position="bottom">
      <div className="van-popup" style={{ backgroundColor: 'white', borderRadius: '16px 16px 0 0' }}>
        <div className="van-picker__toolbar">
          <button
            className="van-picker__cancel"
            onClick={onClose}
          >
            取消
          </button>
          <div className="van-picker__title">
            选择地区
          </div>
          <button
            className="van-picker__confirm"
            style={{
              color: !selectedProvince || !selectedCity || !selectedDistrict ? '#c8c9cc' : '#1989fa',
            }}
            onClick={handleConfirm}
            disabled={!selectedProvince || !selectedCity || !selectedDistrict}
          >
            确认
          </button>
        </div>

        <div className="van-picker__columns" style={{ display: 'flex', height: '216px' }}>
          {/* 省份 */}
          <PickerColumn
            options={[
              { label: '请选择省/市', value: '' },
              ...provinces.map(([code, province]) => ({ label: province.name, value: code }))
            ]}
            value={selectedProvince}
            onChange={handleProvinceChange}
          />

          {/* 城市 */}
          <PickerColumn
            options={[
              { label: '请选择市', value: '' },
              ...cities.map(([code, city]) => ({ label: city.name, value: code }))
            ]}
            value={selectedCity}
            onChange={handleCityChange}
            disabled={!selectedProvince}
          />

          {/* 区县 */}
          <PickerColumn
            options={[
              { label: '请选择区/县', value: '' },
              ...districts.map(([code, district]) => ({ label: district.name, value: code }))
            ]}
            value={selectedDistrict}
            onChange={setSelectedDistrict}
            disabled={!selectedCity}
          />
        </div>
      </div>
    </Modal>
  );
};
