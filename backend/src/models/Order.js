import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';
import { generateOrderNumber } from '../utils/orderUtils.js';

const Order = sequelize.define('Order', {
  id: {
    type: DataTypes.STRING(20),
    primaryKey: true,
    allowNull: false,
    defaultValue: () => generateOrderNumber(),
    comment: '订单号：年份后两位(2) + 月日时分秒(10) + 随机数(4)'
  },
  orderNumber: {
    type: DataTypes.STRING(50),
    allowNull: true,
    unique: true,
    comment: 'Human readable order number'
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('amount');
      return value === null ? null : Number(value);
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    allowNull: false,
    comment: 'Order quantity'
  },
  unitPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Unit price of the product',
    get() {
      const value = this.getDataValue('unitPrice');
      return value === null ? null : Number(value);
    }
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Total amount of the order',
    get() {
      const value = this.getDataValue('totalAmount');
      return value === null ? null : Number(value);
    }
  },
  status: {
    type: DataTypes.ENUM('pending', 'paid', 'cancelled', 'completed'),
    defaultValue: 'pending'
  },
  orderType: {
    type: DataTypes.ENUM('physical', 'service'),
    defaultValue: 'physical',
    allowNull: false,
    comment: 'Order type: physical for goods, service for services like bazi query'
  },
  customerName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customerPhone: {
    type: DataTypes.STRING,
    allowNull: true
  },
  customerAddress: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Customer address'
  },
  deviceId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '设备标识，用于防止短时间内重复下单'
  },
  clientIp: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '客户端IP地址，用于防止短时间内重复下单'
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Products',
      key: 'id'
    }
  },
  agentId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  parentAgentId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  productLinkId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'ProductLinks',
      key: 'id'
    }
  },
  agentCommission: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    get() {
      const value = this.getDataValue('agentCommission');
      return value === null ? 0 : Number(value);
    }
  },
  parentAgentCommission: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    get() {
      const value = this.getDataValue('parentAgentCommission');
      return value === null ? 0 : Number(value);
    }
  },
  adminCommission: {
    type: DataTypes.DECIMAL(10, 2),
    defaultValue: 0,
    comment: '管理员佣金',
    get() {
      const value = this.getDataValue('adminCommission');
      return value === null ? 0 : Number(value);
    }
  },
  paidAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  timestamps: true,
  tableName: 'Orders'
});

export default Order;