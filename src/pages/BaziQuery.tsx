import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { message, Spin } from 'antd';
import { motion } from 'framer-motion';
import { Field } from '../components/ui/Field';
import { Radio, RadioGroup } from '../components/ui/Radio';
import { CalendarTypeSelector } from '../components/CalendarTypeSelector';
import { DateTimeSelector } from '../components/DateTimeSelector';
import { AreaSelector } from '../components/AreaSelector';
import { TopSection2 } from '../components/TopSection2';
import { ImageBlock2 } from '../components/ImageBlock2';
import { Marquee } from '../components/Marquee';
import { useBaziStore } from '../store/baziStore';
import BaziReportEnhanced from '../components/BaziReportEnhanced';
import { BaZiQueryFormValues, BaZiApiParams, BaZiReportData } from '../types/bazi';
import { format } from 'date-fns';
import axios from 'axios';
import '../styles/BaziReportEnhanced.css';

// 导入图片
const a0 = '/assets/theme/2/0.png';

interface Product {
  id: number;
  name: string;
  price: number;
  description: string;
  type: string;
}

const BaziQuery: React.FC = () => {
  const { code } = useParams<{ code: string }>();
  const navigate = useNavigate();

  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState<BaZiReportData | null>(null);
  const [currentStep, setCurrentStep] = useState<'form' | 'payment' | 'report'>('form');

  // 使用 bz 风格的状态管理
  const {
    formData,
    setFormData,
    showCalendarTypeSelector,
    showDateTimeSelector,
    showAreaSelector,
    selectedCalendarType,
    submitting,
    setShowCalendarTypeSelector,
    setShowDateTimeSelector,
    setShowAreaSelector,
    setSelectedCalendarType,
    setDateTime,
    setArea,
    setSubmitting
  } = useBaziStore();

  // 获取产品信息
  useEffect(() => {
    const fetchProduct = async () => {
      if (!code) {
        message.error('缺少产品链接代码');
        navigate('/');
        return;
      }

      try {
        const response = await axios.get(`/api/product-links/${code}`);
        const productData = response.data.data;

        if (productData.product.type !== 'service' || !productData.product.name.includes('八字')) {
          message.error('此链接不支持八字查询服务');
          navigate('/');
          return;
        }

        setProduct(productData.product);
      } catch (error) {
        console.error('获取产品信息失败:', error);
        message.error('获取产品信息失败');
        navigate('/');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();

    // 设置页面背景色
    document.body.style.background = '#864a30';

    return () => {
      document.body.removeAttribute('style');
    };
  }, [code, navigate]);

  // 处理公历/农历选择
  const handleCalendarTypeSelect = (type: 'solar' | 'lunar') => {
    setSelectedCalendarType(type);
    setShowCalendarTypeSelector(false);
    setShowDateTimeSelector(true);
  };

  // 处理日期时间选择
  const handleDateTimeConfirm = (data: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  }) => {
    setDateTime(data);
    setShowDateTimeSelector(false);
  };

  // 处理地区选择
  const handleAreaConfirm = (data: { province: string; city: string; district?: string }) => {
    setArea(data);
    setShowAreaSelector(false);
  };

  // 验证表单数据
  const validateForm = () => {
    if (!formData.name.trim()) {
      message.error('请输入姓名');
      return false;
    }

    if (!formData.birthday) {
      message.error('请选择生日');
      return false;
    }

    if (!formData.address) {
      message.error('请选择地区');
      return false;
    }

    return true;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // 确认对话框
    const confirmed = window.confirm(`
请确认生日是否正确！

姓名：${formData.name}
性别：${formData.sex === '1' ? '男' : '女'}
生日：${formData.birthday}
出生地址：${formData.address}
    `);

    if (!confirmed) {
      return;
    }

    if (!code) {
      message.error('缺少产品代码');
      return;
    }

    setSubmitting(true);

    try {
      // 构建订单数据
      const requestData = {
        name: formData.name,
        gender: formData.sex,
        calendarType: formData.yearType,
        birthYear: formData.dateTime?.year.toString() || '',
        birthMonth: formData.dateTime?.month.toString().padStart(2, '0') || '',
        birthDay: formData.dateTime?.day.toString().padStart(2, '0') || '',
        birthHour: formData.dateTime?.hour || 0,
        birthMinute: formData.dateTime?.minute || 0,
        birthProvince: formData.area?.province || '',
        birthCity: formData.area?.city || '',
        customerPhone: formData.phone || '',
        deviceId: 'web-' + Date.now(),
        clientIp: '127.0.0.1'
      };

      console.log('创建八字订单，请求数据:', requestData);

      const response = await axios.post(`/api/orders/purchase/${code}`, requestData);

      console.log('订单创建成功:', response.data);

      if (response.data.paymentUrl) {
        message.success('订单创建成功，正在跳转到支付页面...');

        setTimeout(() => {
          window.location.href = response.data.paymentUrl;
        }, 1000);
      } else {
        throw new Error('未获取到支付链接');
      }

    } catch (error) {
      console.error('创建订单失败:', error);

      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || '创建订单失败';
        message.error(errorMessage);
      } else {
        message.error('创建订单失败，请重试');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 获取八字报告（从URL参数或订单ID获取）
  const fetchBaziReport = async (orderId: string) => {
    try {
      const response = await axios.get(`/api/bazi/orders/${orderId}/report`);
      setReportData(response.data.data.reportData);
      setCurrentStep('report');
    } catch (error) {
      console.error('获取八字报告失败:', error);
      message.error('获取八字报告失败');
    }
  };

  // 检查是否从支付成功页面跳转过来
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('orderId');
    const paymentSuccess = urlParams.get('paymentSuccess');
    
    if (orderId && paymentSuccess === 'true') {
      setCurrentStep('report');
      fetchBaziReport(orderId);
    }
  }, []);

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh' 
      }}>
        <Spin size="large" tip="正在加载产品信息..." />
      </div>
    );
  }

  if (!product) {
    return null;
  }

  return (
    <div className="page home2">
      {currentStep === 'form' && (
        <>
          {/* 跑马灯 */}
          <Marquee
            productName={product.name}
            productPrice={product.price}
            productDescription={product.description}
          />

          {/* Banner图片 */}
          <img className="banner" src={a0} alt="Banner" style={{ width: '100%', display: 'block' }} />

          {/* 顶部罗盘动画 */}
          <TopSection2 />

          {/* 表单区域 */}
          <section className="block" style={{ paddingTop: 0 }}>
            <div className="main-form">
              <div className="inner">
                {/* 姓名输入 */}
                <Field
                  label="姓名"
                  value={formData.name}
                  onChange={(value) => setFormData({ ...formData, name: value })}
                  placeholder="请输入您的姓名"
                  required
                />

                {/* 性别选择 */}
                <div className="field-group">
                  <label className="field-label">性别</label>
                  <RadioGroup
                    value={formData.sex}
                    onChange={(value) => setFormData({ ...formData, sex: value })}
                  >
                    <Radio value="1">男</Radio>
                    <Radio value="0">女</Radio>
                  </RadioGroup>
                </div>

                {/* 生日选择 */}
                <div className="field-group">
                  <label className="field-label">生日</label>
                  <div
                    className="field-input clickable"
                    onClick={() => setShowCalendarTypeSelector(true)}
                  >
                    {formData.birthday || '请选择生日'}
                  </div>
                </div>

                {/* 出生地区选择 */}
                <div className="field-group">
                  <label className="field-label">出生地区</label>
                  <div
                    className="field-input clickable"
                    onClick={() => setShowAreaSelector(true)}
                  >
                    {formData.address || '请选择出生地区'}
                  </div>
                </div>

                {/* 手机号输入 */}
                <Field
                  label="手机号"
                  value={formData.phone}
                  onChange={(value) => setFormData({ ...formData, phone: value })}
                  placeholder="请输入手机号（选填）"
                />

                {/* 提交按钮 */}
                <motion.button
                  className="submit-button"
                  onClick={handleSubmit}
                  disabled={submitting}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  style={{
                    background: 'url("/assets/theme/2/6.png") no-repeat center',
                    backgroundSize: '90% auto',
                    animation: 'scale-animate 1.8s ease-in-out infinite',
                    height: '60px',
                    border: 'none',
                    color: 'transparent',
                    cursor: submitting ? 'not-allowed' : 'pointer',
                    opacity: submitting ? 0.7 : 1
                  }}
                >
                  {submitting ? '提交中...' : '立即测算'}
                </motion.button>
              </div>
            </div>
          </section>

          {/* 图片展示区域 */}
          <ImageBlock2 />

          <style jsx>{`
            @keyframes marquee {
              0% { transform: translateX(100%); }
              100% { transform: translateX(-100%); }
            }
            @keyframes scale-animate {
              from {
                transform: scale3d(1, 1, 1);
              }
              50% {
                transform: scale3d(0.9, 0.9, 0.9);
              }
              to {
                transform: scale3d(1, 1, 1);
              }
            }
          `}</style>
        </>
      )}

      {currentStep === 'report' && reportData && (
        <BaziReportEnhanced
          data={reportData}
          customerName={reportData.命主信息.姓名}
        />
      )}

      {/* 选择器组件 */}
      <CalendarTypeSelector
        isOpen={showCalendarTypeSelector}
        onClose={() => setShowCalendarTypeSelector(false)}
        onSelect={handleCalendarTypeSelect}
      />

      <DateTimeSelector
        isOpen={showDateTimeSelector}
        onClose={() => setShowDateTimeSelector(false)}
        onConfirm={handleDateTimeConfirm}
        calendarType={selectedCalendarType}
      />

      <AreaSelector
        isOpen={showAreaSelector}
        onClose={() => setShowAreaSelector(false)}
        onConfirm={handleAreaConfirm}
      />
    </div>
  );
};

export default BaziQuery; 