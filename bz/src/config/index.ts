// 环境配置
export const env = {
  isDev: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,
};

// API 配置
export const API_BASE_URL = env.isDev 
  ? 'http://localhost:9600' 
  : `${location.protocol}//api-${location.hostname}`;

// 正则表达式
export const regChineseName = /^[\u4e00-\u9fa5]{2,8}$/;
export const regTel = /^1[3-9]\d{9}$/;

// 应用配置
export const AppMap = {
  bigdata: 103,
  fantasy: 200,
};

// 交易状态
export const TradeStatus = {
  created: 0,
  paid: 1,
  success: 2,
  failed: 3,
  refund: 4,
  closed: 5,
  finished: 6,
};

// 默认表单数据（开发环境）
export const defaultFormData = env.isDev ? {
  name: '张三',
  sex: '1' as const,
  address: '北京市北京市东城区',
} : {};

// 关系选项
export const relationOptions = {
  fragment: ['本人', '孩子', '更多'],
  full: ['本人', '恋人', '夫妻', '孩子', '父母', '亲戚', '朋友', '其他'],
};

// 年份类型选项
export const yearTypeOptions = [
  { name: '使用公历生日', subname: '点击选择此选项将使用公历生日进行测算' },
  { name: '使用农历生日', subname: '点击选择此选项将使用农历生日进行测算' },
];
