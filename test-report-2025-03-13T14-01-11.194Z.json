{"timestamp": "2025-03-13T14:01:11.194Z", "summary": {"total": 7, "passed": 0, "failed": 7, "skipped": 0}, "details": [{"name": "用户登录 - 管理员", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "Invalid credentials"}}, {"name": "用户登录 - 普通用户", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "Invalid credentials"}}, {"name": "获取个人资料", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "浏览产品列表", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "查询佣金记录", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "查询提现记录", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "API健康检查", "result": "failed", "error": "API健康检查失败: Request failed with status code 401"}], "config": {"baseURL": "https://ye.bzcy.xyz", "includeAdmin": false}}