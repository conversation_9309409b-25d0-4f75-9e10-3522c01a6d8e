// 增强的省市区数据，包含经纬度和时区信息

export interface LocationData {
  name: string;
  code: string;
  children?: LocationData[];
  longitude?: number;
  latitude?: number;
  timezone?: string;
}

export const enhancedLocationData: LocationData[] = [
  {
    name: '北京市',
    code: '110000',
    longitude: 116.4074,
    latitude: 39.9042,
    timezone: 'Asia/Shanghai',
    children: [
      { name: '东城区', code: '110101', longitude: 116.4074, latitude: 39.9042 },
      { name: '西城区', code: '110102', longitude: 116.3662, latitude: 39.9097 },
      { name: '朝阳区', code: '110105', longitude: 116.4435, latitude: 39.9211 },
      { name: '丰台区', code: '110106', longitude: 116.2868, latitude: 39.8583 },
      { name: '石景山区', code: '110107', longitude: 116.1953, latitude: 39.9063 },
      { name: '海淀区', code: '110108', longitude: 116.2988, latitude: 39.9593 },
      { name: '门头沟区', code: '110109', longitude: 116.1057, latitude: 39.9375 },
      { name: '房山区', code: '110111', longitude: 115.9882, latitude: 39.7489 },
      { name: '通州区', code: '110112', longitude: 116.6562, latitude: 39.9026 },
      { name: '顺义区', code: '110113', longitude: 116.6544, latitude: 40.1280 },
      { name: '昌平区', code: '110114', longitude: 116.2317, latitude: 40.2206 },
      { name: '大兴区', code: '110115', longitude: 116.3385, latitude: 39.7288 },
      { name: '怀柔区', code: '110116', longitude: 116.6317, latitude: 40.3242 },
      { name: '平谷区', code: '110117', longitude: 117.1219, latitude: 40.1445 },
      { name: '密云区', code: '110118', longitude: 116.8432, latitude: 40.3769 },
      { name: '延庆区', code: '110119', longitude: 115.9851, latitude: 40.4651 }
    ]
  },
  {
    name: '上海市',
    code: '310000',
    longitude: 121.4737,
    latitude: 31.2304,
    timezone: 'Asia/Shanghai',
    children: [
      { name: '黄浦区', code: '310101', longitude: 121.4900, latitude: 31.2320 },
      { name: '徐汇区', code: '310104', longitude: 121.4368, latitude: 31.1880 },
      { name: '长宁区', code: '310105', longitude: 121.4220, latitude: 31.2200 },
      { name: '静安区', code: '310106', longitude: 121.4484, latitude: 31.2290 },
      { name: '普陀区', code: '310107', longitude: 121.3968, latitude: 31.2497 },
      { name: '虹口区', code: '310109', longitude: 121.5057, latitude: 31.2646 },
      { name: '杨浦区', code: '310110', longitude: 121.5220, latitude: 31.2595 },
      { name: '闵行区', code: '310112', longitude: 121.3816, latitude: 31.1124 },
      { name: '宝山区', code: '310113', longitude: 121.4890, latitude: 31.4044 },
      { name: '嘉定区', code: '310114', longitude: 121.2655, latitude: 31.3750 },
      { name: '浦东新区', code: '310115', longitude: 121.5447, latitude: 31.2216 },
      { name: '金山区', code: '310116', longitude: 121.3420, latitude: 30.7240 },
      { name: '松江区', code: '310117', longitude: 121.2281, latitude: 31.0324 },
      { name: '青浦区', code: '310118', longitude: 121.1240, latitude: 31.1510 },
      { name: '奉贤区', code: '310120', longitude: 121.4740, latitude: 30.9180 },
      { name: '崇明区', code: '310151', longitude: 121.3970, latitude: 31.6270 }
    ]
  },
  {
    name: '广东省',
    code: '440000',
    longitude: 113.2644,
    latitude: 23.1291,
    timezone: 'Asia/Shanghai',
    children: [
      { name: '广州市', code: '440100', longitude: 113.2644, latitude: 23.1291 },
      { name: '韶关市', code: '440200', longitude: 113.5917, latitude: 24.8016 },
      { name: '深圳市', code: '440300', longitude: 114.0579, latitude: 22.5431 },
      { name: '珠海市', code: '440400', longitude: 113.5530, latitude: 22.2240 },
      { name: '汕头市', code: '440500', longitude: 116.7081, latitude: 23.3540 },
      { name: '佛山市', code: '440600', longitude: 113.1220, latitude: 23.0288 },
      { name: '江门市', code: '440700', longitude: 113.0946, latitude: 22.5901 },
      { name: '湛江市', code: '440800', longitude: 110.3594, latitude: 21.2707 },
      { name: '茂名市', code: '440900', longitude: 110.9255, latitude: 21.6687 },
      { name: '肇庆市', code: '441200', longitude: 112.4721, latitude: 23.0515 },
      { name: '惠州市', code: '441300', longitude: 114.4152, latitude: 23.0794 },
      { name: '梅州市', code: '441400', longitude: 116.1255, latitude: 24.2899 },
      { name: '汕尾市', code: '441500', longitude: 115.3648, latitude: 22.7864 },
      { name: '河源市', code: '441600', longitude: 114.6974, latitude: 23.7469 },
      { name: '阳江市', code: '441700', longitude: 111.9827, latitude: 21.8590 },
      { name: '清远市', code: '441800', longitude: 113.0510, latitude: 23.6817 },
      { name: '东莞市', code: '441900', longitude: 113.7518, latitude: 23.0205 },
      { name: '中山市', code: '442000', longitude: 113.3823, latitude: 22.5154 },
      { name: '潮州市', code: '445100', longitude: 116.6302, latitude: 23.6618 },
      { name: '揭阳市', code: '445200', longitude: 116.3729, latitude: 23.5479 },
      { name: '云浮市', code: '445300', longitude: 112.0446, latitude: 22.9297 }
    ]
  },
  {
    name: '江苏省',
    code: '320000',
    longitude: 118.7674,
    latitude: 32.0415,
    timezone: 'Asia/Shanghai',
    children: [
      { name: '南京市', code: '320100', longitude: 118.7674, latitude: 32.0415 },
      { name: '无锡市', code: '320200', longitude: 120.3019, latitude: 31.5747 },
      { name: '徐州市', code: '320300', longitude: 117.1848, latitude: 34.2618 },
      { name: '常州市', code: '320400', longitude: 119.9463, latitude: 31.7720 },
      { name: '苏州市', code: '320500', longitude: 120.6197, latitude: 31.2989 },
      { name: '南通市', code: '320600', longitude: 120.8644, latitude: 32.0162 },
      { name: '连云港市', code: '320700', longitude: 119.1788, latitude: 34.5996 },
      { name: '淮安市', code: '320800', longitude: 119.0153, latitude: 33.5975 },
      { name: '盐城市', code: '320900', longitude: 120.1397, latitude: 33.3776 },
      { name: '扬州市', code: '321000', longitude: 119.4215, latitude: 32.3932 },
      { name: '镇江市', code: '321100', longitude: 119.4520, latitude: 32.2044 },
      { name: '泰州市', code: '321200', longitude: 119.9153, latitude: 32.4849 },
      { name: '宿迁市', code: '321300', longitude: 118.2757, latitude: 33.9630 }
    ]
  },
  {
    name: '浙江省',
    code: '330000',
    longitude: 120.1538,
    latitude: 30.2875,
    timezone: 'Asia/Shanghai',
    children: [
      { name: '杭州市', code: '330100', longitude: 120.1538, latitude: 30.2875 },
      { name: '宁波市', code: '330200', longitude: 121.5440, latitude: 29.8683 },
      { name: '温州市', code: '330300', longitude: 120.6994, latitude: 27.9944 },
      { name: '嘉兴市', code: '330400', longitude: 120.7506, latitude: 30.7467 },
      { name: '湖州市', code: '330500', longitude: 120.0865, latitude: 30.8936 },
      { name: '绍兴市', code: '330600', longitude: 120.5820, latitude: 29.9971 },
      { name: '金华市', code: '330700', longitude: 119.6492, latitude: 29.0895 },
      { name: '衢州市', code: '330800', longitude: 118.8718, latitude: 28.9417 },
      { name: '舟山市', code: '330900', longitude: 122.1070, latitude: 30.0360 },
      { name: '台州市', code: '331000', longitude: 121.4287, latitude: 28.6561 },
      { name: '丽水市', code: '331100', longitude: 119.9214, latitude: 28.4517 }
    ]
  }
];

// 工具函数
export class EnhancedLocationUtils {
  /**
   * 根据省份获取城市列表
   */
  static getCitiesByProvince(provinceName: string): LocationData[] {
    const province = enhancedLocationData.find(p => p.name === provinceName);
    return province?.children || [];
  }

  /**
   * 获取所有省份
   */
  static getAllProvinces(): LocationData[] {
    return enhancedLocationData;
  }

  /**
   * 根据城市名称获取经纬度
   */
  static getCoordinates(provinceName: string, cityName: string): { longitude: number; latitude: number } | null {
    const province = enhancedLocationData.find(p => p.name === provinceName);
    if (!province) return null;

    const city = province.children?.find(c => c.name === cityName);
    if (city && city.longitude && city.latitude) {
      return { longitude: city.longitude, latitude: city.latitude };
    }

    // 如果城市没有坐标，返回省份坐标
    if (province.longitude && province.latitude) {
      return { longitude: province.longitude, latitude: province.latitude };
    }

    return null;
  }

  /**
   * 获取时区信息
   */
  static getTimezone(provinceName: string): string {
    const province = enhancedLocationData.find(p => p.name === provinceName);
    return province?.timezone || 'Asia/Shanghai';
  }

  /**
   * 搜索地区
   */
  static searchLocation(keyword: string): LocationData[] {
    const results: LocationData[] = [];
    
    enhancedLocationData.forEach(province => {
      if (province.name.includes(keyword)) {
        results.push(province);
      }
      
      province.children?.forEach(city => {
        if (city.name.includes(keyword)) {
          results.push({
            ...city,
            name: `${province.name} ${city.name}`
          });
        }
      });
    });
    
    return results;
  }
}
