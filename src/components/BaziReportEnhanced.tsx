import React from 'react';
import {
  Card,
  Descriptions,
  Typography,
  Row,
  Col,
  Tag,
  Progress,
  Divider,
  Space,
  Alert,
  Table,
  Statistic
} from 'antd';
import {
  UserOutlined,
  CalendarOutlined,
  StarOutlined,
  BookOutlined,
  FireOutlined,
  HeartOutlined,
  GiftOutlined,
  CrownOutlined
} from '@ant-design/icons';
import { BaZiReportData } from '../types/bazi';

const { Title, Paragraph, Text } = Typography;

interface BaziReportEnhancedProps {
  data: BaZiReportData;
  customerName?: string;
}

const BaziReportEnhanced: React.FC<BaziReportEnhancedProps> = ({ data, customerName }) => {
  // 命主信息组件 - 增强版
  const MingZhuInfoSection = () => (
    <Card 
      title={
        <span>
          <UserOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
          命主信息
        </span>
      }
      style={{ marginBottom: '24px' }}
      headStyle={{ backgroundColor: '#f0f9ff', borderBottom: '2px solid #1890ff' }}
    >
      <Row gutter={[24, 16]}>
        <Col xs={24} md={12}>
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="姓名">
              <Text strong style={{ color: '#1890ff' }}>{data.命主信息.姓名}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="性别">{data.命主信息.性别}</Descriptions.Item>
            <Descriptions.Item label="生肖">{data.命主信息.生肖}</Descriptions.Item>
            <Descriptions.Item label="星座">{data.命主信息.星座}</Descriptions.Item>
            <Descriptions.Item label="公历生日">{data.命主信息.公历生日}</Descriptions.Item>
            <Descriptions.Item label="农历生日">{data.命主信息.农历生日}</Descriptions.Item>
            <Descriptions.Item label="出生地">{data.命主信息.出生地}</Descriptions.Item>
          </Descriptions>
        </Col>
        
        <Col xs={24} md={12}>
          <Card size="small" title={
            <span>
              <GiftOutlined style={{ marginRight: '8px', color: '#52c41a' }} />
              幸运秘诀
            </span>
          } style={{ backgroundColor: '#f6ffed' }}>
            <Row gutter={[16, 8]}>
              <Col span={12}>
                <Statistic 
                  title="幸运数字" 
                  value={data.命主信息.幸运秘诀?.幸运数字 || '未知'} 
                  valueStyle={{ color: '#52c41a', fontSize: '16px' }}
                />
              </Col>
              <Col span={12}>
                <Statistic 
                  title="大凶数字" 
                  value={data.命主信息.幸运秘诀?.大凶数字 || '未知'} 
                  valueStyle={{ color: '#ff4d4f', fontSize: '16px' }}
                />
              </Col>
              <Col span={12}>
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>吉祥颜色</Text>
                  <div>
                    <Tag color="green">{data.命主信息.幸运秘诀?.吉祥颜色 || '未知'}</Tag>
                  </div>
                </div>
              </Col>
              <Col span={12}>
                <div>
                  <Text type="secondary" style={{ fontSize: '12px' }}>忌讳颜色</Text>
                  <div>
                    <Tag color="red">{data.命主信息.幸运秘诀?.忌讳颜色 || '未知'}</Tag>
                  </div>
                </div>
              </Col>
            </Row>
            <Divider style={{ margin: '12px 0' }} />
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>幸运花：</Text>
                <Text>{data.命主信息.幸运秘诀?.幸运花 || '未知'}</Text>
              </div>
              <div>
                <Text type="secondary" style={{ fontSize: '12px' }}>幸运物品：</Text>
                <Text>{data.命主信息.幸运秘诀?.幸运物品 || '未知'}</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </Card>
  );

  // 八字称骨信息组件 - 增强版
  const ChengGuInfoSection = () => (
    <Card 
      title={
        <span>
          <StarOutlined style={{ marginRight: '8px', color: '#faad14' }} />
          八字称骨信息
        </span>
      }
      style={{ marginBottom: '24px' }}
      headStyle={{ backgroundColor: '#fffbe6', borderBottom: '2px solid #faad14' }}
    >
      <Row gutter={24}>
        <Col xs={24} md={10}>
          <Card size="small" title="骨重详情" style={{ backgroundColor: '#fafafa' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ textAlign: 'center', marginBottom: '16px' }}>
                <Statistic 
                  title="总骨重" 
                  value={data.命主八字称骨信息.总骨重} 
                  valueStyle={{ color: '#faad14', fontSize: '24px', fontWeight: 'bold' }}
                />
              </div>
              <Descriptions column={1} size="small">
                <Descriptions.Item label="年骨重">{data.命主八字称骨信息.年骨重}</Descriptions.Item>
                <Descriptions.Item label="月骨重">{data.命主八字称骨信息.月骨重}</Descriptions.Item>
                <Descriptions.Item label="日骨重">{data.命主八字称骨信息.日骨重}</Descriptions.Item>
                <Descriptions.Item label="时骨重">{data.命主八字称骨信息.时骨重}</Descriptions.Item>
              </Descriptions>
            </Space>
          </Card>
        </Col>
        <Col xs={24} md={14}>
          <Card 
            size="small" 
            title={
              <span>
                <CrownOutlined style={{ marginRight: '8px', color: '#722ed1' }} />
                称骨歌
              </span>
            } 
            style={{ backgroundColor: '#f9f0ff', height: '100%' }}
          >
            <Paragraph 
              style={{ 
                whiteSpace: 'pre-line', 
                color: '#333',
                fontSize: '14px',
                lineHeight: '1.6',
                textAlign: 'center',
                fontStyle: 'italic'
              }}
            >
              {data.命主八字称骨信息.称骨歌}
            </Paragraph>
          </Card>
        </Col>
      </Row>
    </Card>
  );

  // 五行喜神信息组件 - 增强版
  const WuXingXiShenSection = () => {
    const wuxingColors = {
      '金': '#FFD700',
      '木': '#228B22',
      '水': '#4169E1',
      '火': '#FF4500',
      '土': '#8B4513'
    };

    const wuxingStats = data.命主五行和喜神信息.五行统计;
    const maxCount = Math.max(...Object.values(wuxingStats));

    return (
      <Card 
        title={
          <span>
            <FireOutlined style={{ marginRight: '8px', color: '#ff4500' }} />
            五行喜神信息
          </span>
        }
        style={{ marginBottom: '24px' }}
        headStyle={{ backgroundColor: '#fff2e8', borderBottom: '2px solid #ff4500' }}
      >
        <Row gutter={24}>
          <Col xs={24} lg={14}>
            <Title level={4}>五行统计</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(wuxingStats).map(([element, count]) => (
                <div key={element} style={{ display: 'flex', alignItems: 'center', marginBottom: '12px' }}>
                  <div style={{ 
                    minWidth: '60px', 
                    textAlign: 'center',
                    backgroundColor: wuxingColors[element as keyof typeof wuxingColors],
                    color: 'white',
                    padding: '4px 8px',
                    borderRadius: '4px',
                    fontWeight: 'bold',
                    marginRight: '12px'
                  }}>
                    {element}
                  </div>
                  <Progress 
                    percent={(count / maxCount) * 100} 
                    size="small" 
                    strokeColor={wuxingColors[element as keyof typeof wuxingColors]}
                    format={() => `${count}个`}
                    style={{ flex: 1 }}
                  />
                </div>
              ))}
            </Space>
          </Col>
          <Col xs={24} lg={10}>
            <Title level={4}>喜神分析</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Card size="small" style={{ backgroundColor: '#f6ffed' }}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">喜用神</Text>
                  <div style={{ marginTop: '8px' }}>
                    <Tag color="green" style={{ fontSize: '16px', padding: '4px 12px' }}>
                      {data.命主五行和喜神信息.喜用神}
                    </Tag>
                  </div>
                </div>
              </Card>
              
              <Card size="small" style={{ backgroundColor: '#fff1f0' }}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">忌神</Text>
                  <div style={{ marginTop: '8px' }}>
                    <Tag color="red" style={{ fontSize: '16px', padding: '4px 12px' }}>
                      {data.命主五行和喜神信息.忌神}
                    </Tag>
                  </div>
                </div>
              </Card>
              
              <Card size="small" style={{ backgroundColor: '#fafafa' }}>
                <div style={{ textAlign: 'center' }}>
                  <Text type="secondary">五行缺失</Text>
                  <div style={{ marginTop: '8px' }}>
                    <Space wrap>
                      {data.命主五行和喜神信息.五行缺失.length > 0 ? (
                        data.命主五行和喜神信息.五行缺失.map(element => (
                          <Tag key={element} color="orange">{element}</Tag>
                        ))
                      ) : (
                        <Tag color="blue">五行齐全</Tag>
                      )}
                    </Space>
                  </div>
                </div>
              </Card>
            </Space>
            
            <Alert
              message="改运建议"
              description={data.命主五行和喜神信息.建议}
              type="success"
              showIcon
              style={{ marginTop: '16px' }}
            />
          </Col>
        </Row>
      </Card>
    );
  };

  // 姓名数理信息组件 - 增强版
  const XingMingShuLiSection = () => {
    const shuliData = [
      { name: '天格', ...data.命主姓名数理信息.天格 },
      { name: '人格', ...data.命主姓名数理信息.人格 },
      { name: '地格', ...data.命主姓名数理信息.地格 },
      { name: '总格', ...data.命主姓名数理信息.总格 },
      { name: '外格', ...data.命主姓名数理信息.外格 },
    ];

    const columns = [
      {
        title: '格局',
        dataIndex: 'name',
        key: 'name',
        width: 80,
        render: (text: string) => (
          <Tag color="blue" style={{ fontWeight: 'bold' }}>{text}</Tag>
        ),
      },
      {
        title: '数字',
        dataIndex: '数字',
        key: 'number',
        width: 80,
        render: (text: string) => (
          <Text strong style={{ color: '#1890ff' }}>{text}</Text>
        ),
      },
      {
        title: '吉凶',
        dataIndex: '吉凶',
        key: 'luck',
        width: 80,
        render: (text: string) => (
          <Tag color={text.includes('吉') ? 'green' : 'red'} style={{ fontWeight: 'bold' }}>
            {text}
          </Tag>
        ),
      },
      {
        title: '含义',
        dataIndex: '含义',
        key: 'meaning',
        ellipsis: true,
        render: (text: string) => (
          <Text style={{ fontSize: '13px' }}>{text}</Text>
        ),
      },
    ];

    return (
      <Card 
        title={
          <span>
            <BookOutlined style={{ marginRight: '8px', color: '#722ed1' }} />
            姓名数理信息
          </span>
        }
        style={{ marginBottom: '24px' }}
        headStyle={{ backgroundColor: '#f9f0ff', borderBottom: '2px solid #722ed1' }}
      >
        <Table 
          dataSource={shuliData} 
          columns={columns} 
          pagination={false}
          size="small"
          rowKey="name"
          style={{ backgroundColor: '#fafafa' }}
        />
      </Card>
    );
  };

  // 八字命盘信息组件 - 增强版
  const MingPanInfoSection = () => (
    <Card 
      title={
        <span>
          <CalendarOutlined style={{ marginRight: '8px', color: '#13c2c2' }} />
          八字命盘信息
        </span>
      }
      style={{ marginBottom: '24px' }}
      headStyle={{ backgroundColor: '#e6fffb', borderBottom: '2px solid #13c2c2' }}
    >
      <Row gutter={24}>
        <Col xs={24} lg={12}>
          <Title level={4}>八字信息</Title>
          <Card size="small" style={{ backgroundColor: '#f0f9ff' }}>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label="年柱">
                <Tag color="red" style={{ fontSize: '14px' }}>{data.命主八字命盘信息.八字信息.年柱}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="月柱">
                <Tag color="orange" style={{ fontSize: '14px' }}>{data.命主八字命盘信息.八字信息.月柱}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="日柱">
                <Tag color="green" style={{ fontSize: '14px' }}>{data.命主八字命盘信息.八字信息.日柱}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="时柱">
                <Tag color="blue" style={{ fontSize: '14px' }}>{data.命主八字命盘信息.八字信息.时柱}</Tag>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Title level={4}>十神信息</Title>
          <Card size="small" style={{ backgroundColor: '#fff7e6' }}>
            <Descriptions column={1} bordered size="small">
              {Object.entries(data.命主八字命盘信息.十神信息).map(([key, value]) => (
                <Descriptions.Item key={key} label={key}>
                  <Text style={{ color: '#fa8c16' }}>{value}</Text>
                </Descriptions.Item>
              ))}
            </Descriptions>
          </Card>
        </Col>
      </Row>
      
      <Divider />
      
      <Title level={4}>命理综述</Title>
      <Alert
        message="八字命理分析"
        description={data.命主八字命盘信息.八字信息.命理综述}
        type="info"
        showIcon
        style={{ marginTop: '16px' }}
      />
    </Card>
  );

  // 日干论命信息组件 - 增强版
  const RiGanLunMingSection = () => (
    <Card 
      title={
        <span>
          <HeartOutlined style={{ marginRight: '8px', color: '#eb2f96' }} />
          日干论命信息
        </span>
      }
      style={{ marginBottom: '24px' }}
      headStyle={{ backgroundColor: '#fff0f6', borderBottom: '2px solid #eb2f96' }}
    >
      <Row gutter={24}>
        <Col span={24} style={{ textAlign: 'center', marginBottom: '24px' }}>
          <Title level={3}>
            日干：<Tag color="magenta" style={{ fontSize: '18px', padding: '8px 16px' }}>
              {data.命主日干论命信息.日干}
            </Tag>
          </Title>
        </Col>
      </Row>
      
      <Row gutter={24}>
        <Col xs={24} lg={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small" title="性格特点" style={{ backgroundColor: '#f6ffed' }}>
              <Paragraph style={{ margin: 0, fontSize: '14px', lineHeight: '1.6' }}>
                {data.命主日干论命信息.性格特点}
              </Paragraph>
            </Card>
            
            <Card size="small" title="事业运势" style={{ backgroundColor: '#fff7e6' }}>
              <Paragraph style={{ margin: 0, fontSize: '14px', lineHeight: '1.6' }}>
                {data.命主日干论命信息.事业运势}
              </Paragraph>
            </Card>
            
            <Card size="small" title="财运分析" style={{ backgroundColor: '#f0f9ff' }}>
              <Paragraph style={{ margin: 0, fontSize: '14px', lineHeight: '1.6' }}>
                {data.命主日干论命信息.财运分析}
              </Paragraph>
            </Card>
          </Space>
        </Col>
        
        <Col xs={24} lg={12}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Card size="small" title="感情运势" style={{ backgroundColor: '#fff1f0' }}>
              <Paragraph style={{ margin: 0, fontSize: '14px', lineHeight: '1.6' }}>
                {data.命主日干论命信息.感情运势}
              </Paragraph>
            </Card>
            
            <Card size="small" title="健康状况" style={{ backgroundColor: '#f9f0ff' }}>
              <Paragraph style={{ margin: 0, fontSize: '14px', lineHeight: '1.6' }}>
                {data.命主日干论命信息.健康状况}
              </Paragraph>
            </Card>
            
            <Card size="small" title="总体建议" style={{ backgroundColor: '#e6fffb' }}>
              <Alert
                message={data.命主日干论命信息.总体建议}
                type="info"
                showIcon
                style={{ margin: 0 }}
              />
            </Card>
          </Space>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto', backgroundColor: '#f5f5f5' }}>
      <div style={{ textAlign: 'center', marginBottom: '32px' }}>
        <Title level={1} style={{ color: '#1890ff', marginBottom: '8px' }}>
          <UserOutlined style={{ marginRight: '12px' }} />
          {customerName || data.命主信息.姓名} 命盘报告
        </Title>
        <Paragraph style={{ fontSize: '16px', color: '#666' }}>
          公历生日: {data.命主信息.公历生日}
        </Paragraph>
        <Tag color="gold" style={{ fontSize: '14px', padding: '4px 12px' }}>
          专业八字命理分析
        </Tag>
      </div>

      <MingZhuInfoSection />
      <ChengGuInfoSection />
      <XingMingShuLiSection />
      <MingPanInfoSection />
      <WuXingXiShenSection />
      <RiGanLunMingSection />

      <Card style={{ textAlign: 'center', backgroundColor: '#fafafa', marginTop: '32px' }}>
        <Space direction="vertical">
          <Paragraph style={{ color: '#999', margin: 0, fontSize: '14px' }}>
            仅供参考，命运掌握在自己手中
          </Paragraph>
          <Paragraph style={{ color: '#999', fontSize: '12px', margin: 0 }}>
            &copy; {new Date().getFullYear()} 八字命盘. All rights reserved.
          </Paragraph>
        </Space>
      </Card>
    </div>
  );
};

export default BaziReportEnhanced; 