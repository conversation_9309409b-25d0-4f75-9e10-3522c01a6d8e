/**
 * 综合功能测试脚本
 * 测试产品展示、支付流程和跳转逻辑
 */
import fetch from 'node-fetch';
import { Product, ProductImage, ProductLink, Order, User } from '../models/index.js';
import sequelize from '../config/database.js';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

// 加载环境变量
dotenv.config();

// 测试配置
const config = {
  apiUrl: 'http://localhost:3005/api',
  adminUser: {
    username: 'admin',
    password: 'admin123'
  },
  testUser: {
    username: `test_${Date.now()}`,
    password: 'test123',
    name: '测试用户',
    email: `test_${Date.now()}@example.com`,
    phone: `1380000${Math.floor(Math.random() * 10000)}`,
    level: 2
  }
};

// 全局变量存储测试过程中的数据
const testData = {
  adminToken: null,
  userToken: null,
  products: [],
  deepseekProduct: null,
  productLink: null,
  order: null
};

/**
 * 测试用例执行器
 */
async function runTests() {
  try {
    console.log('=== 开始综合功能测试 ===');
    
    // 清理测试数据
    await cleanupTestData();
    
    // 1. 测试管理员登录
    await testAdminLogin();
    
    // 2. 测试创建测试用户
    await testCreateUser();
    
    // 3. 测试用户登录
    await testUserLogin();
    
    // 4. 测试获取产品列表
    await testGetProducts();
    
    // 5. 测试 Deepseek 产品是否存在
    await testDeepseekProductExists();
    
    // 6. 测试创建产品推广链接
    await testCreateProductLink();
    
    // 7. 测试创建订单
    await testCreateOrder();
    
    // 8. 测试支付跳转逻辑
    await testPaymentRedirect();
    
    // 9. 测试产品详情页
    await testProductDetail();
    
    console.log('=== 综合功能测试完成 ===');
    console.log('所有测试用例通过！');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    // 清理测试数据
    await cleanupTestData();
    process.exit(0);
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData() {
  try {
    if (testData.order) {
      await Order.destroy({ where: { id: testData.order.id } });
      console.log(`清理测试订单: ${testData.order.id}`);
    }
    
    if (testData.productLink) {
      await ProductLink.destroy({ where: { id: testData.productLink.id } });
      console.log(`清理测试推广链接: ${testData.productLink.id}`);
    }
    
    // 清理测试用户
    const testUser = await User.findOne({ where: { username: config.testUser.username } });
    if (testUser) {
      await testUser.destroy();
      console.log(`清理测试用户: ${testUser.username}`);
    }
    
  } catch (error) {
    console.error('清理测试数据失败:', error);
  }
}

/**
 * 测试管理员登录
 */
async function testAdminLogin() {
  console.log('\n--- 测试管理员登录 ---');
  
  const response = await fetch(`${config.apiUrl}/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      username: config.adminUser.username,
      password: config.adminUser.password
    })
  });
  
  const data = await response.json();
  
  if (!response.ok || !data.token) {
    throw new Error(`管理员登录失败: ${JSON.stringify(data)}`);
  }
  
  testData.adminToken = data.token;
  console.log('管理员登录成功，获取到token');
}

/**
 * 测试创建测试用户
 */
async function testCreateUser() {
  console.log('\n--- 测试创建用户 ---');
  
  const response = await fetch(`${config.apiUrl}/users`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${testData.adminToken}`
    },
    body: JSON.stringify(config.testUser)
  });
  
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(`创建用户失败: ${JSON.stringify(data)}`);
  }
  
  console.log(`创建测试用户成功: ${data.username}`);
}

/**
 * 测试用户登录
 */
async function testUserLogin() {
  console.log('\n--- 测试用户登录 ---');
  
  const response = await fetch(`${config.apiUrl}/auth/login`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      username: config.testUser.username,
      password: config.testUser.password
    })
  });
  
  const data = await response.json();
  
  if (!response.ok || !data.token) {
    throw new Error(`用户登录失败: ${JSON.stringify(data)}`);
  }
  
  testData.userToken = data.token;
  console.log('用户登录成功，获取到token');
}

/**
 * 测试获取产品列表
 */
async function testGetProducts() {
  console.log('\n--- 测试获取产品列表 ---');
  
  const response = await fetch(`${config.apiUrl}/products`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${testData.userToken}`
    }
  });
  
  const data = await response.json();
  
  if (!response.ok || !Array.isArray(data)) {
    throw new Error(`获取产品列表失败: ${JSON.stringify(data)}`);
  }
  
  testData.products = data;
  console.log(`获取产品列表成功，共 ${data.length} 个产品`);
}

/**
 * 测试 Deepseek 产品是否存在
 */
async function testDeepseekProductExists() {
  console.log('\n--- 测试 Deepseek 产品是否存在 ---');
  
  const deepseekProduct = testData.products.find(p => p.title.includes('Deepseek'));
  
  if (!deepseekProduct) {
    throw new Error('Deepseek 产品不存在，请先运行 addCompleteDeepseekCourse.js 脚本');
  }
  
  testData.deepseekProduct = deepseekProduct;
  console.log(`找到 Deepseek 产品: ${deepseekProduct.title}, ID: ${deepseekProduct.id}`);
  
  // 测试产品图片
  const response = await fetch(`${config.apiUrl}/product-images/${deepseekProduct.id}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${testData.userToken}`
    }
  });
  
  const images = await response.json();
  
  if (!response.ok || !Array.isArray(images)) {
    throw new Error(`获取产品图片失败: ${JSON.stringify(images)}`);
  }
  
  console.log(`产品图片获取成功，共 ${images.length} 张图片`);
  
  if (images.length === 0) {
    throw new Error('Deepseek 产品没有图片，请检查 addCompleteDeepseekCourse.js 脚本');
  }
}

/**
 * 测试创建产品推广链接
 */
async function testCreateProductLink() {
  console.log('\n--- 测试创建产品推广链接 ---');
  
  const response = await fetch(`${config.apiUrl}/product-links`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${testData.userToken}`
    },
    body: JSON.stringify({
      productId: testData.deepseekProduct.id
    })
  });
  
  const data = await response.json();
  
  if (!response.ok || !data.id) {
    throw new Error(`创建产品推广链接失败: ${JSON.stringify(data)}`);
  }
  
  testData.productLink = data;
  console.log(`创建产品推广链接成功，链接码: ${data.code}`);
}

/**
 * 测试创建订单
 */
async function testCreateOrder() {
  console.log('\n--- 测试创建订单 ---');
  
  // 获取用户信息
  const userResponse = await fetch(`${config.apiUrl}/users/me`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${testData.userToken}`
    }
  });
  
  const userData = await userResponse.json();
  
  if (!userResponse.ok) {
    throw new Error(`获取用户信息失败: ${JSON.stringify(userData)}`);
  }
  
  // 创建模拟订单
  const mockOrder = {
    productLinkId: testData.productLink.id,
    productId: testData.deepseekProduct.id,
    amount: testData.deepseekProduct.price,
    status: 'pending',
    buyerName: '测试买家',
    buyerPhone: '13800001234',
    buyerEmail: '<EMAIL>'
  };
  
  // 直接在数据库中创建订单
  const order = await Order.create({
    id: `TEST${Date.now()}`,
    ...mockOrder,
    agentId: userData.id
  });
  
  testData.order = order;
  console.log(`创建订单成功，订单ID: ${order.id}`);
}

/**
 * 测试支付跳转逻辑
 */
async function testPaymentRedirect() {
  console.log('\n--- 测试支付跳转逻辑 ---');
  
  // 模拟订单已支付
  await testData.order.update({
    status: 'paid',
    paidAt: new Date(),
    paymentId: `test_payment_${Date.now()}`
  });
  
  console.log('模拟订单已支付');
  
  // 测试同步回调跳转
  const response = await fetch(`${config.apiUrl}/payment/alipay/return?out_trade_no=${testData.order.id}`, {
    method: 'GET',
    redirect: 'manual' // 不自动跟随重定向
  });
  
  // 检查重定向URL
  const redirectUrl = response.headers.get('location');
  
  if (!redirectUrl) {
    throw new Error('没有获取到重定向URL');
  }
  
  console.log(`重定向URL: ${redirectUrl}`);
  
  // 检查是否重定向到 PAID_RETURN_URL2
  if (!redirectUrl.includes(process.env.PAID_RETURN_URL2)) {
    throw new Error(`重定向URL不正确，应该包含 ${process.env.PAID_RETURN_URL2}`);
  }
  
  console.log('支付跳转逻辑测试通过，成功重定向到 PAID_RETURN_URL2');
}

/**
 * 测试产品详情页
 */
async function testProductDetail() {
  console.log('\n--- 测试产品详情页 ---');
  
  const response = await fetch(`${config.apiUrl}/product-links/${testData.productLink.code}`, {
    method: 'GET'
  });
  
  const data = await response.json();
  
  if (!response.ok) {
    throw new Error(`获取产品详情失败: ${JSON.stringify(data)}`);
  }
  
  console.log(`获取产品详情成功: ${data.product.title}`);
  
  // 检查产品标题是否包含 Deepseek
  if (!data.product.title.includes('Deepseek')) {
    throw new Error(`产品标题不正确: ${data.product.title}`);
  }
  
  // 检查产品价格
  if (data.product.price != 299.00) {
    throw new Error(`产品价格不正确: ${data.product.price}`);
  }
  
  console.log('产品详情页测试通过');
}

// 运行测试
runTests();
