import React, { useState, useEffect } from 'react';
import { config } from '../config';
import { useAuth } from '../contexts/AuthContext';
import { 
  Row, Col, Divider, Tooltip, Modal, Select, Input, Empty,
  Typography, Tag, Space, Button as AntButton
} from 'antd';
import { 
  UserOutlined, EditOutlined, CopyOutlined, 
  CheckCircleOutlined, StopOutlined, QuestionCircleOutlined,
  TeamOutlined, LinkOutlined
} from '@ant-design/icons';
import {
  PageContainer,
  PageTitle,
  PageDescription,
  Card,
  Table,
  Button,
  StatusTag,
  FilterContainer
} from '../styles/AdminTheme';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface User {
  id: number;
  username: string;
  name: string;
  phone: string;
  status: string;
  role: string;
  level: number;
  createdAt: string;
  orderCount: number;
  commissionRate: number;
  inviteLink?: string;
}

const UserManagement: React.FC = () => {
  const { token, user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [statusReason, setStatusReason] = useState('');
  const [newStatus, setNewStatus] = useState('');
  const [newCommissionRate, setNewCommissionRate] = useState('');
  const [showInviteLink, setShowInviteLink] = useState<boolean>(false);
  const [selectedInviteLink, setSelectedInviteLink] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('');
  const [filterRole, setFilterRole] = useState<string>('');
  const [searchValue, setSearchValue] = useState<string>('');

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('Fetching users with token:', token);

      // 增加分页大小参数，确保能获取所有用户
      const response = await fetch(
        `${config.apiUrl}/api/user-management?pageSize=100`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '获取用户列表失败');
      }

      const data = await response.json();
      console.log('Received users data:', data);
      setUsers(data.users);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      setError(error instanceof Error ? error.message : '获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateUser = async () => {
    if (!selectedUser) return;

    try {
      const response = await fetch(
        `${config.apiUrl}/api/user-management/${selectedUser.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            status: newStatus || undefined,
            reason: statusReason,
            commissionRate: newCommissionRate ? parseFloat(newCommissionRate) : undefined, // 不需要除以100，直接使用原始值
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '更新用户信息失败');
      }

      setOpenDialog(false);
      setStatusReason('');
      setNewStatus('');
      setNewCommissionRate('');
      fetchUsers();
    } catch (error) {
      alert(error instanceof Error ? error.message : '更新用户信息失败');
    }
  };

  const handleCopyInviteLink = async (inviteLink: string) => {
    try {
      await navigator.clipboard.writeText(inviteLink);
      alert('邀请链接已复制到剪贴板');
    } catch (err) {
      console.error('复制失败:', err);
      alert('复制失败，请手动复制');
    }
  };

  useEffect(() => {
    if (token) {
      fetchUsers();
    }
  }, [token]);

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <StatusTag status="warning">{getStatusText(status)}</StatusTag>;
      case 'approved':
        return <StatusTag status="success">{getStatusText(status)}</StatusTag>;
      case 'rejected':
        return <StatusTag status="error">{getStatusText(status)}</StatusTag>;
      case 'disabled':
        return <StatusTag status="default">{getStatusText(status)}</StatusTag>;
      default:
        return <StatusTag status="default">{getStatusText(status)}</StatusTag>;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '待审核';
      case 'approved':
        return '已通过';
      case 'rejected':
        return '已拒绝';
      case 'disabled':
        return '已停用';
      default:
        return status;
    }
  };

  const getAvailableStatuses = (currentStatus: string) => {
    console.log('Getting available statuses for:', currentStatus);
    // 确保状态值是有效的
    const validStatus = currentStatus?.toLowerCase() || '';
    
    switch (validStatus) {
      case 'pending':
        return [
          { value: 'approved', label: '通过' },
          { value: 'rejected', label: '拒绝' }
        ];
      case 'approved':
        return [
          { value: 'disabled', label: '停用' }
        ];
      case 'rejected':
        return [
          { value: 'approved', label: '通过' }
        ];
      case 'disabled':
        return [
          { value: 'approved', label: '恢复' }
        ];
      default:
        console.log('Unknown status:', currentStatus);
        return [];
    }
  };

  const filteredUsers = users.filter(user => {
    // 状态筛选
    if (filterStatus && user.status !== filterStatus) return false;
    
    // 角色筛选
    if (filterRole && user.role !== filterRole) return false;
    
    // 搜索功能
    if (searchValue) {
      const searchLower = searchValue.toLowerCase();
      return (
        user.username.toLowerCase().includes(searchLower) ||
        user.name.toLowerCase().includes(searchLower) ||
        user.phone.toLowerCase().includes(searchLower)
      );
    }
    
    return true;
  });

  const columns = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role: string) => role === 'admin' ? '管理员' : '用户',
    },
    {
      title: '等级',
      dataIndex: 'level',
      key: 'level',
      render: (level: number) => `${level}级代理`,
    },
    {
      title: '佣金比例',
      dataIndex: 'commissionRate',
      key: 'commissionRate',
      render: (rate: number) => {
        // 检查rate是否为有效数字
        if (rate === null || rate === undefined || isNaN(Number(rate))) {
          return '0.0%';
        }
        // 检查rate是否已经是百分比形式
        const numRate = Number(rate);
        const displayRate = numRate > 1 ? numRate : numRate * 100;
        return `${displayRate.toFixed(1)}%`;
      },
    },
    {
      title: '订单数',
      dataIndex: 'orderCount',
      key: 'orderCount',
      render: (count: number) => (
        <Tag color="blue" className="rounded-full px-2 py-1">{count || 0}</Tag>
      ),
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: User) => (
        <Space>
          {currentUser?.role === 'admin' && record.role !== 'admin' && (
            <Button
              icon={<EditOutlined />}
              onClick={() => {
                setSelectedUser(record);
                // 检查佣金率是否为有效数字
                const rate = record.commissionRate || 0;
                const displayRate = rate > 1 ? rate : rate * 100;
                setNewCommissionRate(displayRate.toString());
                setOpenDialog(true);
              }}
              type="primary"
              size="small"
            >
              编辑
            </Button>
          )}
          {currentUser?.role === 'admin' && record.role === 'admin' && record.status === 'approved' && record.inviteLink && (
            <Button
              icon={<LinkOutlined />}
              onClick={() => {
                setSelectedInviteLink(record.inviteLink || '');
                setShowInviteLink(true);
              }}
              type="default"
              size="small"
            >
              邀请链接
            </Button>
          )}
        </Space>
      ),
    },
  ];

  if (loading) {
    return (
      <PageContainer>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer>
        <Card className="error-card">
          <Text type="danger">{error}</Text>
        </Card>
      </PageContainer>
    );
  }

  if (!users.length) {
    return (
      <PageContainer>
        <Card>
          <Empty description="暂无用户数据" />
        </Card>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="page-header">
        <PageTitle level={2}>
          <TeamOutlined className="icon" /> 用户管理
        </PageTitle>
        <PageDescription>
          管理系统中的用户和代理商，设置佣金比例，审核新用户
        </PageDescription>
      </div>

      <FilterContainer>
        <div className="filter-item">
          <Select
            placeholder="状态筛选"
            style={{ width: '100%' }}
            allowClear
            value={filterStatus || undefined}
            onChange={value => setFilterStatus(value)}
          >
            <Option value="pending">待审核</Option>
            <Option value="approved">已通过</Option>
            <Option value="rejected">已拒绝</Option>
            <Option value="disabled">已停用</Option>
          </Select>
        </div>
        
        <div className="filter-item">
          <Select
            placeholder="角色筛选"
            style={{ width: '100%' }}
            allowClear
            value={filterRole || undefined}
            onChange={value => setFilterRole(value)}
          >
            <Option value="admin">管理员</Option>
            <Option value="user">用户</Option>
          </Select>
        </div>
        
        <div className="filter-item" style={{ width: '240px' }}>
          <Input
            placeholder="搜索用户名/姓名/手机号"
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
            allowClear
          />
        </div>
        
        <div className="filter-actions">
          <Button
            type="primary"
            onClick={fetchUsers}
          >
            刷新数据
          </Button>
        </div>
      </FilterContainer>

      <Card>
        <div className="responsive-table-container" style={{ overflowX: 'auto', width: '100%' }}>
          <Table
            dataSource={filteredUsers}
            columns={columns}
            rowKey="id"
            pagination={{ 
              pageSize: 10,
              showSizeChanger: true,
              showTotal: total => `共 ${total} 条记录`
            }}
            scroll={{ x: 'max-content' }}
            className="responsive-table"
            size={window.innerWidth < 768 ? "small" : "middle"}
          />
        </div>
      </Card>

      <Modal
        title="编辑用户信息"
        open={openDialog}
        onCancel={() => {
          setOpenDialog(false);
          setStatusReason('');
          setNewStatus('');
          setNewCommissionRate('');
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setOpenDialog(false);
              setStatusReason('');
              setNewStatus('');
              setNewCommissionRate('');
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={handleUpdateUser}
          >
            确认
          </Button>
        ]}
      >
        <div className="space-y-4">
          <div>
            <div className="mb-2">
              <Text type="secondary">用户信息:</Text>
            </div>
            <Card style={{ backgroundColor: '#f9f9f9' }}>
              <div className="mb-2">
                <Text strong>用户名:</Text> {selectedUser?.username}
              </div>
              <div className="mb-2">
                <Text strong>姓名:</Text> {selectedUser?.name}
              </div>
              <div>
                <Text strong>当前状态:</Text> {selectedUser && getStatusTag(selectedUser.status)}
              </div>
            </Card>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              状态
            </label>
            <Select
              placeholder="选择新状态"
              style={{ width: '100%' }}
              value={newStatus || undefined}
              onChange={value => setNewStatus(value)}
              allowClear
            >
              {selectedUser && getAvailableStatuses(selectedUser.status).map((status) => (
                <Option key={status.value} value={status.value}>
                  {status.label}
                </Option>
              ))}
            </Select>
          </div>

          {newStatus && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                状态变更原因
              </label>
              <TextArea
                value={statusReason}
                onChange={e => setStatusReason(e.target.value)}
                placeholder="请输入原因说明"
                rows={4}
              />
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              佣金比例 (%)
            </label>
            <Input
              type="number"
              value={newCommissionRate}
              onChange={e => {
                const value = e.target.value;
                if (value === '' || (parseFloat(value) >= 0 && parseFloat(value) <= 100)) {
                  setNewCommissionRate(value);
                }
              }}
              placeholder="输入0-100之间的数字"
              min="0"
              max="100"
              step="0.1"
              addonAfter="%"
            />
          </div>
        </div>
      </Modal>

      <Modal
        title="邀请链接"
        open={showInviteLink}
        onCancel={() => setShowInviteLink(false)}
        footer={[
          <Button
            key="close"
            onClick={() => setShowInviteLink(false)}
          >
            关闭
          </Button>
        ]}
      >
        <div className="mb-4">
          <Text type="secondary">使用此链接邀请新的代理商注册。链接有效期为7天。</Text>
        </div>
        <div className="flex items-center space-x-2">
          <Input 
            value={selectedInviteLink} 
            readOnly 
          />
          <Button
            icon={<CopyOutlined />}
            onClick={() => handleCopyInviteLink(selectedInviteLink)}
          >
            复制
          </Button>
        </div>
      </Modal>
    </PageContainer>
  );
};

export default UserManagement;
