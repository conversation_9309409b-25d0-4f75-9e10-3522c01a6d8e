'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 先删除外键约束
    await queryInterface.removeConstraint('Commissions', 'commissions_ibfk_1');
    
    // 修改 Orders 表的 id 字段
    await queryInterface.changeColumn('Orders', 'id', {
      type: Sequelize.STRING(20),
      allowNull: false,
      primaryKey: true,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    });

    // 修改 Commissions 表的 orderId 字段
    await queryInterface.changeColumn('Commissions', 'orderId', {
      type: Sequelize.STRING(20),
      allowNull: false,
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    });

    // 重新添加外键约束
    await queryInterface.addConstraint('Commissions', {
      fields: ['orderId'],
      type: 'foreign key',
      name: 'commissions_ibfk_1',
      references: {
        table: 'Orders',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });
  },

  async down(queryInterface, Sequelize) {
    // 先删除外键约束
    await queryInterface.removeConstraint('Commissions', 'commissions_ibfk_1');
    
    // 恢复 Orders 表的 id 字段
    await queryInterface.changeColumn('Orders', 'id', {
      type: Sequelize.STRING(16),
      allowNull: false,
      primaryKey: true
    });

    // 恢复 Commissions 表的 orderId 字段
    await queryInterface.changeColumn('Commissions', 'orderId', {
      type: Sequelize.STRING(16),
      allowNull: false
    });

    // 重新添加外键约束
    await queryInterface.addConstraint('Commissions', {
      fields: ['orderId'],
      type: 'foreign key',
      name: 'commissions_ibfk_1',
      references: {
        table: 'Orders',
        field: 'id'
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE'
    });
  }
};
