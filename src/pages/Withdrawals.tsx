import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import { Tabs, Table, Space, Tag, message } from 'antd';

interface WithdrawalStats {
  totalCommission: number;
  availableCommission: number;
  pendingCommission: number;
  withdrawnCommission: number;
}

interface CommissionDetail {
  id: number;
  amount: number;
  commissionRate?: number; // 添加佣金率字段
  status: 'pending' | 'available' | 'withdrawn';
  createdAt: string;
  updatedAt: string;
  order: {
    id: number;
    amount: number;
    status: string;
    customerName: string;
    customerPhone: string;
    createdAt: string;
    product: {
      id: number;
      title: string;
      price: number;
      imageUrl: string;
    } | null;
  } | null;
}

interface Withdrawal {
  id: number;
  amount: number;
  status: 'pending' | 'approved' | 'rejected' | 'completed';
  alipayAccount: string;
  createdAt: string;
  approvedAt: string | null;
  completedAt: string | null;
  remarks: string | null;
}

const Withdrawals: React.FC = () => {
  const { token } = useAuth();
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([]);
  const [commissionDetails, setCommissionDetails] = useState<CommissionDetail[]>([]);
  const [commissionLoading, setCommissionLoading] = useState<boolean>(false);
  const [commissionPage, setCommissionPage] = useState<number>(1);
  const [commissionTotal, setCommissionTotal] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [stats, setStats] = useState<WithdrawalStats>({
    totalCommission: 0,
    availableCommission: 0,
    pendingCommission: 0,
    withdrawnCommission: 0
  });
  const [withdrawalForm, setWithdrawalForm] = useState({
    amount: '',
    alipayAccount: ''
  });

  useEffect(() => {
    if (token) {
      Promise.all([fetchStats(), fetchWithdrawals()]);
    }
  }, [token]);
  
  // 获取佣金明细列表
  const fetchCommissionDetails = async (page = 1) => {
    setCommissionLoading(true);
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('page', page.toString());
      queryParams.append('limit', '10');
      
      const response = await fetch(`${config.apiUrl}/api/commissions/details/list?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('获取佣金明细失败');
      }
      
      const data = await response.json();
      // 过滤只保留有订单且订单状态为已支付的佣金记录
      const filteredCommissions = data.commissions.filter((commission: CommissionDetail) => 
        commission.order && commission.order.status === 'paid'
      );
      setCommissionDetails(filteredCommissions);
      setCommissionTotal(filteredCommissions.length);
      setCommissionLoading(false);
    } catch (err) {
      console.error('获取佣金明细失败:', err);
      message.error('获取佣金明细失败');
      setCommissionLoading(false);
    }
  };
  
  // 切换佣金页码
  const handleCommissionPageChange = (page: number) => {
    setCommissionPage(page);
    fetchCommissionDetails(page);
  };

  const fetchStats = async () => {
    try {
      const response = await fetch(`${config.apiUrl}/api/commissions`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const data = await response.json();
        if (response.status === 401) {
          throw new Error('登录已过期，请重新登录');
        }
        throw new Error(data.message || '获取统计数据失败');
      }

      const data = await response.json();
      setStats({
        totalCommission: Number(data.stats.totalCommission),
        availableCommission: Number(data.stats.availableCommission),
        pendingCommission: Number(data.stats.pendingCommission),
        withdrawnCommission: Number(data.stats.withdrawnCommission)
      });
    } catch (err) {
      console.error('获取统计数据失败:', err);
      setError(err instanceof Error ? err.message : '获取统计数据失败，请重试');
    }
  };

  const fetchWithdrawals = async () => {
    try {
      const response = await fetch(`${config.apiUrl}/api/withdrawals`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || '获取提现记录失败');
      }

      const data = await response.json();
      setWithdrawals(data.withdrawals);
      setError('');
    } catch (err) {
      console.error('获取提现记录失败:', err);
      setError(err instanceof Error ? err.message : '获取提现记录失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const response = await fetch(`${config.apiUrl}/api/withdrawals`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(withdrawalForm)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '申请提现失败');
      }

      // 重新获取数据
      await Promise.all([fetchStats(), fetchWithdrawals()]);
      
      // 清空表单
      setWithdrawalForm({
        amount: '',
        alipayAccount: ''
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '申请提现失败，请重试');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setWithdrawalForm({
      ...withdrawalForm,
      [e.target.name]: e.target.value
    });
  };

  const getStatusBadgeClass = (status: Withdrawal['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-blue-100 text-blue-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: Withdrawal['status']) => {
    const statusMap = {
      completed: '已完成',
      pending: '待审核',
      approved: '已审核',
      rejected: '已拒绝'
    };
    return statusMap[status];
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-semibold text-gray-900">提现管理</h1>

        {/* 统计卡片 */}
        <div className="mt-6 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">总佣金</dt>
                    <dd className="text-lg font-medium text-gray-900">¥{stats.totalCommission.toFixed(2)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">可提现佣金</dt>
                    <dd className="text-lg font-medium text-green-600">¥{stats.availableCommission.toFixed(2)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">待结算佣金</dt>
                    <dd className="text-lg font-medium text-gray-900">¥{stats.pendingCommission.toFixed(2)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">已提现佣金</dt>
                    <dd className="text-lg font-medium text-gray-900">¥{stats.withdrawnCommission.toFixed(2)}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 提现表单 */}
        <div className="mt-8 bg-white overflow-hidden shadow rounded-lg">
          <div className="p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">申请提现</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                  提现金额
                </label>
                <input
                  type="number"
                  name="amount"
                  id="amount"
                  required
                  min="0.01"
                  max={stats.availableCommission}
                  step="0.01"
                  value={withdrawalForm.amount}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="alipayAccount" className="block text-sm font-medium text-gray-700">
                  支付宝账号
                </label>
                <input
                  type="text"
                  name="alipayAccount"
                  id="alipayAccount"
                  required
                  value={withdrawalForm.alipayAccount}
                  onChange={handleChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
              <button
                type="submit"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                申请提现
              </button>
            </form>
          </div>
        </div>

        {/* 提现记录和佣金明细 */}
        <div className="mt-8">
          <Tabs 
            defaultActiveKey="withdrawals" 
            className="mb-4"
            onChange={(key: string) => {
              if (key === 'commissions' && commissionDetails.length === 0) {
                fetchCommissionDetails();
              }
            }}
            items={[
              {
                key: 'withdrawals',
                label: '提现记录',
                children: (
                  <div className="flex flex-col">
                    <div className="responsive-table-container" style={{ overflowX: 'auto', width: '100%' }}>
                      <div className="py-2 align-middle inline-block min-w-full">
                        <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
                          <table className="min-w-full divide-y divide-gray-200 responsive-table">
                            <thead className="bg-gray-50">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  提现信息
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  状态
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  时间
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  备注
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {withdrawals.map((withdrawal) => (
                                <tr key={withdrawal.id}>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm font-medium text-gray-900">
                                      ¥{withdrawal.amount.toFixed(2)}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      支付宝: {withdrawal.alipayAccount}
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(withdrawal.status)}`}>
                                      {getStatusText(withdrawal.status)}
                                    </span>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900">
                                      申请时间: {new Date(withdrawal.createdAt).toLocaleString()}
                                    </div>
                                    {withdrawal.approvedAt && (
                                      <div className="text-sm text-gray-500">
                                        审核时间: {new Date(withdrawal.approvedAt).toLocaleString()}
                                      </div>
                                    )}
                                    {withdrawal.completedAt && (
                                      <div className="text-sm text-gray-500">
                                        完成时间: {new Date(withdrawal.completedAt).toLocaleString()}
                                      </div>
                                    )}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {withdrawal.remarks || '-'}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                )
              },
              {
                key: 'commissions',
                label: '佣金明细',
                //disabled: true, // 禁用此标签页，使其不可点击
                children: (
                  <>
                    <div className="bg-white rounded-lg shadow p-3 sm:p-6">
                      <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-6">佣金明细</h2>
                      

                      
                      <div className="px-2">
                        {commissionLoading ? (
                          <div className="text-center py-10">加载中...</div>
                        ) : commissionDetails.length > 0 ? (
                          <div className="responsive-table-container" style={{ overflowX: 'auto', width: '100%' }}>
                            <Table
                              dataSource={commissionDetails}
                              rowKey="id"
                              pagination={{
                                pageSize: 10,
                                showTotal: (total) => `共 ${total} 条记录`,
                                showSizeChanger: false,
                                current: commissionPage,
                                total: commissionTotal,
                                onChange: handleCommissionPageChange
                              }}
                              scroll={{ x: 'max-content' }}
                              className="responsive-table"
                              size={window.innerWidth < 768 ? "small" : "middle"}
                              columns={[
                                {
                                  title: '订单号',
                                  key: 'orderId',
                                  render: (commission: CommissionDetail) => (
                                    <span>{commission.order?.id || '-'}</span>
                                  )
                                },
                                {
                                  title: '产品名称',
                                  key: 'productTitle',
                                  render: (commission: CommissionDetail) => (
                                    <span>{commission.order?.product?.title || '未知产品'}</span>
                                  )
                                },

                                {
                                  title: '订单金额',
                                  key: 'orderAmount',
                                  render: (commission: CommissionDetail) => (
                                    <span>¥{(commission.order?.amount || 0).toFixed(2)}</span>
                                  )
                                },
                                {
                                  title: '佣金率',
                                  key: 'commissionRate',
                                  render: (commission: CommissionDetail) => (
                                    <span>{commission.commissionRate ? `${commission.commissionRate}%` : '-'}</span>
                                  )
                                },
                                {
                                  title: '佣金金额',
                                  key: 'commissionAmount',
                                  render: (commission: CommissionDetail) => {
                                    // 打印调试信息
                                    console.log('前端显示佣金金额:', commission.amount);
                                    return (
                                      <span style={{ color: '#faad14', fontWeight: 'bold' }}>¥{Number(commission.amount).toFixed(2)}</span>
                                    );
                                  }
                                },
                                {
                                  title: '时间',
                                  key: 'createdAt',
                                  render: (commission: CommissionDetail) => (
                                    <span>{commission.createdAt ? new Date(commission.createdAt).toLocaleString() : '-'}</span>
                                  )
                                },
                                {
                                  title: '状态',
                                  key: 'status',
                                  render: (commission: CommissionDetail) => (
                                    <span className={`inline-flex px-2 text-xs font-semibold leading-5 rounded-full ${
                                      commission.status === 'pending'
                                        ? 'bg-yellow-100 text-yellow-800'
                                        : commission.status === 'available'
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-blue-100 text-blue-800'
                                    }`}>
                                      {commission.status === 'pending'
                                        ? '待结算'
                                        : commission.status === 'available'
                                        ? '可提现'
                                        : '已提现'}
                                    </span>
                                  )
                                }
                              ]}
                              summary={(pageData) => {
                                let totalCommission = 0;
                                pageData.forEach((commission: CommissionDetail) => {
                                  totalCommission += commission.amount || 0;
                                });
                                
                                return (
                                  <>
                                    <Table.Summary.Row>
                                      <Table.Summary.Cell index={0} colSpan={4}>
                                        <strong>本页佣金总计</strong>
                                      </Table.Summary.Cell>
                                      <Table.Summary.Cell index={1}>
                                        <strong>¥{totalCommission.toFixed(2)}</strong>
                                      </Table.Summary.Cell>
                                      <Table.Summary.Cell index={2} colSpan={2}></Table.Summary.Cell>
                                    </Table.Summary.Row>
                                  </>
                                );
                              }}
                            />
                          </div>
                        ) : (
                          <div className="bg-white shadow rounded-lg p-6 text-center text-gray-500">
                            暂无佣金明细记录
                          </div>
                        )}
                      </div>
                      
                      {/* 分页控件 */}
                      {commissionTotal > 10 && (
                        <div className="flex justify-center mt-6">
                          <div className="flex space-x-1">
                            <button
                              onClick={() => handleCommissionPageChange(commissionPage - 1)}
                              disabled={commissionPage === 1}
                              className={`px-4 py-2 border rounded-md ${commissionPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                            >
                              上一页
                            </button>
                            
                            <div className="px-4 py-2 border bg-blue-50 text-blue-600 rounded-md">
                              {commissionPage}
                            </div>
                            
                            <button
                              onClick={() => handleCommissionPageChange(commissionPage + 1)}
                              disabled={commissionPage * 10 >= commissionTotal}
                              className={`px-4 py-2 border rounded-md ${commissionPage * 10 >= commissionTotal ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                            >
                              下一页
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </>
                )
              }
            ]}
          />
        </div>
      </div>
    </div>
  );
}

export default Withdrawals; 