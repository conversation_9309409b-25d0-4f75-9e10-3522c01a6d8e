import { Order, BaziOrder } from './src/models/index.js';
import sequelize from './src/config/database.js';

async function checkChengguInfo() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    const order = await Order.findByPk('250710210442919301', {
      include: [{
        model: BaziOrder,
        as: 'baziOrder'
      }]
    });
    
    if (!order || !order.baziOrder) {
      console.log('未找到订单或八字订单');
      return;
    }
    
    if (!order.baziOrder.reportData) {
      console.log('未找到报告数据');
      return;
    }
    
    const reportData = order.baziOrder.reportData;
    const chengguInfo = reportData['命主八字称骨信息'];
    
    console.log('=== 称骨信息详情 ===');
    console.log('称骨重量:', chengguInfo.称骨重量);
    console.log('称骨重量_命数:', chengguInfo.称骨重量_命数);
    console.log('称骨重量_歌诀:', chengguInfo.称骨重量_歌诀);
    console.log('称骨重量_歌诀释义:', chengguInfo.称骨重量_歌诀释义);
    console.log('称骨重量_命运详解:', chengguInfo.称骨重量_命运详解);

    console.log('\n=== 完整称骨信息对象 ===');
    console.log(JSON.stringify(chengguInfo, null, 2));

    console.log('\n=== 检查是否有其他称骨相关字段 ===');
    const allKeys = Object.keys(reportData);
    console.log('所有顶级字段:', allKeys);

    // 检查是否有其他包含"称骨"或"骨"的字段
    const boneRelatedKeys = allKeys.filter(key => key.includes('骨') || key.includes('称'));
    console.log('包含"骨"或"称"的字段:', boneRelatedKeys);

    // 检查原始API响应
    if (order.baziOrder.apiResponse) {
      console.log('\n=== 检查原始API响应中的称骨信息 ===');
      const apiResponse = order.baziOrder.apiResponse;
      if (apiResponse['命主八字称骨信息']) {
        console.log('API响应中的称骨信息:');
        console.log(JSON.stringify(apiResponse['命主八字称骨信息'], null, 2));
      } else {
        console.log('API响应中没有找到称骨信息字段');
        console.log('API响应的所有字段:', Object.keys(apiResponse));
      }
    }
    
  } catch (error) {
    console.error('检查失败:', error.message);
  } finally {
    await sequelize.close();
  }
}

checkChengguInfo();
