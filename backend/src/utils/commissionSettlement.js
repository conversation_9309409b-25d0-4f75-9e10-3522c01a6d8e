// 佣金结算工具函数
import { Op } from 'sequelize';
import { Commission, Order } from '../models/index.js';

/**
 * 结算待处理佣金
 * 将创建时间超过指定时间的待结算佣金状态更新为可提现状态
 * @param {number} settlementPeriodHours - 结算周期（小时）
 * @returns {Promise<{success: boolean, count: number, error?: string}>}
 */
export const settlePendingCommissions = async (settlementPeriodHours = 24) => {
  try {
    console.log(`【佣金结算】开始处理待结算佣金，结算周期：${settlementPeriodHours}小时`);
    
    // 计算结算时间点
    const settlementTime = new Date();
    settlementTime.setHours(settlementTime.getHours() - settlementPeriodHours);
    
    // 查找需要结算的佣金记录
    const pendingCommissions = await Commission.findAll({
      where: {
        status: 'pending',
        createdAt: {
          [Op.lt]: settlementTime // 创建时间早于结算时间点
        }
      },
      include: [{
        model: Order,
        as: 'order',
        where: {
          status: 'paid' // 确保订单状态为已支付
        }
      }]
    });
    
    console.log(`【佣金结算】找到 ${pendingCommissions.length} 条待结算佣金记录`);
    
    // 更新佣金状态为可提现
    let updatedCount = 0;
    for (const commission of pendingCommissions) {
      await commission.update({ status: 'available' });
      updatedCount++;
      
      console.log(`【佣金结算】佣金ID: ${commission.id}, 订单ID: ${commission.orderId}, 金额: ${commission.amount} 已更新为可提现状态`);
    }
    
    console.log(`【佣金结算】成功更新 ${updatedCount} 条佣金记录为可提现状态`);
    
    return {
      success: true,
      count: updatedCount
    };
  } catch (error) {
    console.error('【佣金结算】处理失败:', error);
    return {
      success: false,
      count: 0,
      error: error.message
    };
  }
};

export default settlePendingCommissions;
