// 增强的八字API服务 - 连接真实后端接口

import { BaziFormData } from '../types/bazi-enhanced';
import { appConfig } from '../config/app-config';

// API响应类型
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  code?: number;
}

// 订单创建响应
interface CreateOrderResponse {
  orderId: string;
  paymentUrl?: string;
  amount: number;
  status: string;
}

// 报告数据响应
interface ReportResponse {
  reportData: any;
  reportStatus: string;
  orderId: string;
}

// 历史订单响应
interface HistoryOrderResponse {
  id: string;
  customerName: string;
  createdAt: string;
  status: string;
  totalAmount: number;
  baziOrder?: {
    name: string;
    gender: string;
    birthYear: number;
    birthMonth: number;
    birthDay: number;
    birthProvince: string;
    birthCity: string;
    reportStatus: string;
  };
}

class BaziApiService {
  private baseUrl: string;
  private timeout: number;

  constructor() {
    this.baseUrl = appConfig.api.baseUrl;
    this.timeout = appConfig.api.timeout;
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    // 添加超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...defaultOptions,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('请求超时，请检查网络连接');
        }
        throw error;
      }
      
      throw new Error('网络请求失败');
    }
  }

  // 创建八字查询订单
  async createBaziOrder(formData: BaziFormData, productCode?: string): Promise<CreateOrderResponse> {
    const dateParts = formData.birthDate.split('-');
    const apiData = {
      productCode: productCode || 'default-bazi-product',
      name: formData.name,
      gender: formData.gender,
      calendarType: formData.calendarType || 'gregorian',
      birthYear: parseInt(dateParts[0]),
      birthMonth: parseInt(dateParts[1]),
      birthDay: parseInt(dateParts[2]),
      birthHour: formData.birthTime?.hour || 0,
      birthMinute: formData.birthTime?.minute || 0,
      birthProvince: formData.location?.province || '',
      birthCity: formData.location?.city || ''
    };

    console.log('创建八字订单API请求:', apiData);

    const response = await this.request<CreateOrderResponse>('/api/bazi/orders', {
      method: 'POST',
      body: JSON.stringify(apiData),
    });

    if (!response.success) {
      throw new Error(response.message || '创建订单失败');
    }

    return response.data!;
  }

  // 获取八字报告
  async getBaziReport(orderId: string): Promise<ReportResponse> {
    const response = await this.request<ReportResponse>(`/api/bazi/orders/${orderId}/report`);

    if (!response.success) {
      throw new Error(response.message || '获取报告失败');
    }

    return response.data!;
  }

  // 生成八字报告
  async generateBaziReport(orderId: string): Promise<ReportResponse> {
    const response = await this.request<ReportResponse>(`/api/bazi/orders/${orderId}/generate-report`, {
      method: 'POST',
    });

    if (!response.success) {
      throw new Error(response.message || '生成报告失败');
    }

    return response.data!;
  }

  // 直接生成八字报告（无需订单）
  async generateDirectReport(formData: BaziFormData): Promise<any> {
    const dateParts = formData.birthDate.split('-');
    const apiData = {
      xing: formData.name.charAt(0), // 姓
      ming: formData.name.slice(1),  // 名
      sex: formData.gender === 'male' ? '1' : '2',
      yearType: formData.calendarType === 'gregorian' ? '1' : '2',
      year: dateParts[0],
      month: dateParts[1].padStart(2, '0'), // 确保月份是两位数
      day: dateParts[2].padStart(2, '0'),   // 确保日期是两位数
      hour: (formData.birthTime?.hour || 0).toString().padStart(2, '0'),
      minute: (formData.birthTime?.minute || 0).toString().padStart(2, '0'),
      address: `${formData.location?.province || ''}${formData.location?.city || ''}`
    };

    console.log('直接生成八字报告API请求:', apiData);

    const response = await this.request('/api/bazi/report', {
      method: 'POST',
      body: JSON.stringify(apiData),
    });

    if (!response.success || response.code !== 0) {
      throw new Error(response.message || '生成报告失败');
    }

    return response.data;
  }

  // 获取用户的八字查询历史
  async getBaziHistory(): Promise<HistoryOrderResponse[]> {
    try {
      const response = await this.request<HistoryOrderResponse[]>('/api/bazi/orders');
      
      if (!response.success) {
        throw new Error(response.message || '获取历史记录失败');
      }

      return response.data || [];
    } catch (error) {
      // 如果获取历史记录失败，返回空数组而不是抛出错误
      console.warn('获取八字查询历史失败:', error);
      return [];
    }
  }

  // 检查订单状态
  async checkOrderStatus(orderId: string): Promise<{ status: string; paymentUrl?: string }> {
    const response = await this.request(`/api/orders/${orderId}/status`);

    if (!response.success) {
      throw new Error(response.message || '检查订单状态失败');
    }

    return response.data!;
  }

  // 重新发起支付
  async retryPayment(orderId: string): Promise<{ paymentUrl: string }> {
    const response = await this.request(`/api/orders/${orderId}/retry-payment`, {
      method: 'POST',
    });

    if (!response.success) {
      throw new Error(response.message || '重新发起支付失败');
    }

    return response.data!;
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.request('/api/health');
      return response.success;
    } catch (error) {
      console.error('API健康检查失败:', error);
      return false;
    }
  }
}

// 创建单例实例
export const baziApiService = new BaziApiService();

// 导出便捷方法
export const {
  createBaziOrder,
  getBaziReport,
  generateBaziReport,
  generateDirectReport,
  getBaziHistory,
  checkOrderStatus,
  retryPayment,
  healthCheck
} = baziApiService;

// 错误处理工具
export class BaziApiError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = 'BaziApiError';
  }
}

// API状态检查Hook（需要在React组件中使用）
import { useState, useCallback, useEffect } from 'react';

export function useApiStatus() {
  const [isOnline, setIsOnline] = useState(true);
  const [lastCheck, setLastCheck] = useState<Date | null>(null);

  const checkStatus = useCallback(async () => {
    try {
      const status = await healthCheck();
      setIsOnline(status);
      setLastCheck(new Date());
      return status;
    } catch (error) {
      setIsOnline(false);
      setLastCheck(new Date());
      return false;
    }
  }, []);

  useEffect(() => {
    // 初始检查
    checkStatus();

    // 定期检查（每30秒）
    const interval = setInterval(checkStatus, 30000);

    return () => clearInterval(interval);
  }, [checkStatus]);

  return { isOnline, lastCheck, checkStatus };
}
