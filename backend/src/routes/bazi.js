import express from 'express';
import { 
  createBaziOrder, 
  generateBaziReport, 
  getBaziReport, 
  getBaziOrders,
  generateBaziReportDirect
} from '../controllers/baziController.js';
import { auth, adminAuth } from '../middleware/auth.js';

const router = express.Router();

/**
 * 创建八字查询订单
 * POST /api/bazi/orders
 */
router.post('/orders', createBaziOrder);

/**
 * 生成八字报告（支付成功后调用）
 * POST /api/bazi/orders/:orderId/generate-report
 */
router.post('/orders/:orderId/generate-report', generateBaziReport);

/**
 * 获取八字报告
 * GET /api/bazi/orders/:orderId/report
 */
router.get('/orders/:orderId/report', getBaziReport);

/**
 * 获取八字查询订单列表（管理员）
 * GET /api/bazi/orders
 */
router.get('/orders', auth, adminAuth, getBaziOrders);

/**
 * 直接生成八字报告（无需订单）
 * POST /api/bazi/report
 */
router.post('/report', generateBaziReportDirect);

export default router; 