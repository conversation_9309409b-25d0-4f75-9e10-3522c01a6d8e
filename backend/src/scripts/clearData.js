import { Order, Commission, Withdrawal } from '../models/index.js';
import sequelize from '../config/database.js';

async function clearData() {
  const t = await sequelize.transaction();

  try {
    // 先删除提现记录（因为它依赖于佣金记录）
    console.log('正在删除提现记录...');
    await Withdrawal.destroy({
      where: {},
      transaction: t
    });
    console.log('提现记录已删除');

    // 删除佣金记录（因为它依赖于订单）
    console.log('正在删除佣金记录...');
    await Commission.destroy({
      where: {},
      transaction: t
    });
    console.log('佣金记录已删除');

    // 最后删除订单记录
    console.log('正在删除订单记录...');
    await Order.destroy({
      where: {},
      transaction: t
    });
    console.log('订单记录已删除');

    await t.commit();
    console.log('所有数据已清空！');

  } catch (error) {
    await t.rollback();
    console.error('清空数据失败:', error);
  } finally {
    process.exit(0);
  }
}

// 执行清空函数
clearData();
