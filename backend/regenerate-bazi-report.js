import { Order, BaziOrder } from './src/models/index.js';
import sequelize from './src/config/database.js';
import baziService from './src/services/baziService.js';
import { createCommissionsForOrder } from './src/services/commissionService.js';

async function regenerateBaziReport() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    const orderId = '250710210442919301';
    console.log(`重新生成订单 ${orderId} 的八字报告`);
    
    // 查找订单
    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder'
      }]
    });
    
    if (!order) {
      console.log('❌ 订单不存在');
      return;
    }
    
    if (!order.baziOrder) {
      console.log('❌ 八字订单不存在');
      return;
    }
    
    const baziOrder = order.baziOrder;
    
    console.log('📋 订单信息:');
    console.log('  ID:', order.id);
    console.log('  状态:', order.status);
    console.log('  客户姓名:', order.customerName);
    
    console.log('\n🔮 八字订单信息:');
    console.log('  姓名:', baziOrder.name);
    console.log('  性别:', baziOrder.gender);
    console.log('  出生年月日时:', `${baziOrder.birthYear}-${baziOrder.birthMonth}-${baziOrder.birthDay} ${baziOrder.birthHour}:${baziOrder.birthMinute}`);
    console.log('  出生地:', `${baziOrder.birthProvince} ${baziOrder.birthCity}`);
    console.log('  当前报告状态:', baziOrder.reportStatus);
    
    // 重置报告状态
    await baziOrder.update({ 
      reportStatus: 'pending',
      errorMessage: null 
    });
    
    console.log('\n🚀 开始生成八字报告...');
    
    try {
      // 调用八字服务生成报告
      const reportData = await baziService.generateBaziReport({
        name: baziOrder.name,
        gender: baziOrder.gender,
        calendarType: baziOrder.calendarType,
        birthYear: baziOrder.birthYear,
        birthMonth: baziOrder.birthMonth,
        birthDay: baziOrder.birthDay,
        birthHour: baziOrder.birthHour,
        birthMinute: baziOrder.birthMinute,
        birthProvince: baziOrder.birthProvince,
        birthCity: baziOrder.birthCity
      });

      if (reportData.success) {
        // 保存报告数据
        await baziOrder.update({
          reportData: reportData.data,
          reportStatus: 'generated',
          apiResponse: reportData.data
        });

        console.log('✅ 报告生成成功');
        
        // 尝试创建佣金记录
        try {
          await createCommissionsForOrder(order.id);
          console.log('✅ 佣金记录创建成功');
        } catch (commissionError) {
          console.log('⚠️ 佣金记录创建失败:', commissionError.message);
        }
        
        console.log('\n🎉 八字报告重新生成完成！');
        
      } else {
        console.log('❌ 报告生成失败:', reportData.error);
        await baziOrder.update({
          reportStatus: 'failed',
          errorMessage: reportData.error
        });
      }

    } catch (apiError) {
      console.error('❌ 八字API调用失败:', apiError);
      await baziOrder.update({
        reportStatus: 'failed',
        errorMessage: apiError.message
      });
    }
    
  } catch (error) {
    console.error('重新生成失败:', error.message);
  } finally {
    await sequelize.close();
  }
}

regenerateBaziReport();
