import sequelize from './src/config/database.js';
import Product from './src/models/Product.js';
import ProductLink from './src/models/ProductLink.js';
import User from './src/models/User.js';
import Order from './src/models/Order.js';

// 设置模型关联
Product.hasMany(ProductLink, { foreignKey: 'productId', as: 'productLinks' });
ProductLink.belongsTo(Product, { foreignKey: 'productId', as: 'product' });
ProductLink.belongsTo(User, { foreignKey: 'agentId', as: 'linkAgent' });
Order.belongsTo(ProductLink, { foreignKey: 'productLinkId', as: 'productLink' });

async function checkBaziOrders() {
  try {
    console.log('=== 检查八字订单和支付跳转问题 ===\n');
    
    // 1. 查找八字相关的订单
    console.log('1. 查找八字相关订单:');
    
    const baziOrders = await sequelize.query(`
      SELECT 
        o.id, o.status, o.amount, o.createdAt, o.paidAt,
        p.title as productTitle, p.type as productType,
        pl.code as linkCode,
        u.name as agentName
      FROM Orders o
      LEFT JOIN ProductLinks pl ON o.productLinkId = pl.id
      LEFT JOIN Products p ON pl.productId = p.id
      LEFT JOIN Users u ON pl.agentId = u.id
      WHERE p.title LIKE '%八字%' OR p.title LIKE '%命理%' OR p.title LIKE '%命盘%'
      ORDER BY o.createdAt DESC
      LIMIT 10
    `, {
      type: sequelize.QueryTypes.SELECT
    });
    
    if (baziOrders.length > 0) {
      baziOrders.forEach(order => {
        console.log(`订单ID: ${order.id}`);
        console.log(`  产品: ${order.productTitle} (类型: ${order.productType})`);
        console.log(`  状态: ${order.status}`);
        console.log(`  金额: ${order.amount}`);
        console.log(`  代理: ${order.agentName}`);
        console.log(`  创建时间: ${order.createdAt}`);
        console.log(`  支付时间: ${order.paidAt || '未支付'}`);
        console.log('---');
      });
    } else {
      console.log('没有找到八字相关订单');
    }
    
    // 2. 查找最近的所有订单（不限产品类型）
    console.log('\n2. 最近的所有订单:');
    
    const recentOrders = await sequelize.query(`
      SELECT 
        o.id, o.status, o.amount, o.createdAt, o.paidAt,
        p.title as productTitle, p.type as productType,
        u.name as agentName
      FROM Orders o
      LEFT JOIN ProductLinks pl ON o.productLinkId = pl.id
      LEFT JOIN Products p ON pl.productId = p.id
      LEFT JOIN Users u ON pl.agentId = u.id
      ORDER BY o.createdAt DESC
      LIMIT 5
    `, {
      type: sequelize.QueryTypes.SELECT
    });
    
    if (recentOrders.length > 0) {
      recentOrders.forEach(order => {
        console.log(`订单ID: ${order.id}, 产品: ${order.productTitle}, 类型: ${order.productType}, 状态: ${order.status}`);
      });
    } else {
      console.log('没有找到任何订单');
    }
    
    // 3. 检查八字产品的状态
    console.log('\n3. 八字产品状态检查:');
    
    const baziProducts = await Product.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.or]: [
            { [sequelize.Sequelize.Op.like]: '%八字%' },
            { [sequelize.Sequelize.Op.like]: '%命理%' },
            { [sequelize.Sequelize.Op.like]: '%命盘%' }
          ]
        }
      },
      attributes: ['id', 'title', 'type', 'status', 'price']
    });
    
    baziProducts.forEach(product => {
      console.log(`产品ID: ${product.id}, 标题: ${product.title}`);
      console.log(`  类型: ${product.type}, 状态: ${product.status}, 价格: ${product.price}`);
      
      if (product.status === 'inactive') {
        console.log(`  ⚠️  警告: 产品状态为inactive，可能影响支付流程`);
      }
    });
    
    // 4. 模拟支付跳转逻辑测试
    console.log('\n4. 支付跳转逻辑测试:');
    
    const testCases = [
      { title: '玄易八字命盘', type: 'service' },
      { title: '专业八字命理查询服务', type: 'service' },
      { title: '儿童纪录片', type: 'physical' }
    ];
    
    testCases.forEach(testCase => {
      const isBaziService = testCase.type === 'service' && 
                           (testCase.title.includes('八字') || testCase.title.includes('命理'));
      
      let redirectUrl;
      if (isBaziService) {
        redirectUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=test123`;
      } else {
        redirectUrl = process.env.PAID_RETURN_URL;
      }
      
      console.log(`产品: ${testCase.title}`);
      console.log(`  是否八字服务: ${isBaziService}`);
      console.log(`  跳转地址: ${redirectUrl}`);
    });
    
    console.log('\n5. 环境变量:');
    console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
    console.log(`PAID_RETURN_URL: ${process.env.PAID_RETURN_URL}`);
    
  } catch (error) {
    console.error('检查八字订单时出错:', error);
  } finally {
    await sequelize.close();
  }
}

checkBaziOrders();