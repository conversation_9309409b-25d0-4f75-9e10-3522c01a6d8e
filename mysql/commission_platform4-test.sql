/*
 Navicat Premium Dump SQL

 Source Server         : commission_platform
 Source Server Type    : MySQL
 Source Server Version : 80403 (8.4.3)
 Source Host           : localhost:3306
 Source Schema         : commission_platform4

 Target Server Type    : MySQL
 Target Server Version : 80403 (8.4.3)
 File Encoding         : 65001

 Date: 11/01/2025 17:44:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for Commissions
-- ----------------------------
DROP TABLE IF EXISTS `Commissions`;
CREATE TABLE `Commissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','available','withdrawn') DEFAULT 'pending',
  `orderId` varchar(25) DEFAULT NULL,
  `agentId` int NOT NULL,
  `withdrawalId` int DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON> `orderId` (`orderId`),
  KEY `agentId` (`agentId`),
  KEY `withdrawalId` (`withdrawalId`),
  CONSTRAINT `commissions_ibfk_1` FOREIGN KEY (`orderId`) REFERENCES `Orders` (`id`),
  CONSTRAINT `commissions_ibfk_2` FOREIGN KEY (`agentId`) REFERENCES `Users` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `commissions_ibfk_3` FOREIGN KEY (`withdrawalId`) REFERENCES `Withdrawals` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of Commissions
-- ----------------------------
BEGIN;
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (1, 0.08, 'available', '241229002657322', 1, NULL, '2024-12-28 16:27:56', '2024-12-28 16:27:56');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (2, 0.08, 'available', '241229153909889', 2, NULL, '2024-12-29 07:40:04', '2024-12-29 07:40:04');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (3, 0.02, 'available', '241229153909889', 1, NULL, '2024-12-29 07:40:04', '2024-12-29 07:40:04');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (4, 0.08, 'available', '241229155522671', 3, NULL, '2024-12-29 07:55:51', '2024-12-29 07:55:51');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (5, 0.02, 'available', '241229155522671', 2, NULL, '2024-12-29 07:55:51', '2024-12-29 07:55:51');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (6, 0.08, 'available', '250110223744199231', 2, NULL, '2025-01-10 14:42:23', '2025-01-10 14:42:23');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (7, 0.02, 'available', '250110223744199231', 1, NULL, '2025-01-10 14:42:23', '2025-01-10 14:42:23');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (8, 0.08, 'available', '250110225249314790', 3, NULL, '2025-01-10 14:53:40', '2025-01-10 14:53:40');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (9, 0.02, 'available', '250110225249314790', 2, NULL, '2025-01-10 14:53:40', '2025-01-10 14:53:40');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (10, 0.08, 'available', '250111011428737237', 3, NULL, '2025-01-10 17:15:28', '2025-01-10 17:15:28');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (11, 0.02, 'available', '250111011428737237', 2, NULL, '2025-01-10 17:15:28', '2025-01-10 17:15:28');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (12, 0.06, 'available', '250111015128844443', 3, NULL, '2025-01-10 17:52:45', '2025-01-10 17:52:45');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (13, 0.02, 'available', '250111015128844443', 2, NULL, '2025-01-10 17:52:45', '2025-01-10 17:52:45');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (14, 0.00, 'available', '250111021428114662', 3, NULL, '2025-01-10 18:15:06', '2025-01-10 18:15:06');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (15, 0.00, 'available', '250111022227165214', 3, NULL, '2025-01-10 18:22:56', '2025-01-10 18:22:56');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (16, 0.04, 'available', '250111023627681196', 3, NULL, '2025-01-10 18:36:59', '2025-01-10 18:36:59');
INSERT INTO `Commissions` (`id`, `amount`, `status`, `orderId`, `agentId`, `withdrawalId`, `createdAt`, `updatedAt`) VALUES (17, 0.04, 'available', '250111023627681196', 2, NULL, '2025-01-10 18:36:59', '2025-01-10 18:36:59');
COMMIT;

-- ----------------------------
-- Table structure for InviteLinks
-- ----------------------------
DROP TABLE IF EXISTS `InviteLinks`;
CREATE TABLE `InviteLinks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL,
  `agentId` int NOT NULL,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `usageCount` int DEFAULT '0',
  `expiresAt` datetime DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `agentId` (`agentId`),
  CONSTRAINT `invitelinks_ibfk_1` FOREIGN KEY (`agentId`) REFERENCES `Users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Records of InviteLinks
-- ----------------------------
BEGIN;
COMMIT;

-- ----------------------------
-- Table structure for Orders
-- ----------------------------
DROP TABLE IF EXISTS `Orders`;
CREATE TABLE `Orders` (
  `id` varchar(25) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','paid','cancelled') DEFAULT 'pending',
  `customerName` varchar(255) DEFAULT NULL,
  `customerPhone` varchar(255) DEFAULT NULL,
  `productId` int NOT NULL,
  `agentId` int NOT NULL,
  `parentAgentId` int DEFAULT NULL,
  `productLinkId` int DEFAULT NULL,
  `agentCommission` decimal(10,2) DEFAULT '0.00',
  `parentAgentCommission` decimal(10,2) DEFAULT '0.00',
  `paidAt` datetime DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `adminCommission` decimal(10,2) DEFAULT '0.00' COMMENT '管理员佣金',
  PRIMARY KEY (`id`),
  KEY `productId` (`productId`),
  KEY `agentId` (`agentId`),
  KEY `parentAgentId` (`parentAgentId`),
  KEY `productLinkId` (`productLinkId`),
  CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `Products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`agentId`) REFERENCES `Users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `orders_ibfk_3` FOREIGN KEY (`parentAgentId`) REFERENCES `Users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `orders_ibfk_4` FOREIGN KEY (`productLinkId`) REFERENCES `ProductLinks` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `orders_ibfk_5` FOREIGN KEY (`agentId`) REFERENCES `Users` (`id`),
  CONSTRAINT `orders_ibfk_6` FOREIGN KEY (`parentAgentId`) REFERENCES `Users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of Orders
-- ----------------------------
BEGIN;
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('1', 29.90, 'pending', NULL, NULL, 1, 1, NULL, 1, 0.00, 0.00, NULL, '2024-12-19 16:49:13', '2024-12-19 16:49:13', 0.00);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('2', 29.90, 'pending', NULL, NULL, 1, 1, NULL, 1, 0.00, 0.00, NULL, '2024-12-19 16:49:30', '2024-12-19 16:49:30', 0.00);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('241229002309831', 29.90, 'pending', NULL, NULL, 1, 1, NULL, 1, 0.00, 0.00, NULL, '2024-12-28 16:23:09', '2024-12-28 16:23:09', 29.90);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('241229002657322', 0.10, 'paid', NULL, NULL, 1, 1, NULL, 1, 0.08, 0.00, '2024-12-28 16:27:56', '2024-12-28 16:26:57', '2024-12-28 16:27:56', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('241229153909889', 0.10, 'paid', NULL, NULL, 1, 2, 1, 2, 0.08, 0.02, '2024-12-29 07:40:04', '2024-12-29 07:39:09', '2024-12-29 07:40:04', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('241229155522671', 0.10, 'paid', NULL, NULL, 1, 3, 2, 3, 0.08, 0.02, '2024-12-29 07:55:51', '2024-12-29 07:55:22', '2024-12-29 07:55:51', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250109140052995533', 0.10, 'pending', NULL, NULL, 1, 2, 1, 2, 0.00, 0.00, NULL, '2025-01-09 06:00:52', '2025-01-09 06:00:52', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250110170431409061', 0.10, 'pending', NULL, NULL, 1, 2, 1, 2, 0.00, 0.00, NULL, '2025-01-10 09:04:31', '2025-01-10 09:04:31', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250110170432303993', 0.10, 'pending', NULL, NULL, 1, 2, 1, 2, 0.00, 0.00, NULL, '2025-01-10 09:04:32', '2025-01-10 09:04:32', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250110223744199231', 0.10, 'paid', NULL, NULL, 1, 2, 1, 2, 0.08, 0.02, '2025-01-10 14:42:23', '2025-01-10 14:37:44', '2025-01-10 14:42:23', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250110225249314790', 0.10, 'paid', NULL, NULL, 1, 3, 2, 3, 0.08, 0.02, '2025-01-10 14:53:40', '2025-01-10 14:52:49', '2025-01-10 14:53:40', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250111011428737237', 0.10, 'paid', NULL, NULL, 1, 3, 2, 3, 0.08, 0.02, '2025-01-10 17:15:28', '2025-01-10 17:14:28', '2025-01-10 17:15:28', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250111015128844443', 0.10, 'paid', NULL, NULL, 1, 3, 2, 3, 0.06, 0.02, '2025-01-10 17:52:45', '2025-01-10 17:51:28', '2025-01-10 17:52:45', 0.02);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250111021428114662', 0.10, 'paid', NULL, NULL, 1, 3, 2, 3, 0.00, 0.00, '2025-01-10 18:15:06', '2025-01-10 18:14:28', '2025-01-10 18:15:06', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250111022227165214', 0.10, 'paid', NULL, NULL, 1, 3, 2, 3, 0.00, 0.00, '2025-01-10 18:22:56', '2025-01-10 18:22:27', '2025-01-10 18:22:56', 0.10);
INSERT INTO `Orders` (`id`, `amount`, `status`, `customerName`, `customerPhone`, `productId`, `agentId`, `parentAgentId`, `productLinkId`, `agentCommission`, `parentAgentCommission`, `paidAt`, `createdAt`, `updatedAt`, `adminCommission`) VALUES ('250111023627681196', 0.10, 'paid', NULL, NULL, 1, 3, 2, 3, 0.04, 0.04, '2025-01-10 18:36:59', '2025-01-10 18:36:27', '2025-01-10 18:36:59', 0.02);
COMMIT;

-- ----------------------------
-- Table structure for ProductLinks
-- ----------------------------
DROP TABLE IF EXISTS `ProductLinks`;
CREATE TABLE `ProductLinks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `code` varchar(255) NOT NULL,
  `productId` int NOT NULL,
  `agentId` int NOT NULL,
  `commissionRate` float NOT NULL DEFAULT '80',
  `clicks` int NOT NULL DEFAULT '0',
  `sales` int NOT NULL DEFAULT '0',
  `totalCommission` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status` enum('active','inactive') DEFAULT 'active',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `product_links_code` (`code`),
  UNIQUE KEY `code_2` (`code`),
  KEY `product_links_product_id` (`productId`),
  KEY `product_links_agent_id` (`agentId`),
  CONSTRAINT `productlinks_ibfk_1` FOREIGN KEY (`productId`) REFERENCES `Products` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `productlinks_ibfk_2` FOREIGN KEY (`agentId`) REFERENCES `Users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of ProductLinks
-- ----------------------------
BEGIN;
INSERT INTO `ProductLinks` (`id`, `code`, `productId`, `agentId`, `commissionRate`, `clicks`, `sales`, `totalCommission`, `status`, `createdAt`, `updatedAt`) VALUES (1, 'HGVy2eXnaw', 1, 1, 80, 50, 1, 0.08, 'active', '2024-12-19 16:49:07', '2024-12-28 16:27:56');
INSERT INTO `ProductLinks` (`id`, `code`, `productId`, `agentId`, `commissionRate`, `clicks`, `sales`, `totalCommission`, `status`, `createdAt`, `updatedAt`) VALUES (2, 'EMZla_HgAf', 1, 2, 80, 19, 2, 0.16, 'active', '2024-12-29 07:37:18', '2025-01-10 14:42:23');
INSERT INTO `ProductLinks` (`id`, `code`, `productId`, `agentId`, `commissionRate`, `clicks`, `sales`, `totalCommission`, `status`, `createdAt`, `updatedAt`) VALUES (3, 'KLZ2IGLlEk', 1, 3, 80, 21, 7, 0.34, 'active', '2024-12-29 07:55:14', '2025-01-10 18:36:59');
COMMIT;

-- ----------------------------
-- Table structure for Products
-- ----------------------------
DROP TABLE IF EXISTS `Products`;
CREATE TABLE `Products` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `baseCommissionRate` float NOT NULL DEFAULT '0',
  `status` enum('active','inactive') DEFAULT 'active',
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of Products
-- ----------------------------
BEGIN;
INSERT INTO `Products` (`id`, `title`, `description`, `price`, `baseCommissionRate`, `status`, `createdAt`, `updatedAt`) VALUES (1, '儿童纪录片', '一部精彩的儿童纪录片，适合3-12岁儿童观看。\n\n通过生动有趣的画面和故事，帮助孩子了解自然、科学和文化。\n\n包含多个精彩单元：\n1. 神奇的自然世界\n2. 有趣的科学实验\n3. 世界各地的文化习俗\n4. 动物们的故事\n\n每个单元都经过精心设计，既有趣又有教育意义。', 0.10, 80, 'active', '2024-12-19 16:48:02', '2024-12-19 16:48:02');
COMMIT;

-- ----------------------------
-- Table structure for SequelizeMeta
-- ----------------------------
DROP TABLE IF EXISTS `SequelizeMeta`;
CREATE TABLE `SequelizeMeta` (
  `name` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  PRIMARY KEY (`name`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;

-- ----------------------------
-- Records of SequelizeMeta
-- ----------------------------
BEGIN;
INSERT INTO `SequelizeMeta` (`name`) VALUES ('20241224-add-admin-commission.cjs');
INSERT INTO `SequelizeMeta` (`name`) VALUES ('20241228-add-commission-fields.cjs');
INSERT INTO `SequelizeMeta` (`name`) VALUES ('20241228-fix-commission-fields.cjs');
INSERT INTO `SequelizeMeta` (`name`) VALUES ('20241228-fix-orders-schema.cjs');
COMMIT;

-- ----------------------------
-- Table structure for Users
-- ----------------------------
DROP TABLE IF EXISTS `Users`;
CREATE TABLE `Users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `level` int NOT NULL DEFAULT '1',
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `role` enum('user','admin') DEFAULT 'user',
  `commissionRate` float DEFAULT NULL,
  `parentAgentId` int DEFAULT NULL,
  `approvedAt` datetime DEFAULT NULL,
  `approvedBy` int DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `totalCommission` decimal(10,2) DEFAULT '0.00' COMMENT '累积佣金总额',
  `monthlyCommission` decimal(10,2) DEFAULT '0.00' COMMENT '当月佣金总额',
  `lastCommissionReset` datetime DEFAULT NULL COMMENT '上次佣金重置时间（用于月度统计）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `username_2` (`username`),
  KEY `parentAgentId` (`parentAgentId`),
  KEY `approvedBy` (`approvedBy`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`parentAgentId`) REFERENCES `Users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `users_ibfk_2` FOREIGN KEY (`approvedBy`) REFERENCES `Users` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of Users
-- ----------------------------
BEGIN;
INSERT INTO `Users` (`id`, `username`, `password`, `name`, `phone`, `level`, `status`, `role`, `commissionRate`, `parentAgentId`, `approvedAt`, `approvedBy`, `createdAt`, `updatedAt`, `totalCommission`, `monthlyCommission`, `lastCommissionReset`) VALUES (1, 'admin', '$2a$08$WBxrpqAAv9y5SaGsEivLvOVzHtuPjBPoVRbZN9Oyi/r8BOFHkRIGi', '管理员', '13800138000', 0, 'approved', 'admin', NULL, NULL, NULL, NULL, '2024-12-19 16:48:02', '2024-12-19 16:48:02', 0.00, 0.00, NULL);
INSERT INTO `Users` (`id`, `username`, `password`, `name`, `phone`, `level`, `status`, `role`, `commissionRate`, `parentAgentId`, `approvedAt`, `approvedBy`, `createdAt`, `updatedAt`, `totalCommission`, `monthlyCommission`, `lastCommissionReset`) VALUES (2, 'agent1', '$2a$08$uiU76gd.fupWEWif1MiUCOEzL8W9vQL0G4PEpWe9nVMjIdu741e.S', '一级代理', '13849476371', 1, 'approved', 'user', 0.8, 1, NULL, NULL, '2024-12-28 16:43:15', '2024-12-28 16:43:52', 0.00, 0.00, NULL);
INSERT INTO `Users` (`id`, `username`, `password`, `name`, `phone`, `level`, `status`, `role`, `commissionRate`, `parentAgentId`, `approvedAt`, `approvedBy`, `createdAt`, `updatedAt`, `totalCommission`, `monthlyCommission`, `lastCommissionReset`) VALUES (3, 'agent2', '$2a$08$HFf9RyMtCjS9q6re4yhaE.sh/VGj68hb9CxH.y3oksipt4XtZKtFC', '二级代理', '13567876767', 2, 'approved', 'user', 0.5, 2, NULL, NULL, '2024-12-29 07:53:48', '2024-12-29 07:54:47', 0.00, 0.00, NULL);
INSERT INTO `Users` (`id`, `username`, `password`, `name`, `phone`, `level`, `status`, `role`, `commissionRate`, `parentAgentId`, `approvedAt`, `approvedBy`, `createdAt`, `updatedAt`, `totalCommission`, `monthlyCommission`, `lastCommissionReset`) VALUES (4, 'test1', '$2a$10$7Xl6NvliJzxixmsAJYLfQegj/v9CuULwZeaxzUZ5sKckM8412Grtm', '测试用户', '13507689876', 1, 'approved', 'user', 0.8, NULL, NULL, NULL, '2025-01-09 06:02:33', '2025-01-09 06:03:05', 0.00, 0.00, NULL);
INSERT INTO `Users` (`id`, `username`, `password`, `name`, `phone`, `level`, `status`, `role`, `commissionRate`, `parentAgentId`, `approvedAt`, `approvedBy`, `createdAt`, `updatedAt`, `totalCommission`, `monthlyCommission`, `lastCommissionReset`) VALUES (5, 'ag2test', '$2a$10$DyWLsSv70Ed5jkHNRFpz.O9ByyUkKHR/Fj.5r1vwDGJcuaUYlWA1u', 'ag2', '18384893948', 2, 'approved', 'user', 80, 2, NULL, NULL, '2025-01-10 08:45:51', '2025-01-10 08:46:03', 0.00, 0.00, NULL);
INSERT INTO `Users` (`id`, `username`, `password`, `name`, `phone`, `level`, `status`, `role`, `commissionRate`, `parentAgentId`, `approvedAt`, `approvedBy`, `createdAt`, `updatedAt`, `totalCommission`, `monthlyCommission`, `lastCommissionReset`) VALUES (6, 'ag22', '$2a$10$BOOGO3t6Z.DB7YpQzAf0POEUmeK0uDIsQuyDQSkMEOpimBsE/lUmm', 'ag22', '18838385895', 2, 'approved', 'user', 0.8, 2, NULL, NULL, '2025-01-10 08:49:54', '2025-01-10 08:53:26', 0.00, 0.00, NULL);
INSERT INTO `Users` (`id`, `username`, `password`, `name`, `phone`, `level`, `status`, `role`, `commissionRate`, `parentAgentId`, `approvedAt`, `approvedBy`, `createdAt`, `updatedAt`, `totalCommission`, `monthlyCommission`, `lastCommissionReset`) VALUES (7, 'ad1', '$2a$10$irgalJq6qvPdRnqgGANuUOvdwmSWCrfEmi2cIE.n9wMzwHPP7aS0y', 'ad1', '***********', 1, 'rejected', 'user', 80, 1, NULL, NULL, '2025-01-10 08:57:15', '2025-01-10 14:51:10', 0.00, 0.00, NULL);
COMMIT;

-- ----------------------------
-- Table structure for Withdrawals
-- ----------------------------
DROP TABLE IF EXISTS `Withdrawals`;
CREATE TABLE `Withdrawals` (
  `id` int NOT NULL AUTO_INCREMENT,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','approved','rejected','completed') DEFAULT 'pending',
  `agentId` int NOT NULL,
  `alipayAccount` varchar(255) NOT NULL,
  `approvedAt` datetime DEFAULT NULL,
  `approvedBy` int DEFAULT NULL,
  `completedAt` datetime DEFAULT NULL,
  `remarks` text,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  `commissionId` int DEFAULT NULL,
  `approverId` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `agentId` (`agentId`),
  KEY `approvedBy` (`approvedBy`),
  KEY `commissionId` (`commissionId`),
  KEY `approverId` (`approverId`),
  CONSTRAINT `withdrawals_ibfk_1` FOREIGN KEY (`agentId`) REFERENCES `Users` (`id`) ON UPDATE CASCADE,
  CONSTRAINT `withdrawals_ibfk_2` FOREIGN KEY (`approvedBy`) REFERENCES `Users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `withdrawals_ibfk_3` FOREIGN KEY (`commissionId`) REFERENCES `Commissions` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `withdrawals_ibfk_4` FOREIGN KEY (`approverId`) REFERENCES `Users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of Withdrawals
-- ----------------------------
BEGIN;
COMMIT;

SET FOREIGN_KEY_CHECKS = 1;
