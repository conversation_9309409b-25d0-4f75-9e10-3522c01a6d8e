import sequelize from '../config/database.js';
import { DataTypes } from 'sequelize';

// 创建 ProductImages 表
const createProductImagesTable = async () => {
  try {
    console.log('开始创建 ProductImages 表...');

    await sequelize.getQueryInterface().createTable('ProductImages', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      productId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'Products',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      url: {
        type: DataTypes.STRING,
        allowNull: false
      },
      isPrimary: {
        type: DataTypes.BOOLEAN,
        defaultValue: false
      },
      order: {
        type: DataTypes.INTEGER,
        defaultValue: 0
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
      }
    });

    console.log('ProductImages 表创建成功!');
  } catch (error) {
    console.error('创建 ProductImages 表失败:', error);
    
    // 检查表是否已存在
    if (error.name === 'SequelizeUniqueConstraintError' || error.message.includes('already exists')) {
      console.log('ProductImages 表已存在，跳过创建');
    } else {
      throw error;
    }
  }
};

// 执行迁移
createProductImagesTable()
  .then(() => {
    console.log('迁移完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('迁移失败:', error);
    process.exit(1);
  });
