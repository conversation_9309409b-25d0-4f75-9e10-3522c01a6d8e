'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 检查字段是否存在，如果不存在则添加
    const tableInfo = await queryInterface.describeTable('Users');
    
    if (!tableInfo.totalCommission) {
      await queryInterface.addColumn('Users', 'totalCommission', {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0,
        allowNull: true,
        comment: '累积佣金总额'
      });
    }

    if (!tableInfo.monthlyCommission) {
      await queryInterface.addColumn('Users', 'monthlyCommission', {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0,
        allowNull: true,
        comment: '当月佣金总额'
      });
    }

    if (!tableInfo.lastCommissionReset) {
      await queryInterface.addColumn('Users', 'lastCommissionReset', {
        type: Sequelize.DATE,
        allowNull: true,
        comment: '上次佣金重置时间（用于月度统计）'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // 如果需要回滚，则删除这些字段
    const tableInfo = await queryInterface.describeTable('Users');
    
    if (tableInfo.totalCommission) {
      await queryInterface.removeColumn('Users', 'totalCommission');
    }
    if (tableInfo.monthlyCommission) {
      await queryInterface.removeColumn('Users', 'monthlyCommission');
    }
    if (tableInfo.lastCommissionReset) {
      await queryInterface.removeColumn('Users', 'lastCommissionReset');
    }
  }
};
