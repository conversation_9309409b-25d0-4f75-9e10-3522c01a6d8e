import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, DatePicker, Radio, Card, message, ConfigProvider, Typography, Row, Col } from 'antd';
import dayjs from 'dayjs';
import zhCN from 'antd/locale/zh_CN';
import 'dayjs/locale/zh-cn';

// 导入样式
import '../styles/BaziStyles.css';

// 设置dayjs为中文
dayjs.locale('zh-cn');

const { Title } = Typography;
const { Option } = Select;

// 省份城市数据
const locationData = {
  '北京市': ['北京市'],
  '上海市': ['上海市'],
  '天津市': ['天津市'],
  '重庆市': ['重庆市'],
  '广东省': ['广州市', '深圳市', '珠海市', '汕头市', '佛山市', '韶关市', '湛江市', '肇庆市', '江门市', '茂名市', '惠州市', '梅州市', '汕尾市', '河源市', '阳江市', '清远市', '东莞市', '中山市', '潮州市', '揭阳市', '云浮市'],
  '江苏省': ['南京市', '无锡市', '徐州市', '常州市', '苏州市', '南通市', '连云港市', '淮安市', '盐城市', '扬州市', '镇江市', '泰州市', '宿迁市'],
  '浙江省': ['杭州市', '宁波市', '温州市', '嘉兴市', '湖州市', '绍兴市', '金华市', '衢州市', '舟山市', '台州市', '丽水市'],
  '山东省': ['济南市', '青岛市', '淄博市', '枣庄市', '东营市', '烟台市', '潍坊市', '济宁市', '泰安市', '威海市', '日照市', '临沂市', '德州市', '聊城市', '滨州市', '菏泽市']
};

// 时辰数据
const hours = Array.from({ length: 24 }, (_, i) => i);
const minutes = Array.from({ length: 60 }, (_, i) => i);

interface ProductInfo {
  name: string;
  price: number;
  description: string;
}

interface BaziFormProps {
  productCode?: string;
  onSubmit?: (data: any) => void;
}

const BaziForm: React.FC<BaziFormProps> = ({ productCode, onSubmit }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [productInfo, setProductInfo] = useState<ProductInfo | null>(null);
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [cities, setCities] = useState<string[]>([]);

  // 处理省份变化
  const handleProvinceChange = (province: string) => {
    setSelectedProvince(province);
    setCities(locationData[province] || []);
    form.setFieldsValue({ birthCity: undefined });
  };

  // 获取产品信息
  useEffect(() => {
    if (productCode) {
      // 模拟获取产品信息
      setProductInfo({
        name: '八字精批详细报告',
        price: 99,
        description: '专业大师解读，详细分析您的八字命理'
      });
    }
  }, [productCode]);

  // 表单提交处理
  const finalHandleSubmit = async (values: any) => {
    try {
      setSubmitting(true);
      
      const formData = {
        ...values,
        birthDate: values.birthDate?.format('YYYY-MM-DD'),
        productCode
      };

      console.log('提交的表单数据:', formData);
      
      if (onSubmit) {
        await onSubmit(formData);
      } else {
        // 默认处理逻辑
        message.success('表单提交成功！');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{
        minHeight: '100vh',
        background: 'rgb(134, 74, 48)',
        position: 'relative',
        fontFamily: 'Arial, sans-serif'
      }}>
        {/* 顶部滚动公告栏 */}
        <div style={{
          color: 'white',
          fontSize: '13px',
          padding: '6px',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          background: 'rgba(0,0,0,0.1)'
        }}>
          <div style={{
            animation: 'scroll 20s linear infinite',
            display: 'inline-block'
          }}>
            本网页数据安全由阿里云提供，严格按照法律法规和用户协议对您的信息进行全方位保护，请放心使用，测试仅供娱乐！
          </div>
        </div>

        {/* 浮动订单查询模块 */}
        <div style={{
          position: 'fixed',
          right: '10px',
          top: '50%',
          transform: 'translateY(-50%)',
          zIndex: 1000,
          background: 'rgba(0,0,0,0.7)',
          color: 'white',
          padding: '8px',
          borderRadius: '8px',
          fontSize: '12px',
          cursor: 'pointer'
        }}>
          <div>🔍</div>
          <div>订单查询</div>
        </div>

        {/* 顶部banner图片区域 */}
        <div style={{
          width: '100%',
          height: '200px',
          background: 'linear-gradient(45deg, #8B4513, #D2691E)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#FFD700',
          fontSize: '2rem',
          fontWeight: 'bold',
          textShadow: '2px 2px 4px rgba(0,0,0,0.5)'
        }}>
          八字精批
        </div>

        {/* 动画装饰区域 */}
        <div style={{
          position: 'relative',
          height: '100px',
          background: 'linear-gradient(180deg, rgba(134, 74, 48, 0.8), rgba(134, 74, 48, 1))',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <div style={{
            fontSize: '1.2rem',
            color: '#FFD700',
            textAlign: 'center'
          }}>
            ✨ 权威大师在线测算 ✨
          </div>
        </div>

        {/* 表单区域 */}
        <section style={{
          background: 'white',
          margin: '10px',
          borderRadius: '15px',
          marginTop: '-20px',
          position: 'relative',
          zIndex: 2
        }}>
          <Form form={form} onFinish={finalHandleSubmit}>
            <div style={{
              height: '40px',
              background: 'linear-gradient(45deg, #8B4513, #D2691E)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '16px',
              fontWeight: 'bold'
            }}>
              请填写您的出生信息
            </div>
            
            <div style={{ padding: '20px' }}>
              {/* 产品信息 */}
              {productInfo && (
                <div style={{
                  textAlign: 'center',
                  marginBottom: '20px',
                  padding: '15px',
                  background: 'rgba(139, 69, 19, 0.1)',
                  borderRadius: '8px'
                }}>
                  <div style={{ fontSize: '16px', fontWeight: 'bold', color: '#8B4513' }}>
                    {productInfo.name}
                  </div>
                  <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#D2691E' }}>
                    ¥{productInfo.price}
                  </div>
                </div>
              )}

              {/* 表单字段 */}
              <Form.Item 
                name="name" 
                label="姓名"
                rules={[
                  { required: true, message: '请输入您的姓名' },
                  { pattern: /^[\u4e00-\u9fa5]+$/, message: '姓名必须是中文' }
                ]}
              >
                <Input placeholder="请输入姓名(必须汉字)" />
              </Form.Item>

              <Form.Item 
                name="gender" 
                label="性别"
                rules={[{ required: true, message: '请选择性别' }]}
              >
                <Radio.Group>
                  <Radio value="male">男</Radio>
                  <Radio value="female">女</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item 
                name="birthDate" 
                label="出生日期"
                rules={[{ required: true, message: '请选择出生日期' }]}
              >
                <DatePicker 
                  placeholder="点击选择时间"
                  style={{ width: '100%' }}
                  disabledDate={(current) => current && (current > dayjs().endOf('day') || current < dayjs('1900-01-01'))}
                />
              </Form.Item>

              <Form.Item 
                name="birthProvince" 
                label="出生省份"
                rules={[{ required: true, message: '请选择出生省份' }]}
              >
                <Select placeholder="选择省份" onChange={handleProvinceChange}>
                  {Object.keys(locationData).map(province => (
                    <Option key={province} value={province}>{province}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item 
                name="birthCity" 
                label="出生城市"
                rules={[{ required: true, message: '请选择出生城市' }]}
              >
                <Select placeholder="选择城市" disabled={!selectedProvince}>
                  {cities.map(city => (
                    <Option key={city} value={city}>{city}</Option>
                  ))}
                </Select>
              </Form.Item>

              {/* 提交按钮 */}
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                loading={loading || submitting}
                style={{
                  width: '100%',
                  height: '44px',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  borderRadius: '22px',
                  background: 'linear-gradient(45deg, #8B4513, #D2691E)',
                  border: 'none',
                  color: 'white',
                  marginTop: '20px'
                }}
              >
                {(loading || submitting) ? (
                  '正在测算中...'
                ) : (
                  productCode ? '立即支付获取报告' : '立即测算八字预测'
                )}
              </Button>
            </div>
          </Form>
        </section>
      </div>
    </ConfigProvider>
  );
};

export default BaziForm;
