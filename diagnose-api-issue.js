#!/usr/bin/env node

/**
 * API 404问题诊断脚本
 * 快速诊断登录API返回404的原因
 */

import { exec } from 'child_process';
import util from 'util';
const execAsync = util.promisify(exec);

/**
 * 测试不同的API端点
 */
async function testAPIEndpoints() {
  console.log('🔍 测试API端点...');
  
  const endpoints = [
    'https://ye.bzcy.xyz/api/auth/login',
    'https://ye.bzcy.xyz/api/',
    'https://ye.bzcy.xyz/api/health',
    'https://ye.bzcy.xyz/'
  ];
  
  for (const endpoint of endpoints) {
    try {
      console.log(`\n测试: ${endpoint}`);
      const { stdout } = await execAsync(`curl -s -I "${endpoint}" | head -1`);
      console.log(`响应: ${stdout.trim()}`);
    } catch (error) {
      console.log(`错误: ${error.message}`);
    }
  }
}

/**
 * 检查本地部署脚本中的配置
 */
function checkDeploymentConfig() {
  console.log('\n🔍 检查部署配置...');
  
  // 检查关键配置
  const checks = [
    {
      name: 'Nginx配置中的代理端口',
      description: '检查nginx配置文件中API代理的端口设置'
    },
    {
      name: '后端服务端口',
      description: '检查后端index.js中的PORT配置'
    },
    {
      name: '环境变量',
      description: '检查.env文件中的端口配置'
    }
  ];
  
  checks.forEach(check => {
    console.log(`- ${check.name}: ${check.description}`);
  });
}

/**
 * 提供解决方案建议
 */
function provideSolutions() {
  console.log('\n🔧 可能的解决方案:');
  console.log('=' .repeat(50));
  
  const solutions = [
    {
      issue: '后端服务未启动',
      solution: '重新部署或手动启动后端服务',
      commands: [
        'ssh root@************',
        'cd /root/commission-platform-api',
        'pm2 restart commission-platform-api',
        'pm2 logs commission-platform-api'
      ]
    },
    {
      issue: '端口配置不匹配',
      solution: '检查Nginx配置和后端服务端口是否一致',
      commands: [
        '检查nginx配置: /etc/nginx/conf.d/commission-platform.conf',
        '检查后端配置: 确认PORT=3005'
      ]
    },
    {
      issue: 'Nginx配置问题',
      solution: '重新加载Nginx配置',
      commands: [
        'ssh root@************',
        'nginx -t',
        'systemctl reload nginx'
      ]
    }
  ];
  
  solutions.forEach((sol, index) => {
    console.log(`\n${index + 1}. ${sol.issue}`);
    console.log(`   解决方案: ${sol.solution}`);
    console.log('   执行命令:');
    sol.commands.forEach(cmd => {
      console.log(`     ${cmd}`);
    });
  });
}

/**
 * 快速修复脚本
 */
function generateQuickFix() {
  console.log('\n⚡ 快速修复建议:');
  console.log('=' .repeat(50));
  
  console.log('1. 重新运行部署脚本:');
  console.log('   ./deploy_new.sh');
  console.log('');
  
  console.log('2. 或者手动重启服务:');
  console.log('   ssh root@************');
  console.log('   pm2 restart commission-platform-api');
  console.log('   systemctl reload nginx');
  console.log('');
  
  console.log('3. 检查服务状态:');
  console.log('   pm2 status');
  console.log('   netstat -tlnp | grep :3005');
  console.log('   curl -I http://localhost:3005/api/auth/login');
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 API 404问题诊断开始...');
  console.log('问题: POST https://ye.bzcy.xyz/api/auth/login 404 (Not Found)');
  
  await testAPIEndpoints();
  checkDeploymentConfig();
  provideSolutions();
  generateQuickFix();
  
  console.log('\n✅ 诊断完成!');
  console.log('建议: 首先尝试重新运行部署脚本 ./deploy_new.sh');
}

// 运行诊断
main().catch(console.error);