export async function up(queryInterface, Sequelize) {
  await queryInterface.changeColumn('Orders', 'id', {
    type: Sequelize.STRING(16),
    allowNull: false,
    primaryKey: true
  });
}

export async function down(queryInterface, Sequelize) {
  await queryInterface.changeColumn('Orders', 'id', {
    type: Sequelize.INTEGER,
    allowNull: false,
    primaryKey: true,
    autoIncrement: true
  });
}
