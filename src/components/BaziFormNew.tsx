import React, { useState } from 'react';
import { CalendarTypeSelector } from './CalendarTypeSelector';
import { DateTimeSelector } from './DateTimeSelector';
import { AreaSelector } from './AreaSelector';

interface FormData {
  name: string;
  sex: '1' | '2'; // 1: 男, 2: 女
  birthday: string;
  address: string;
  yearType: '1' | '2'; // 1: 公历, 2: 农历
}

export const BaziFormNew: React.FC = () => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    sex: '1',
    birthday: '',
    address: '',
    yearType: '1'
  });

  // Modal状态
  const [showCalendarTypeSelector, setShowCalendarTypeSelector] = useState(false);
  const [showDateTimeSelector, setShowDateTimeSelector] = useState(false);
  const [showAreaSelector, setShowAreaSelector] = useState(false);
  const [selectedCalendarType, setSelectedCalendarType] = useState<'solar' | 'lunar'>('solar');

  // 处理公历/农历选择
  const handleCalendarTypeSelect = (type: 'solar' | 'lunar') => {
    setSelectedCalendarType(type);
    setFormData(prev => ({
      ...prev,
      yearType: type === 'solar' ? '1' : '2'
    }));
    setShowDateTimeSelector(true);
  };

  // 处理日期时间选择
  const handleDateTimeConfirm = (data: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  }) => {
    const birthdayStr = `${data.year}-${data.month.toString().padStart(2, '0')}-${data.day.toString().padStart(2, '0')} ${data.hour.toString().padStart(2, '0')}:${data.minute.toString().padStart(2, '0')}`;
    setFormData(prev => ({
      ...prev,
      birthday: birthdayStr
    }));
  };

  // 处理地区选择
  const handleAreaConfirm = (data: { province: string; city: string; district?: string }) => {
    const address = data.district ? `${data.province} ${data.city} ${data.district}` : `${data.province} ${data.city}`;
    setFormData(prev => ({
      ...prev,
      address
    }));
  };

  const handleSubmit = () => {
    if (!formData.name || !formData.birthday || !formData.address) {
      alert('请填写完整信息');
      return;
    }
    console.log('提交表单数据:', formData);
    alert('表单提交成功！（这是测试版本）');
  };

  return (
    <div className="main-form">
      <div className="bg-title"></div>
      <div className="inner">
        {/* 姓名输入 */}
        <div className="field-container">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className="field-label">姓名</div>
            <input
              className="field-input"
              type="text"
              placeholder="请输入姓名"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            />
          </div>
        </div>

        {/* 性别选择 */}
        <div className="field-container">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className="field-label">性别</div>
            <div className="radio-group" style={{ flex: 1, justifyContent: 'flex-end', paddingRight: '16px' }}>
              <label className="radio-item">
                <input
                  type="radio"
                  name="sex"
                  value="1"
                  checked={formData.sex === '1'}
                  onChange={(e) => setFormData(prev => ({ ...prev, sex: e.target.value as '1' | '2' }))}
                />
                <span>男</span>
              </label>
              <label className="radio-item">
                <input
                  type="radio"
                  name="sex"
                  value="2"
                  checked={formData.sex === '2'}
                  onChange={(e) => setFormData(prev => ({ ...prev, sex: e.target.value as '1' | '2' }))}
                />
                <span>女</span>
              </label>
            </div>
          </div>
        </div>

        {/* 生日选择 */}
        <div className="field-container">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className="field-label">生日</div>
            <div
              className="field-input"
              style={{ cursor: 'pointer', color: formData.birthday ? '#333' : '#999' }}
              onClick={() => setShowCalendarTypeSelector(true)}
            >
              {formData.birthday || '请选择生日'}
            </div>
          </div>
        </div>

        {/* 地区选择 */}
        <div className="field-container">
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className="field-label">地区</div>
            <div
              className="field-input"
              style={{ cursor: 'pointer', color: formData.address ? '#333' : '#999' }}
              onClick={() => setShowAreaSelector(true)}
            >
              {formData.address || '请选择地区'}
            </div>
          </div>
        </div>

        {/* 提交按钮 */}
        <button className="submit-button" onClick={handleSubmit}>
          立即测算
        </button>
      </div>

      {/* 模态框组件 */}
      <CalendarTypeSelector
        isOpen={showCalendarTypeSelector}
        onClose={() => setShowCalendarTypeSelector(false)}
        onSelect={handleCalendarTypeSelect}
      />

      <DateTimeSelector
        isOpen={showDateTimeSelector}
        onClose={() => setShowDateTimeSelector(false)}
        onConfirm={handleDateTimeConfirm}
        calendarType={selectedCalendarType}
      />

      <AreaSelector
        isOpen={showAreaSelector}
        onClose={() => setShowAreaSelector(false)}
        onConfirm={handleAreaConfirm}
      />
    </div>
  );
};
