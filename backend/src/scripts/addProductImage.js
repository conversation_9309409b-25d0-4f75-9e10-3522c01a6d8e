import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { ProductImage } from '../models/index.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * 为Deepseek课程添加产品图片
 * @param {number} productId - 产品ID
 */
async function addProductImage(productId) {
  try {
    console.log(`开始为产品ID ${productId} 添加图片...`);
    
    // 创建图片记录
    const productImage = await ProductImage.create({
      productId: productId,
      url: 'https://img.freepik.com/free-vector/artificial-intelligence-concept-landing-page_23-2148227480.jpg',
      isPrimary: true,
      order: 1
    });
    
    console.log('产品图片添加成功:', {
      id: productImage.id,
      productId: productImage.productId,
      url: productImage.url
    });
    
    return productImage;
  } catch (error) {
    console.error('添加产品图片失败:', error);
    throw error;
  }
}

// 从命令行参数获取产品ID
const productId = process.argv[2];

if (!productId) {
  console.error('请提供产品ID作为参数');
  process.exit(1);
}

// 执行函数
addProductImage(productId)
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
