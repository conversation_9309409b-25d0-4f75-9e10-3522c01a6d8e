import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { settlePendingCommissions } from './utils/commissionSettlement.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 根据环境加载相应的配置文件
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.develop';
const envPath = path.resolve(process.cwd(), envFile);

console.log(`正在加载环境配置文件: ${envPath}`);

// 使用dotenv加载环境变量
dotenv.config({ path: envPath });

// 打印当前环境信息
console.log('当前运行环境：', process.env.NODE_ENV);
console.log('支付宝配置检查：', {
  appId: process.env.ALIPAY_APPID ? '已配置' : '未配置',
  privateKeyLength: process.env.ALIPAY_PRIVATE_KEY ? process.env.ALIPAY_PRIVATE_KEY.length : 0,
  publicKeyLength: process.env.ALIPAY_PUBLIC_KEY ? process.env.ALIPAY_PUBLIC_KEY.length : 0,
  notifyUrl: process.env.ALIPAY_NOTIFY_URL,
  returnUrl: process.env.ALIPAY_RETURN_URL
});

import express from 'express';
import cors from 'cors';
import morgan from 'morgan';
import sequelize from './config/database.js';
import { adminData, productData } from './config/seedData.js';
import dashboardRoutes from './routes/dashboard.js';
import authRoutes from './routes/auth.js';
import productRoutes from './routes/products.js';
import orderRoutes from './routes/orders.js';
import commissionRoutes from './routes/commissions.js';
import withdrawalRoutes from './routes/withdrawals.js';
import productLinksRoutes from './routes/productLinks.js';
import paymentRoutes from './routes/payment.js';
import paymentConfigRoutes from './routes/paymentConfig.js';
import userManagementRoutes from './routes/userManagement.js';
import userRoutes from './routes/users.js';
import categoryRoutes from './routes/categories.js';
import productImagesRoutes from './routes/productImages.js';
import baziRoutes from './routes/bazi.js';
import namingOrderRoutes from './routes/namingOrders.js';
import systemConfigRoutes from './routes/systemConfig.js';

import setupAssociations, { User, Product, Order, Commission, Withdrawal, ProductLink, ProductImage } from './models/associations.js';
import { SystemConfig } from './models/index.js';

const app = express();

// CORS 配置 - 从环境变量中读取允许的源
let corsOrigins = process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : ['https://ye.bzcy.xyz','http://************:3000'];

// 开发环境下，自动允许所有本地请求
if (process.env.NODE_ENV === 'development') {
  // 添加常用的本地开发地址
  corsOrigins.push('http://localhost:*');
  corsOrigins.push('http://127.0.0.1:*');
}

console.log('允许的CORS源:', corsOrigins);

// 自定义CORS处理，支持通配符
app.use((req, res, next) => {
  const origin = req.headers.origin;
  
  // 如果没有来源头，继续处理
  if (!origin) {
    return next();
  }
  
  // 检查是否匹配任何允许的来源（支持通配符）
  let allowed = false;
  for (const pattern of corsOrigins) {
    // 将通配符转换为正则表达式
    if (pattern.includes('*')) {
      const regexPattern = pattern.replace(/\*/g, '.*');
      const regex = new RegExp(`^${regexPattern}$`);
      if (regex.test(origin)) {
        allowed = true;
        break;
      }
    } else if (pattern === origin) {
      allowed = true;
      break;
    }
  }
  
  // 如果允许，设置CORS头
  if (allowed) {
    res.header('Access-Control-Allow-Origin', origin);
    res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Content-Length, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true'); // 允许携带认证信息
  }
  
  // 处理预检请求
  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }
  
  next();
});

// Middleware
app.use(morgan('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));  // 支持 application/x-www-form-urlencoded

// 请求日志中间件
app.use((req, res, next) => {
  console.log('收到请求:', {
    method: req.method,
    url: req.url,
    query: req.query,
    body: req.body,
    headers: req.headers
  });
  next();
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/dashboard', dashboardRoutes);  // 添加数据看板路由
app.use('/api/products', productRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/commissions', commissionRoutes);
app.use('/api/withdrawals', withdrawalRoutes);
app.use('/api/product-links', productLinksRoutes);
app.use('/api/payment', paymentRoutes);  // 保留这个路由用于API调用
app.use('/payment', paymentRoutes);      // 用于支付宝回调
app.use('/api/payment-config', paymentConfigRoutes);  // 支付配置路由
app.use('/api/user-management', userManagementRoutes);  // 使用用户管理路由
app.use('/api/categories', categoryRoutes);  // 添加分类路由
app.use('/api/product-images', productImagesRoutes);  // 添加产品图片路由
app.use('/api/bazi', baziRoutes);  // 添加八字查询路由
app.use('/api/orders/naming', namingOrderRoutes);  // 添加起名业务订单路由
app.use('/api/system-config', systemConfigRoutes);  // 添加系统配置路由

// 404 处理
app.use((req, res) => {
  console.error('路由未找到:', req.method, req.url, '\n请求参数:', req.query);
  res.status(404).json({ 
    success: false, 
    error: `路由未找到: ${req.method} ${req.url}` 
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error('服务器错误:', err);
  res.status(500).json({ 
    success: false, 
    error: '服务器内部错误',
    details: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// 数据库连接和表同步
async function initializeDatabase() {
  try {
    // 安全检查：确保关键环境变量已设置
    const requiredEnvVars = ['DB_NAME', 'DB_USER', 'DB_HOST', 'JWT_SECRET'];
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

    if (missingVars.length > 0) {
      console.error('❌ 缺少必要的环境变量:', missingVars);
      process.exit(1);
    }

    // 先设置关联关系
    setupAssociations();

    // 同步表结构，但不删除数据
    if (process.env.FORCE_SYNC === 'true') {
      // 只在明确需要时才执行表结构同步
      console.log('强制执行表结构同步...');
      await sequelize.sync({ alter: true });
    } else {
      // 默认情况下只验证连接，不修改表结构
      await sequelize.authenticate();
      console.log('数据库连接验证成功');
    }

    console.log('数据库连接成功，表已同步');

    // 只在明确需要时才执行数据初始化
    const shouldInitializeData = process.env.FORCE_INIT === 'true' || process.env.INIT_DATA === 'true';

    if (shouldInitializeData) {
      console.log('执行数据初始化检查...');

      // 检查并创建管理员账号（如果不存在）
      const existingAdmin = await User.findOne({ where: { username: 'admin' } });
      if (!existingAdmin) {
        const admin = await User.create(adminData);
        console.log('管理员账号创建成功:', admin.username);
      } else {
        console.log('管理员账号已存在，跳过创建');
      }

      // 检查并创建测试产品（如果不存在）
      const existingProduct = await Product.findOne({ where: { title: '儿童纪录片' } });
      if (!existingProduct) {
        const product = await Product.create(productData);
        console.log('测试产品创建成功:', {
          id: product.id,
          title: product.title,
          price: product.price,
          baseCommissionRate: product.baseCommissionRate
        });
      } else {
        console.log('测试产品已存在，跳过创建');
      }

      console.log('数据初始化检查完成');
    } else {
      console.log('跳过数据初始化检查（如需执行，请设置 FORCE_INIT=true）');
    }

  } catch (error) {
    console.error('数据库初始化失败:', error);
  }
}

// 启动服务器
const PORT = process.env.PORT || 3006;
const server = app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
  
  // 启动佣金结算定时任务
  startCommissionSettlementTask();
});

initializeDatabase();

/**
 * 启动佣金结算定时任务
 * 每小时执行一次，将创建时间超过1天的待结算佣金状态更新为可提现状态
 */
function startCommissionSettlementTask() {
  console.log('【系统】佣金结算定时任务已启动，每小时执行一次');
  
  // 立即执行一次佣金结算
  settlePendingCommissions(24).then(result => {
    console.log(`【系统】初始佣金结算完成，结果:`, result);
  });
  
  // 设置定时任务，每小时执行一次
  setInterval(async () => {
    try {
      console.log('【系统】执行定时佣金结算任务...');
      const result = await settlePendingCommissions(24);
      console.log(`【系统】佣金结算任务完成，结果:`, result);
    } catch (error) {
      console.error('【系统】佣金结算任务执行失败:', error);
    }
  }, 60 * 60 * 1000); // 每小时执行一次
}