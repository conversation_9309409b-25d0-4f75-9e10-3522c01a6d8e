import React from 'react';
import { useBaziStore } from '../store/baziStore';
import { CalendarTypeSelector } from './CalendarTypeSelector';
import { DateTimeSelector } from './DateTimeSelector';
import { AreaSelector } from './AreaSelector';

// 姓名输入组件
export const NameField: React.FC = () => {
  const { formData, setFormData } = useBaziStore();

  return (
    <div className="field-container">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div className="field-label">姓名</div>
        <input
          className="field-input"
          type="text"
          placeholder="请输入姓名"
          value={formData.name}
          onChange={(e) => setFormData({ name: e.target.value })}
        />
      </div>
    </div>
  );
};

// 性别选择组件
export const GenderField: React.FC = () => {
  const { formData, setFormData } = useBaziStore();

  return (
    <div className="field-container">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div className="field-label">性别</div>
        <div className="radio-group" style={{ flex: 1, justifyContent: 'flex-end', paddingRight: '16px' }}>
          <label className="radio-item">
            <input
              type="radio"
              name="sex"
              value="1"
              checked={formData.sex === '1'}
              onChange={(e) => setFormData({ sex: e.target.value as '1' | '2' })}
            />
            <span>男</span>
          </label>
          <label className="radio-item">
            <input
              type="radio"
              name="sex"
              value="2"
              checked={formData.sex === '2'}
              onChange={(e) => setFormData({ sex: e.target.value as '1' | '2' })}
            />
            <span>女</span>
          </label>
        </div>
      </div>
    </div>
  );
};

// 生日选择组件
export const BirthdayField: React.FC = () => {
  const { 
    formData, 
    showCalendarTypeSelector, 
    showDateTimeSelector,
    selectedCalendarType,
    setShowCalendarTypeSelector,
    setShowDateTimeSelector,
    setSelectedCalendarType,
    setDateTime
  } = useBaziStore();

  // 处理公历/农历选择
  const handleCalendarTypeSelect = (type: 'solar' | 'lunar') => {
    setSelectedCalendarType(type);
    setShowDateTimeSelector(true);
  };

  // 处理日期时间选择
  const handleDateTimeConfirm = (data: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  }) => {
    setDateTime(data);
  };

  return (
    <>
      <div className="field-container">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div className="field-label">生日</div>
          <div
            className="field-input"
            style={{ cursor: 'pointer', color: formData.birthday ? '#333' : '#999' }}
            onClick={() => setShowCalendarTypeSelector(true)}
          >
            {formData.birthday || '请选择生日'}
          </div>
        </div>
      </div>

      <CalendarTypeSelector
        isOpen={showCalendarTypeSelector}
        onClose={() => setShowCalendarTypeSelector(false)}
        onSelect={handleCalendarTypeSelect}
      />

      <DateTimeSelector
        isOpen={showDateTimeSelector}
        onClose={() => setShowDateTimeSelector(false)}
        onConfirm={handleDateTimeConfirm}
        calendarType={selectedCalendarType}
      />
    </>
  );
};

// 地区选择组件
export const AddressField: React.FC = () => {
  const { 
    formData, 
    showAreaSelector,
    setShowAreaSelector,
    setArea
  } = useBaziStore();

  // 处理地区选择
  const handleAreaConfirm = (data: { province: string; city: string; district?: string }) => {
    setArea(data);
  };

  return (
    <>
      <div className="field-container">
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <div className="field-label">地区</div>
          <div
            className="field-input"
            style={{ cursor: 'pointer', color: formData.address ? '#333' : '#999' }}
            onClick={() => setShowAreaSelector(true)}
          >
            {formData.address || '请选择地区'}
          </div>
        </div>
      </div>

      <AreaSelector
        isOpen={showAreaSelector}
        onClose={() => setShowAreaSelector(false)}
        onConfirm={handleAreaConfirm}
      />
    </>
  );
};

// 手机号输入组件（可选）
export const PhoneField: React.FC = () => {
  const { formData, setFormData } = useBaziStore();

  return (
    <div className="field-container">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div className="field-label">手机号</div>
        <input
          className="field-input"
          type="tel"
          placeholder="请输入手机号（可选）"
          value={formData.phone || ''}
          onChange={(e) => setFormData({ phone: e.target.value })}
        />
      </div>
    </div>
  );
};
