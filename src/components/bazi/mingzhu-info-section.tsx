import type { MingZhuXinXi } from '../../types/bazi';
import { SectionCard } from './section-card';
import { InfoItem, ParagraphText } from './info-item';
import { UserCircle, Gem, Star } from 'lucide-react';

interface MingZhuInfoSectionProps {
  data: MingZhuXinXi;
}

export function MingZhuInfoSection({ data }: MingZhuInfoSectionProps) {
  const compactItemLabelClass = "text-xs sm:text-sm";
  const compactItemValueClass = "text-xs sm:text-sm";

  return (
    <SectionCard title="命主信息" icon={UserCircle}>
      <dl className="divide-y divide-border/50">
        {/* Row 1: Name, Gender, Age */}
        <div className="grid grid-cols-3 gap-x-2 md:gap-x-4 py-0">
          <InfoItem 
            label="姓名" 
            value={data.姓名} 
            direction="col" 
            className="py-1.5 sm:py-2" 
            labelClassName={compactItemLabelClass}
            valueClassName={compactItemValueClass}
          />
          <InfoItem 
            label="性别" 
            value={data.性别} 
            direction="col" 
            className="py-1.5 sm:py-2"
            labelClassName={compactItemLabelClass}
            valueClassName={compactItemValueClass}
          />
          <InfoItem 
            label="岁数" 
            value={data.岁数} 
            direction="col" 
            className="py-1.5 sm:py-2"
            labelClassName={compactItemLabelClass}
            valueClassName={compactItemValueClass}
          />
        </div>

        {/* Row 2: Constellation, Chinese Zodiac, Astrological Sign */}
        <div className="grid grid-cols-3 gap-x-2 md:gap-x-4 py-0">
          <InfoItem 
            label="星座" 
            value={data.星座} 
            direction="col" 
            className="py-1.5 sm:py-2"
            labelClassName={compactItemLabelClass}
            valueClassName={compactItemValueClass}
          />
          <InfoItem 
            label="属相" 
            value={data.属相} 
            direction="col" 
            className="py-1.5 sm:py-2"
            labelClassName={compactItemLabelClass}
            valueClassName={compactItemValueClass}
          />
          <InfoItem 
            label="文昌位" 
            value={data.文昌位} 
            direction="col" 
            className="py-1.5 sm:py-2"
            labelClassName={compactItemLabelClass}
            valueClassName={compactItemValueClass}
          />
        </div>
        
        {/* Row 3: Guardian Buddha, Tai Sui */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-x-2 md:gap-x-4 py-0">
            <InfoItem 
              label="本命佛" 
              value={data.本命佛} 
              direction="col" 
              className="py-1.5 sm:py-2"
              labelClassName={compactItemLabelClass}
              valueClassName={compactItemValueClass}
            />
            <InfoItem 
              label="太岁" 
              value={data.太岁} 
              direction="col" 
              className="py-1.5 sm:py-2"
              labelClassName={compactItemLabelClass}
              valueClassName={compactItemValueClass}
            />
        </div>
    
        {/* Standard full-width items */}
        <InfoItem label="公历生日" value={data.公历生日} />
        <InfoItem label="农历生日" value={data.农历生日} />
        <InfoItem label="出生地区" value={data.出生地区} />
        <InfoItem label="真太阳时" value={data.真太阳时} />
        <InfoItem label="命主福元" value={data.命主福元} />
      </dl>

              <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t-2 border-accent/30">
        <h3 className="bazi-title text-base sm:text-lg font-semibold mb-2 flex items-center">
          <Gem className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-accent shrink-0" />
          幸运秘诀
        </h3>
        <dl className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 divide-y sm:divide-y-0 divide-border/30">
          <InfoItem label="幸运数字" value={<span className="bazi-lucky">{data.幸运秘诀.幸运数字}</span>} />
          <InfoItem label="大凶数字" value={<span className="bazi-unlucky">{data.幸运秘诀.大凶数字}</span>} />
          <InfoItem label="吉祥颜色" value={<span className="bazi-lucky">{data.幸运秘诀.吉祥颜色}</span>} />
          <InfoItem label="忌讳颜色" value={<span className="bazi-unlucky">{data.幸运秘诀.忌讳颜色}</span>} />
          <InfoItem label="幸运花" value={data.幸运秘诀.幸运花} />
          <InfoItem label="幸运物品" value={data.幸运秘诀.幸运物品} />
        </dl>
      </div>

      <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-border/50">
        <h3 className="text-base sm:text-lg font-semibold text-primary mb-2 flex items-center">
          <Star className="mr-2 h-4 w-4 sm:h-5 sm:w-5 text-accent shrink-0" />
          二十八宿
        </h3>
        <dl className="divide-y divide-border/30">
          <InfoItem label="所属星宿" value={data.二十八宿.所属星宿} />
          <InfoItem label="星宿爱情" value={<ParagraphText text={data.二十八宿.星宿爱情} />} />
          <InfoItem label="真爱方位" value={data.二十八宿.真爱方位} />
          <InfoItem label="星宿吉祥物" value={data.二十八宿.星宿吉祥物} />
        </dl>
      </div>
    </SectionCard>
  );
} 