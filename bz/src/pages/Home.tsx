import React, { useEffect } from 'react';
import { TopSection2 } from '@/components/TopSection2';
import { FormSection2 } from '@/components/FormSection2';
import { ImageBlock2 } from '@/components/ImageBlock2';
import { Marquee } from '@/components/Marquee';
import { useAppStore } from '@/store';
import { initializeApp } from '@/store';

// 导入图片
import a0 from '/assets/theme/2/0.png';

export const Home: React.FC = () => {
  const { loading } = useAppStore();

  useEffect(() => {
    // 初始化应用
    initializeApp();

    // 设置页面背景色
    document.body.style.background = '#864a30';

    // 清理函数
    return () => {
      document.body.removeAttribute('style');
    };
  }, []);

  return (
    <div className="page home2">
      <Marquee />
      <img className="banner" src={a0} alt="Banner" />
      <TopSection2 />
      <section className="block" style={{ paddingTop: 0 }}>
        <FormSection2 />
      </section>
      <ImageBlock2 />

      {/* 全局加载状态 */}
      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            <span className="text-gray-700">加载中...</span>
          </div>
        </div>
      )}
    </div>
  );
};
