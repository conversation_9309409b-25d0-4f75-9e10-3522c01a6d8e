import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Card, Typography, Space, Button, Alert, Spin } from 'antd';
import { CheckCircleOutlined, WechatOutlined, CopyOutlined, PhoneOutlined } from '@ant-design/icons';
import { config } from '../config';

const { Title, Paragraph, Text } = Typography;

const NamingServiceSuccess: React.FC = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const orderId = searchParams.get('orderId');
  const [orderInfo, setOrderInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [copySuccess, setCopySuccess] = useState(false);
  const [baziReportId, setBaziReportId] = useState<string | null>(null);

  // 企业微信客服链接
  const wechatServiceUrl = 'https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd';

  // 客服微信号
  const wechatServiceId = 'naming_service_2024';

  // 客服电话
  const servicePhone = '************';

  useEffect(() => {
    if (orderId) {
      fetchOrderInfo();
      // 自动跳转到企业微信客服
      setTimeout(() => {
        redirectToWechatService();
      }, 3000); // 3秒后自动跳转
    } else {
      setLoading(false);
    }
  }, [orderId]);

  const fetchOrderInfo = async () => {
    try {
      // 获取起名订单信息，包括关联的八字报告ID
      const response = await fetch(`${config.apiUrl}/api/orders/naming/${orderId}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setOrderInfo(data.data);
          setBaziReportId(data.data.baziReportId);
        }
      } else {
        // 如果API不存在，使用模拟数据
        setOrderInfo({
          orderId,
          customerName: '客户',
          amount: 0.1,
          status: 'paid',
          baziReportId: 'test123' // 模拟八字报告ID
        });
        setBaziReportId('test123');
      }
    } catch (error) {
      console.error('获取订单信息失败:', error);
      // 使用模拟数据
      setOrderInfo({
        orderId,
        customerName: '客户',
        amount: 0.1,
        status: 'paid',
        baziReportId: 'test123'
      });
      setBaziReportId('test123');
    } finally {
      setLoading(false);
    }
  };

  const redirectToWechatService = () => {
    // 构建八字报告链接
    const baziReportUrl = baziReportId
      ? `${window.location.origin}/bazi-report?orderId=${baziReportId}`
      : `${window.location.origin}/bazi-report`;

    // 构建要发送的消息
    const message = `您好！我已完成起名服务支付，这是我的八字报告链接：${baziReportUrl}，请为我提供专业的起名服务。订单号：${orderId}`;

    // 将消息编码并添加到企业微信链接中
    const encodedMessage = encodeURIComponent(message);
    const wechatUrlWithMessage = `${wechatServiceUrl}?msg=${encodedMessage}`;

    console.log('跳转到企业微信客服，携带消息:', message);
    console.log('完整链接:', wechatUrlWithMessage);

    // 跳转到企业微信客服
    window.location.href = wechatUrlWithMessage;
  };

  const copyWechatId = () => {
    navigator.clipboard.writeText(wechatServiceId).then(() => {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    });
  };

  const copyPhone = () => {
    navigator.clipboard.writeText(servicePhone).then(() => {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    });
  };

  const handleManualRedirect = () => {
    redirectToWechatService();
  };

  if (loading) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        backgroundColor: '#f5f5f5'
      }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: '#f5f5f5', 
      padding: '20px',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <div style={{ maxWidth: '600px', width: '100%' }}>
        {/* 支付成功提示 */}
        <Card style={{ marginBottom: '20px', textAlign: 'center' }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <CheckCircleOutlined style={{ fontSize: '64px', color: '#52c41a' }} />
            <Title level={2} style={{ color: '#52c41a', margin: 0 }}>
              支付成功！
            </Title>
            <Paragraph style={{ fontSize: '16px', margin: 0 }}>
              您的起名服务订单已成功支付，正在为您跳转到专业起名师...
            </Paragraph>
            {orderInfo && (
              <div style={{
                backgroundColor: '#f6ffed',
                padding: '16px',
                borderRadius: '8px',
                border: '1px solid #b7eb8f'
              }}>
                <Text strong>订单号：</Text><Text code>{orderInfo.orderId}</Text><br />
                <Text strong>支付金额：</Text><Text style={{ color: '#52c41a' }}>¥{orderInfo.amount}</Text><br />
                {baziReportId && (
                  <>
                    <Text strong>八字报告：</Text>
                    <Text style={{ color: '#1890ff' }}>
                      {window.location.origin}/bazi-report?orderId={baziReportId}
                    </Text>
                  </>
                )}
              </div>
            )}

            {/* 自动跳转提示 */}
            <Alert
              message="正在自动跳转"
              description="系统将在3秒后自动跳转到企业微信客服，并发送您的八字报告链接。如果没有自动跳转，请点击下方按钮。"
              type="info"
              showIcon
              style={{ marginTop: '16px' }}
            />

            {/* 手动跳转按钮 */}
            <Button
              type="primary"
              size="large"
              icon={<WechatOutlined />}
              onClick={handleManualRedirect}
              style={{
                backgroundColor: '#52c41a',
                borderColor: '#52c41a',
                fontSize: '16px',
                height: '48px',
                padding: '0 32px'
              }}
            >
              立即联系专业起名师
            </Button>
          </Space>
        </Card>

        {/* 联系客服指引 */}
        <Card>
          <Title level={3} style={{ textAlign: 'center', marginBottom: '24px' }}>
            <WechatOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
            服务说明
          </Title>

          <Alert
            message="智能服务流程"
            description="系统已自动为您准备好八字报告链接，并将在跳转到企业微信客服时自动发送。专业起名师将根据您的八字信息为您量身定制姓名方案。"
            type="success"
            showIcon
            style={{ marginBottom: '24px' }}
          />

          <div style={{ textAlign: 'center' }}>
            {/* 如果自动跳转失败的备用方案 */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={4}>如果没有自动跳转，请手动联系</Title>
              <div style={{ marginBottom: '16px' }}>
                <Button
                  type="primary"
                  size="large"
                  icon={<WechatOutlined />}
                  onClick={handleManualRedirect}
                  style={{
                    backgroundColor: '#07c160',
                    borderColor: '#07c160',
                    marginRight: '12px'
                  }}
                >
                  打开企业微信客服
                </Button>
              </div>
              <Paragraph style={{ color: '#666', fontSize: '14px' }}>
                点击上方按钮将自动发送您的八字报告链接给专业起名师
              </Paragraph>
            </div>

            {/* 备用联系方式 */}
            <div style={{ marginBottom: '24px' }}>
              <Title level={4}>其他联系方式</Title>
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '12px',
                marginBottom: '12px'
              }}>
                <Text code style={{ fontSize: '16px', padding: '8px 12px' }}>
                  {wechatServiceId}
                </Text>
                <Button
                  icon={<CopyOutlined />}
                  onClick={copyWechatId}
                  type={copySuccess ? 'primary' : 'default'}
                >
                  {copySuccess ? '已复制' : '复制微信号'}
                </Button>
              </div>
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '12px'
              }}>
                <Text style={{ fontSize: '16px', fontWeight: 'bold', color: '#1890ff' }}>
                  <PhoneOutlined style={{ marginRight: '8px' }} />
                  {servicePhone}
                </Text>
                <Button
                  icon={<CopyOutlined />}
                  onClick={copyPhone}
                  type={copySuccess ? 'primary' : 'default'}
                >
                  {copySuccess ? '已复制' : '复制电话'}
                </Button>
              </div>
              <Paragraph style={{ color: '#666', marginTop: '8px', fontSize: '14px' }}>
                服务时间：周一至周日 9:00-21:00
              </Paragraph>
            </div>
          </div>

          {/* 服务说明 */}
          <Alert
            message="服务说明"
            description={
              <div>
                <p>• 系统已自动发送您的八字报告链接给专业起名师</p>
                <p>• 专业起名师将在24小时内与您联系</p>
                <p>• 提供3-5个精选姓名方案供您选择</p>
                <p>• 包含详细的姓名解析和运势分析</p>
                <p>• 如有任何问题，请随时联系客服</p>
              </div>
            }
            type="success"
            showIcon
          />
        </Card>
      </div>
    </div>
  );
};

export default NamingServiceSuccess;
