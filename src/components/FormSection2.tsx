import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Field } from './ui/Field';
import { Radio, RadioGroup } from './ui/Radio';
import { CalendarTypeSelector } from './CalendarTypeSelector';
import { DateTimeSelector } from './DateTimeSelector';
import { AreaSelector } from './AreaSelector';
import { useBaziStore } from '../store/baziStore';
import { message } from 'antd';
import axios from 'axios';
import { config } from '../config';

export const FormSection2: React.FC = () => {
  const { 
    formData, 
    setFormData,
    showCalendarTypeSelector, 
    showDateTimeSelector,
    showAreaSelector,
    selectedCalendarType,
    submitting,
    setShowCalendarTypeSelector,
    setShowDateTimeSelector,
    setShowAreaSelector,
    setSelectedCalendarType,
    setDateTime,
    setArea,
    setSubmitting
  } = useBaziStore();

  // 处理公历/农历选择
  const handleCalendarTypeSelect = (type: 'solar' | 'lunar') => {
    setSelectedCalendarType(type);
    setShowCalendarTypeSelector(false);
    setShowDateTimeSelector(true);
  };

  // 处理日期时间选择
  const handleDateTimeConfirm = (data: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  }) => {
    setDateTime(data);
    setShowDateTimeSelector(false);
  };

  // 处理地区选择
  const handleAreaConfirm = (data: { province: string; city: string; district?: string }) => {
    setArea(data);
    setShowAreaSelector(false);
  };

  // 验证表单数据
  const validateForm = () => {
    if (!formData.name.trim()) {
      message.error('请输入姓名');
      return false;
    }
    
    if (!formData.birthday) {
      message.error('请选择生日');
      return false;
    }
    
    if (!formData.address) {
      message.error('请选择地区');
      return false;
    }
    
    return true;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // 确认对话框
    const confirmed = window.confirm(`
请确认生日是否正确！

姓名：${formData.name}
性别：${formData.sex === '1' ? '男' : '女'}
生日：${formData.birthday}
出生地址：${formData.address}
    `);

    if (!confirmed) {
      return;
    }

    setSubmitting(true);
    
    try {
      // 构建订单数据
      const requestData = {
        name: formData.name,
        gender: formData.sex,
        calendarType: formData.yearType,
        birthYear: formData.dateTime?.year.toString() || '',
        birthMonth: formData.dateTime?.month.toString().padStart(2, '0') || '',
        birthDay: formData.dateTime?.day.toString().padStart(2, '0') || '',
        birthHour: formData.dateTime?.hour || 0,
        birthMinute: formData.dateTime?.minute || 0,
        birthProvince: formData.area?.province || '',
        birthCity: formData.area?.city || '',
        customerPhone: formData.phone || '',
        deviceId: 'web-' + Date.now(),
        clientIp: '127.0.0.1'
      };

      console.log('创建八字订单，请求数据:', requestData);

      // 这里需要产品代码，暂时使用默认值
      const productCode = 'default';
      const response = await axios.post(`${config.apiUrl}/api/orders/purchase/${productCode}`, requestData);

      console.log('订单创建成功:', response.data);

      if (response.data.paymentUrl) {
        message.success('订单创建成功，正在跳转到支付页面...');
        
        setTimeout(() => {
          window.location.href = response.data.paymentUrl;
        }, 1000);
      } else {
        throw new Error('未获取到支付链接');
      }
      
    } catch (error) {
      console.error('创建订单失败:', error);
      
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || '创建订单失败';
        message.error(errorMessage);
      } else {
        message.error('创建订单失败，请重试');
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="main-form">
      <div className="bg-title"></div>
      <div className="inner">
        {/* 姓名字段 */}
        <Field label="您的名字">
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({ name: e.target.value })}
            placeholder="请输入姓名(必须汉字)"
            style={{
              border: 'none',
              outline: 'none',
              background: 'transparent',
              fontSize: '15px',
              color: '#333',
              textAlign: 'right',
              width: '100%',
            }}
          />
        </Field>

        {/* 性别字段 */}
        <Field label="您的性别">
          <RadioGroup
            value={formData.sex}
            onChange={(value) => setFormData({ sex: value as '1' | '2' })}
          >
            <Radio name="sex" value="1">男</Radio>
            <Radio name="sex" value="2">女</Radio>
          </RadioGroup>
        </Field>

        {/* 生日字段 */}
        <Field 
          label="您的生日" 
          isLink 
          onClick={() => setShowCalendarTypeSelector(true)}
        >
          <span style={{ color: formData.birthday ? '#333' : '#999' }}>
            {formData.birthday || '请选择生日'}
          </span>
        </Field>

        {/* 地区字段 */}
        <Field 
          label="出生地址" 
          isLink 
          onClick={() => setShowAreaSelector(true)}
        >
          <span style={{ color: formData.address ? '#333' : '#999' }}>
            {formData.address || '请选择地区'}
          </span>
        </Field>

        {/* 提交按钮 */}
        <motion.button 
          className="submit" 
          onClick={handleSubmit}
          disabled={submitting}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          style={{
            width: '100%',
            height: '68px',
            border: 'none',
            background: `url('/assets/images/bz-vip-t3.png') no-repeat center`,
            backgroundSize: 'auto 100%',
            cursor: submitting ? 'not-allowed' : 'pointer',
            opacity: submitting ? 0.7 : 1
          }}
        >
          {submitting && (
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
              <div style={{
                animation: 'spin 1s linear infinite',
                borderRadius: '50%',
                width: '24px',
                height: '24px',
                border: '2px solid #ffffff',
                borderTopColor: 'transparent'
              }}></div>
            </div>
          )}
        </motion.button>
      </div>

      {/* 模态框 */}
      <CalendarTypeSelector
        isOpen={showCalendarTypeSelector}
        onClose={() => setShowCalendarTypeSelector(false)}
        onSelect={handleCalendarTypeSelect}
      />

      <DateTimeSelector
        isOpen={showDateTimeSelector}
        onClose={() => setShowDateTimeSelector(false)}
        onConfirm={handleDateTimeConfirm}
        calendarType={selectedCalendarType}
      />

      <AreaSelector
        isOpen={showAreaSelector}
        onClose={() => setShowAreaSelector(false)}
        onConfirm={handleAreaConfirm}
      />

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};
