import { Product, ProductLink, User } from '../models/index.js';
import sequelize from '../config/database.js';

async function createBaziProduct() {
  try {
    console.log('开始创建八字查询测试产品...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 查找管理员用户
    const admin = await User.findOne({ where: { role: 'admin' } });
    if (!admin) {
      console.error('未找到管理员用户，请先创建管理员账号');
      return;
    }

    // 创建八字查询产品
    const baziProduct = await Product.create({
      title: '专业八字命理查询服务',
      description: '由资深命理师提供的专业八字分析服务，包含命盘解读、五行分析、运势预测等内容。',
      price: 99.00,
      baseCommissionRate: 0.30, // 30%佣金
      status: 'active',
      type: 'service'
    });

    console.log('八字查询产品创建成功:', {
      id: baziProduct.id,
      title: baziProduct.title,
      price: baziProduct.price,
      type: baziProduct.type
    });

    // 为管理员创建产品推广链接
    const productLink = await ProductLink.create({
      productId: baziProduct.id,
      agentId: admin.id,
      code: `BAZI${Date.now()}`, // 生成唯一代码
      clickCount: 0,
      sales: 0,
      totalCommission: 0
    });

    console.log('产品推广链接创建成功:', {
      id: productLink.id,
      code: productLink.code,
      productId: productLink.productId,
      agentId: productLink.agentId
    });

    console.log('\n✅ 八字查询产品和推广链接创建完成！');
    console.log(`🔗 测试链接: http://localhost:3000/bazi/${productLink.code}`);
    
  } catch (error) {
    console.error('创建八字查询产品失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  createBaziProduct();
}

export default createBaziProduct;