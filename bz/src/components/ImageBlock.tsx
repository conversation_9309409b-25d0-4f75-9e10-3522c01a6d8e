import React from 'react';

export const ImageBlock: React.FC = () => {
  return (
    <div className="image-block">
      {/* 标题 */}
      <div className="title">
        八字精批示例
      </div>

      {/* 内容区域 */}
      <div className="content">
        {/* 图片网格 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr',
          gap: '12px',
          padding: '16px',
        }}>
          <img src="/assets/images/a1.png" alt="八字示例1" style={{ width: '100%', borderRadius: '8px' }} />
          <img src="/assets/images/a2.png" alt="八字示例2" style={{ width: '100%', borderRadius: '8px' }} />
          <img src="/assets/images/a3.png" alt="八字示例3" style={{ width: '100%', borderRadius: '8px' }} />
          <img src="/assets/images/a4.png" alt="八字示例4" style={{ width: '100%', borderRadius: '8px' }} />
        </div>

        {/* 说明文字 */}
        <div style={{ padding: '0 16px 16px' }}>
          <p style={{
            color: 'rgba(63,36,16,0.7)',
            fontSize: '14px',
            lineHeight: '1.6',
            margin: 0,
          }}>
            八字精批是根据您的出生年月日时，结合五行八卦理论，为您详细分析命理运势。
            包含性格特点、事业财运、婚姻感情、健康状况等多个方面的专业解读。
          </p>
        </div>
      </div>
    </div>
  );
};
