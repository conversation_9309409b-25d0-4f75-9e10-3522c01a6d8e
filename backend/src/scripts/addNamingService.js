import sequelize from '../config/database.js';
import { SystemConfig } from '../models/index.js';

async function addNamingServiceSupport() {
  try {
    console.log('开始添加起名业务支持...');
    
    // 1. 添加价格范围字段到产品表
    console.log('1. 检查并添加价格范围字段...');
    const queryInterface = sequelize.getQueryInterface();
    const tableDescription = await queryInterface.describeTable('products');
    
    if (!tableDescription.minPrice) {
      await queryInterface.addColumn('products', 'minPrice', {
        type: sequelize.Sequelize.DECIMAL(10, 2),
        allowNull: true,
        comment: 'Minimum price for variable pricing products like naming service'
      });
      console.log('✅ 添加 minPrice 字段成功');
    } else {
      console.log('⚠️ minPrice 字段已存在');
    }
    
    if (!tableDescription.maxPrice) {
      await queryInterface.addColumn('products', 'maxPrice', {
        type: sequelize.Sequelize.DECIMAL(10, 2),
        allowNull: true,
        comment: 'Maximum price for variable pricing products like naming service'
      });
      console.log('✅ 添加 maxPrice 字段成功');
    } else {
      console.log('⚠️ maxPrice 字段已存在');
    }

    // 2. 更新产品类型枚举
    console.log('2. 更新产品类型枚举...');
    try {
      // 先添加临时列
      await queryInterface.addColumn('products', 'type_new', {
        type: sequelize.Sequelize.ENUM('physical', 'service', 'naming'),
        allowNull: false,
        defaultValue: 'physical',
        comment: 'Product type: physical for goods, service for services like bazi query, naming for naming service'
      });

      // 复制数据
      await sequelize.query(`UPDATE products SET type_new = type`);

      // 删除旧列
      await queryInterface.removeColumn('products', 'type');

      // 重命名新列
      await queryInterface.renameColumn('products', 'type_new', 'type');
      
      console.log('✅ 产品类型枚举更新成功');
    } catch (error) {
      if (error.message.includes('Duplicate column name')) {
        console.log('⚠️ 产品类型枚举已更新');
      } else {
        throw error;
      }
    }

    // 3. 创建SystemConfigs表（如果不存在）
    console.log('3. 检查并创建SystemConfigs表...');
    try {
      await queryInterface.createTable('SystemConfigs', {
        key: {
          type: sequelize.Sequelize.STRING,
          primaryKey: true,
          allowNull: false
        },
        value: {
          type: sequelize.Sequelize.TEXT,
          allowNull: false
        },
        description: {
          type: sequelize.Sequelize.STRING,
          allowNull: true
        },
        group: {
          type: sequelize.Sequelize.STRING,
          allowNull: false,
          defaultValue: 'general'
        },
        createdAt: {
          type: sequelize.Sequelize.DATE,
          allowNull: false,
          defaultValue: sequelize.Sequelize.literal('CURRENT_TIMESTAMP')
        },
        updatedAt: {
          type: sequelize.Sequelize.DATE,
          allowNull: false,
          defaultValue: sequelize.Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
        }
      });
      console.log('✅ SystemConfigs表创建成功');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('⚠️ SystemConfigs表已存在');
      } else {
        throw error;
      }
    }

    // 4. 添加起名业务系统配置
    console.log('4. 添加起名业务系统配置...');
    const configs = [
      ['naming_service_min_price', '99', '起名业务最低价格（元）', 'naming'],
      ['naming_service_max_price', '299', '起名业务最高价格（元）', 'naming'],
      ['naming_service_commission_rate', '20', '起名业务佣金比例（%）', 'naming'],
      ['naming_service_enabled', 'true', '是否启用起名业务', 'naming']
    ];

    for (const [key, value, description, group] of configs) {
      try {
        await sequelize.query(
          `INSERT IGNORE INTO SystemConfigs (\`key\`, \`value\`, \`description\`, \`group\`, createdAt, updatedAt)
           VALUES (?, ?, ?, ?, NOW(), NOW())`,
          {
            replacements: [key, value, description, group],
            type: sequelize.QueryTypes.INSERT
          }
        );
        console.log(`✅ 添加配置: ${key}`);
      } catch (error) {
        console.log(`⚠️ 配置可能已存在: ${key}`);
      }
    }

    console.log('🎉 起名业务支持添加完成！');
    
  } catch (error) {
    console.error('❌ 添加起名业务支持失败:', error);
    throw error;
  }
}

// 运行脚本
addNamingServiceSupport()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
