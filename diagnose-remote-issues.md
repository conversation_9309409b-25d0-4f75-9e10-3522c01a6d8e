# 远程服务器后端服务问题诊断和解决方案

## 🔍 问题分析

根据部署脚本分析，commission-platform-api 状态为 `errored` 和 `disabled` 的可能原因：

### 1. **Node.js版本问题**
- 远程服务器的Node.js版本可能不支持ES模块
- 需要Node.js >= 14.x 才能支持 `"type": "module"`

### 2. **环境变量配置问题**
- 缺少 `.env.production` 文件
- 数据库连接配置错误
- 端口配置问题

### 3. **依赖安装问题**
- npm install 可能失败
- 某些依赖在生产环境中不兼容

### 4. **文件权限问题**
- start-production.js 没有执行权限
- 目录权限不正确

## 🛠️ 解决方案

### 方案1: 手动SSH连接修复

```bash
# 1. 连接到远程服务器
ssh root@************

# 2. 进入项目目录
cd /data/commission-platform8z/backend

# 3. 检查Node.js版本
node --version
npm --version

# 4. 检查PM2状态和日志
pm2 status
pm2 logs commission-platform-api --lines 20

# 5. 检查文件是否存在
ls -la start-production.js
ls -la src/index.js
ls -la .env.production

# 6. 重新安装依赖
npm install --production

# 7. 停止并删除现有服务
pm2 stop commission-platform-api
pm2 delete commission-platform-api

# 8. 清理端口
lsof -ti:3005 | xargs kill -9

# 9. 手动测试启动
NODE_ENV=production PORT=3005 node src/index.js

# 10. 如果手动启动成功，使用PM2启动
pm2 start start-production.js --name commission-platform-api

# 11. 保存PM2配置
pm2 save
pm2 startup
```

### 方案2: 修复部署脚本

如果Node.js版本过低，修改部署脚本使用CommonJS：

```javascript
// 创建 start-production-cjs.js
const { spawn } = require('child_process');
const path = require('path');

process.env.NODE_ENV = 'production';
process.env.PORT = '3005';

console.log('🚀 启动生产环境后端服务...');

const child = spawn('node', [path.join(__dirname, 'src/index.js')], {
  stdio: 'inherit',
  env: process.env
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});

child.on('exit', (code) => {
  console.log(`🔚 进程退出，代码: ${code}`);
  process.exit(code);
});
```

### 方案3: 创建环境变量文件

在远程服务器创建 `.env.production` 文件：

```bash
# 在 /data/commission-platform8z/backend/ 目录下创建
cat > .env.production << 'EOF'
NODE_ENV=production
PORT=3005

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=commission_platform

# JWT密钥
JWT_SECRET=your_jwt_secret_key

# 支付宝配置
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY=your_private_key
ALIPAY_PUBLIC_KEY=your_public_key

# 前端URL
FRONTEND_URL=https://ye.bzcy.xyz
EOF
```

### 方案4: 简化启动方式

如果ES模块有问题，直接使用简单的启动方式：

```bash
# 修改部署脚本中的启动命令
pm2 start src/index.js --name commission-platform-api --env production
```

## 🔧 立即执行的修复命令

```bash
# 连接到服务器并执行以下命令序列
ssh root@************ << 'EOF'
cd /data/commission-platform8z/backend

# 检查和修复
echo "🔍 检查Node.js版本:"
node --version

echo "🔍 当前PM2状态:"
pm2 status

echo "🔍 查看错误日志:"
pm2 logs commission-platform-api --lines 10 --nostream

# 停止并清理
pm2 stop commission-platform-api 2>/dev/null || true
pm2 delete commission-platform-api 2>/dev/null || true
lsof -ti:3005 | xargs kill -9 2>/dev/null || true

# 重新安装依赖
npm install --production

# 设置环境变量并启动
export NODE_ENV=production
export PORT=3005

# 尝试直接启动
pm2 start src/index.js --name commission-platform-api --env production

# 保存配置
pm2 save

echo "✅ 修复完成，检查状态:"
pm2 status
EOF
```

## 📊 验证修复结果

修复后，验证服务是否正常：

```bash
# 检查PM2状态
ssh root@************ 'pm2 status'

# 检查API是否响应
curl -f https://ye.bzcy.xyz/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}'

# 查看日志
ssh root@************ 'pm2 logs commission-platform-api --lines 10'
```

## 🚨 常见错误和解决方案

1. **SyntaxError: Cannot use import statement outside a module**
   - 解决：降级Node.js版本或改用CommonJS

2. **Error: Cannot find module**
   - 解决：重新运行 `npm install --production`

3. **EADDRINUSE: address already in use**
   - 解决：`lsof -ti:3005 | xargs kill -9`

4. **Database connection failed**
   - 解决：检查 `.env.production` 中的数据库配置

5. **Permission denied**
   - 解决：`chmod +x start-production.js`
