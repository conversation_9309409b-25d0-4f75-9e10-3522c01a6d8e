import type { ReactNode } from 'react';
import { cn } from '../../lib/utils';
import '../../styles/BaziReportEnhanced.css';

interface InfoItemProps {
  label: string;
  value?: ReactNode;
  className?: string;
  labelClassName?: string;
  valueClassName?: string;
  direction?: 'row' | 'col'; // 'row' is horizontal on sm+, 'col' is always vertical
}

export function InfoItem({ label, value, className, labelClassName, valueClassName, direction = 'row' }: InfoItemProps) {
  if (value === undefined || value === null || value === '') {
    return null;
  }
  
  const containerClasses = cn(
    "py-2 sm:py-2.5", // Increased padding for better vertical spacing
    direction === 'row' ? "flex flex-col sm:flex-row sm:items-start sm:justify-between" : "flex flex-col",
    className
  );

  const labelClasses = cn(
    "font-semibold text-sm bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent", // Enhanced label styling
    direction === 'row' ? "sm:w-1/3 mb-0.5 sm:mb-0 shrink-0" : "mb-1", // Ensure label doesn't shrink excessively, added mb-1 for col direction
    labelClassName
  );

  const valueClasses = cn(
    "text-foreground text-sm sm:text-base font-medium", // Added font-medium for better readability
     direction === 'row' ? "sm:w-2/3 sm:text-left" : "",
    "break-words", // Allow long words to break
    valueClassName
  );

  return (
    <div className={containerClasses}>
      <dt className={labelClasses}>{label}:</dt>
      <dd className={valueClasses}>{value}</dd>
    </div>
  );
}

export function ParagraphText({ text }: { text: string | undefined | null }) {
  if (!text) return null;
  // Handles both \r\n and <BR> style line breaks
  const paragraphs = text.split(/\r\n|<br\/?>|\n/gi).filter(p => p.trim() !== "");
  return (
    <>
      {paragraphs.map((p, index) => (
        <p key={index} className="mb-3 sm:mb-3.5 last:mb-0 text-sm sm:text-base leading-relaxed text-gray-700" style={{ whiteSpace: 'pre-line' }}>
          {/* Enhanced text styling with better spacing and color */}
          {p.trim()}
        </p>
      ))}
    </>
  );
}