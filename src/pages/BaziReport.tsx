import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
// 下面这些import路径请根据实际项目结构调整
import { fetchBaZiReport } from '../services/bazi-api';
import { PageHeader } from '../components/layout/page-header';
import { MingZhuInfoSection } from '../components/bazi/mingzhu-info-section';
import { ChengGuInfoSection } from '../components/bazi/chenggu-info-section';
import { XingMingShuLiSection } from '../components/bazi/xingming-shuli-section';
import { MingPanInfoSection } from '../components/bazi/mingpan-info-section';
import { WuXingXiShenSection } from '../components/bazi/wuxing-xishen-section';
import { RiGanLunMingSection } from '../components/bazi/rigan-lunming-section';
import { NamingServiceOffer } from '../components/bazi/naming-service-offer';
import { AlertCircle, Loader2, ScrollText, Star, Sparkles, Crown, Download, Share2, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '../components/ui/alert';
import { EnhancedLoading } from '../components/ui/enhanced-loading';
import type { BaZiReportData } from '../types/bazi';
import { config } from '../config';
import '../styles/BaziReportEnhanced.css';


// 辅助函数：从URL参数获取八字API参数
function getApiParamsFromSearch(search: string) {
  const params = new URLSearchParams(search);
  const requiredFields = [
    'xing', 'ming', 'sex', 'yearType', 'year', 'month', 'day', 'hour', 'minute'
  ];
  const missingFields = requiredFields.filter(field => !params.get(field));
  if (missingFields.length > 0) {
    return { params: null, error: `缺少必要参数: ${missingFields.join(', ')}` };
  }
  return {
    params: {
      xing: params.get('xing')!,
      ming: params.get('ming')!,
      sex: params.get('sex')!,
      yearType: params.get('yearType')!,
      year: params.get('year')!,
      month: params.get('month')!,
      day: params.get('day')!,
      hour: params.get('hour')!,
      minute: params.get('minute')!,
      address: params.get('address') || '',
    },
  };
}

const BaziReport: React.FC = () => {
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const orderId = searchParams.get('orderId');

  const [reportData, setReportData] = useState<BaZiReportData | null>(null);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [isGenerating, setIsGenerating] = useState(false);

  // 获取报告的函数
  const fetchReport = async (attempt = 1) => {
    try {
      setLoading(true);
      setIsGenerating(attempt > 1);

      console.log(`🔄 尝试 ${attempt}: 请求 ${config.apiUrl}/api/bazi/orders/${orderId}/report`);

      const response = await fetch(`${config.apiUrl}/api/bazi/orders/${orderId}/report`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        mode: 'cors', // 明确指定CORS模式
      });

      console.log(`📡 响应状态: ${response.status} ${response.statusText}`);
      console.log(`📡 响应头:`, Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ API响应数据 (尝试 ${attempt}):`, data);

      if (data.success && data.data.reportData?.data?.命主信息) {
        console.log('🎉 报告数据获取成功:', data.data.reportData.data);
        setReportData(data.data.reportData.data);
        setFetchError(null);
        setIsGenerating(false);
        return true;
      } else if (data.success && !data.data.reportData?.data?.命主信息) {
        // 报告正在生成中
        console.log('⏳ 报告正在生成中，准备重试...');
        if (attempt < 6) { // 最多重试5次
          setRetryCount(attempt);
          setTimeout(() => fetchReport(attempt + 1), 3000); // 3秒后重试
          return false;
        } else {
          setFetchError('报告生成超时，请稍后刷新页面重试');
          setIsGenerating(false);
          return false;
        }
      } else {
        console.log('❌ API返回错误:', data.message);
        setFetchError(data.message || '获取报告失败');
        setIsGenerating(false);
        return false;
      }
    } catch (e) {
      console.error(`💥 获取报告异常 (尝试 ${attempt}):`, e);
      console.error('错误详情:', {
        name: (e as any)?.name,
        message: (e as any)?.message,
        stack: (e as any)?.stack
      });

      if (attempt < 3) { // 网络错误最多重试2次
        console.log(`🔄 ${2000}ms后重试...`);
        setTimeout(() => fetchReport(attempt + 1), 2000);
        return false;
      } else {
        const errorMsg = e instanceof Error ? `${e.name}: ${e.message}` : '获取报告时发生未知错误';
        setFetchError(errorMsg);
        setReportData(null);
        setIsGenerating(false);
        return false;
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (orderId) {
      // 通过订单ID获取八字报告，支持自动重试
      fetchReport();
    } else {
      // 使用原有的URL参数方式
      const { params: apiParams, error: paramsError } = getApiParamsFromSearch(location.search);

      if (paramsError) {
        setFetchError(paramsError);
        return;
      }

      if (!apiParams) {
        setFetchError('缺少必要的参数（订单ID或八字信息）');
        return;
      }

      setLoading(true);
      fetchBaZiReport(apiParams)
        .then(data => {
          setReportData(data);
          setFetchError(null);
        })
        .catch(e => {
          setFetchError(e instanceof Error ? e.message : '获取报告时发生未知错误');
          setReportData(null);
        })
        .finally(() => setLoading(false));
    }
    // eslint-disable-next-line
  }, [location.search, orderId]);

  const pageTitle = reportData && reportData.命主信息
    ? `${reportData.命主信息.姓名} 命盘报告`
    : '命盘报告';
  const pageDescription = reportData && reportData.命主信息
    ? `公历生日: ${reportData.命主信息.公历生日}`
    : '正在加载命盘信息...';

  if (fetchError) {
    return (
      <div className="min-h-screen bg-background text-foreground flex flex-col items-center justify-center p-4">
        <PageHeader
          title="获取命盘报告出错"
          icon={AlertCircle}
        />
        <Alert variant="destructive" className="max-w-2xl w-full">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>错误</AlertTitle>
          <AlertDescription>
            无法加载您的八字命盘报告。详情：{fetchError}
            <br />
            请检查输入信息或稍后再试。
          </AlertDescription>
        </Alert>
      </div>
    );
  }



  // 加载状态 - 产品级优化
  if (loading || isGenerating) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 text-foreground">
        {/* 顶部装饰 */}
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-amber-400 via-orange-400 to-yellow-400"></div>

        <div className="flex flex-col items-center justify-center min-h-screen p-4 relative">
          {/* 背景装饰 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-32 h-32 bg-amber-200/20 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute bottom-20 right-10 w-40 h-40 bg-orange-200/20 rounded-full blur-xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-yellow-200/10 rounded-full blur-2xl animate-pulse delay-500"></div>
          </div>

          {/* 主要内容 */}
          <div className="relative z-10 text-center max-w-2xl mx-auto">
            {/* 图标和标题 */}
            <div className="mb-8">
              <div className="relative inline-block">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-400 rounded-full blur-lg opacity-30 animate-pulse"></div>
                <div className="relative bg-white rounded-full p-6 shadow-xl">
                  <Sparkles className="h-12 w-12 text-amber-500 animate-spin" style={{ animationDuration: '3s' }} />
                </div>
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mt-6 mb-4">
                正在生成您的专属八字命盘
              </h1>
              <p className="text-lg text-gray-600 mb-2">
                {isGenerating
                  ? `正在为您精心计算命理信息... ${retryCount > 0 ? `(第${retryCount}次尝试)` : ''}`
                  : '请稍候，我们正在为您准备专属的八字报告'
                }
              </p>
            </div>

            {/* 进度指示器 */}
            <div className="mb-8">
              <div className="flex items-center justify-center space-x-3 text-lg font-medium text-gray-700">
                <Loader2 className="h-6 w-6 animate-spin text-amber-500" />
                <span>
                  {isGenerating
                    ? '系统正在为您计算八字命盘...'
                    : '正在努力为您解读命运玄机...'
                  }
                </span>
              </div>

              {/* 进度条 */}
              <div className="mt-4 w-full max-w-md mx-auto">
                <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div className="bg-gradient-to-r from-amber-400 to-orange-400 h-full rounded-full animate-pulse"
                       style={{ width: isGenerating ? '75%' : '45%', transition: 'width 2s ease-in-out' }}></div>
                </div>
              </div>
            </div>

            {/* 详细步骤 */}
            {isGenerating && (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-100">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center justify-center">
                  <Crown className="h-5 w-5 text-amber-500 mr-2" />
                  命理分析进行中
                </h3>
                <div className="space-y-3 text-sm text-gray-600">
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>✓ 正在分析您的生辰八字</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
                    <span>⟳ 正在计算五行旺衰</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                    <span>○ 正在生成详细报告</span>
                  </div>
                </div>
                <div className="mt-4 text-xs text-gray-500">
                  预计还需要 {Math.max(0, 30 - retryCount * 5)} 秒
                </div>
              </div>
            )}

            {/* 底部提示 */}
            <div className="mt-8 text-sm text-gray-500">
              <p>请保持页面打开，我们正在为您精心准备专业的命理分析</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 调试信息
  console.log('当前状态:', {
    loading,
    reportData: !!reportData,
    hasInfo: !!(reportData?.命主信息),
    orderId,
    fetchError,
    dataKeys: reportData ? Object.keys(reportData) : []
  });

  if (!reportData || !reportData.命主信息) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex flex-col items-center justify-center p-4">
        <div className="text-center max-w-2xl mx-auto">
          <div className="mb-8">
            <div className="relative inline-block">
              <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-400 rounded-full blur-lg opacity-30 animate-pulse"></div>
              <div className="relative bg-white rounded-full p-6 shadow-xl">
                <AlertCircle className="h-12 w-12 text-red-500" />
              </div>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mt-6 mb-4">
              报告暂未生成
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              {fetchError || "您的八字命盘报告正在生成中，请稍后再试"}
            </p>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-red-100 mb-8">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">可能的原因：</h3>
            <div className="space-y-2 text-sm text-gray-600 text-left">
              <p>• 报告正在生成中，通常需要1-2分钟</p>
              <p>• 网络连接不稳定</p>
              <p>• 订单信息有误</p>
            </div>
          </div>

          <div className="flex flex-wrap items-center justify-center gap-3">
            <button
              onClick={() => window.location.reload()}
              className="enhanced-button flex items-center space-x-2 bg-amber-500 hover:bg-amber-600 text-white px-6 py-3 rounded-lg shadow-sm transition-colors hover-lift"
            >
              <RefreshCw className="h-4 w-4" />
              <span>重新加载</span>
            </button>
            <button
              onClick={() => window.history.back()}
              className="enhanced-button flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg border border-gray-200 shadow-sm transition-colors hover-lift"
            >
              <span>返回上页</span>
            </button>
          </div>

          {fetchError && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">错误详情: {fetchError}</p>
            </div>
          )}
        </div>
      </div>
    );
  }

  if (fetchError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex flex-col items-center justify-center p-4">
        <div className="text-center max-w-2xl mx-auto">
          <div className="mb-8">
            <div className="relative inline-block">
              <div className="absolute inset-0 bg-gradient-to-r from-red-400 to-orange-400 rounded-full blur-lg opacity-30 animate-pulse"></div>
              <div className="relative bg-white rounded-full p-6 shadow-xl">
                <AlertCircle className="h-12 w-12 text-red-500" />
              </div>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mt-6 mb-4">
              获取报告失败
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              {fetchError}
            </p>
          </div>

          <div className="flex flex-wrap items-center justify-center gap-3">
            <button
              onClick={() => window.location.reload()}
              className="enhanced-button flex items-center space-x-2 bg-amber-500 hover:bg-amber-600 text-white px-6 py-3 rounded-lg shadow-sm transition-colors hover-lift"
            >
              <RefreshCw className="h-4 w-4" />
              <span>重试</span>
            </button>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className="bazi-report min-h-screen bg-gradient-to-br from-amber-50/30 via-white to-orange-50/30">
      {/* 顶部装饰条 */}
      <div className="h-1 bg-gradient-to-r from-amber-400 via-orange-400 to-yellow-400"></div>

      {/* 优化的页面头部 */}
      <header className="relative bg-gradient-to-r from-amber-50 via-orange-50 to-yellow-50 border-b border-amber-200/50">
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>

        <div className="relative container mx-auto mobile-container px-4 py-8 md:py-12">
          <div className="text-center">
            {/* 主标题区域 */}
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-amber-400 to-orange-400 rounded-full shadow-lg mb-4">
                <ScrollText className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-800 mb-3">
                {pageTitle}
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-4">
                {pageDescription}
              </p>

              {/* 报告状态标签 */}
              <div className="flex items-center justify-center space-x-4 text-sm">
                <div className="flex items-center space-x-2 bg-green-100 text-green-700 px-3 py-1 rounded-full">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>报告已生成</span>
                </div>
                <div className="flex items-center space-x-2 bg-amber-100 text-amber-700 px-3 py-1 rounded-full">
                  <Star className="h-3 w-3" />
                  <span>专业解读</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap items-center justify-center gap-3">
              <button
                onClick={() => window.print()}
                className="enhanced-button flex items-center space-x-2 bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg border border-gray-200 shadow-sm transition-colors hover-lift"
              >
                <Download className="h-4 w-4" />
                <span>保存报告</span>
              </button>
              <button
                onClick={() => {
                  if (navigator.share) {
                    navigator.share({
                      title: pageTitle,
                      text: '我的专属八字命盘报告',
                      url: window.location.href
                    });
                  } else {
                    navigator.clipboard.writeText(window.location.href);
                    alert('链接已复制到剪贴板');
                  }
                }}
                className="enhanced-button glow-effect flex items-center space-x-2 bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-lg shadow-sm transition-colors hover-lift"
              >
                <Share2 className="h-4 w-4" />
                <span>分享报告</span>
              </button>
              <button
                onClick={() => window.location.reload()}
                className="enhanced-button flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg border border-gray-200 shadow-sm transition-colors hover-lift"
              >
                <RefreshCw className="h-4 w-4" />
                <span>刷新</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="container mx-auto px-3 sm:px-4 md:px-6 py-8 md:py-12">
        {/* 内容提示 */}
        <div className="mb-8 bg-gradient-to-r from-amber-100 to-orange-100 border border-amber-200 rounded-xl p-4 md:p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                <Sparkles className="h-4 w-4 text-white" />
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-amber-800 mb-2">您的专属八字命盘报告</h3>
              <p className="text-amber-700 text-sm leading-relaxed">
                以下是根据您的生辰八字精心计算的详细命理分析。每个部分都包含了深度的命理解读，
                帮助您更好地了解自己的性格特点、运势走向和人生指导。
              </p>
            </div>
          </div>
        </div>

        {/* 报告内容 */}
        <div className="space-y-8 md:space-y-12">
          {reportData.命主信息 && (
            <div className="content-section">
              <MingZhuInfoSection data={reportData.命主信息 as any} />
            </div>
          )}
          {reportData.命主八字称骨信息 && (
            <div className="content-section">
              <ChengGuInfoSection data={reportData.命主八字称骨信息 as any} />
            </div>
          )}
          {reportData.命主姓名数理信息 && (
            <div className="content-section">
              <XingMingShuLiSection data={reportData.命主姓名数理信息 as any} />
            </div>
          )}
          {reportData.命主八字命盘信息 && (
            <div className="content-section">
              <MingPanInfoSection data={reportData.命主八字命盘信息 as any} />
            </div>
          )}
          {reportData.命主五行和喜神信息 && (
            <div className="content-section">
              <WuXingXiShenSection data={reportData.命主五行和喜神信息 as any} />
            </div>
          )}
          {reportData.命主日干论命信息 && (
            <div className="content-section">
              <RiGanLunMingSection data={reportData.命主日干论命信息 as any} />
            </div>
          )}

          {/* 起名服务推荐 */}
          <div className="content-section mt-12">
            <NamingServiceOffer
              customerName={reportData.命主信息?.姓名}
              nameScore={undefined}
              baziReportId={orderId || undefined}
            />
          </div>
        </div>
      </main>

      {/* 优化的页脚 */}
      <footer className="bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2 text-gray-600">
              <Crown className="h-5 w-5 text-amber-500" />
              <span className="font-medium">专业命理分析服务</span>
            </div>
            <p className="text-sm text-gray-500 max-w-2xl mx-auto leading-relaxed">
              本报告基于传统八字命理学说生成，仅供参考和娱乐。命运掌握在自己手中，
              积极向上的心态和不懈的努力才是改变人生的关键。
            </p>
            <div className="flex items-center justify-center space-x-6 text-xs text-gray-400">
              <span>&copy; {new Date().getFullYear()} 八字命盘</span>
              <span>•</span>
              <span>All rights reserved</span>
              <span>•</span>
              <span>仅供参考</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BaziReport; 