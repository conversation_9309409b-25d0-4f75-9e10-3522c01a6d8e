import React, { useState, useEffect } from 'react';
import { SimpleModal } from './ui/SimpleModal';

interface DateTimeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  }) => void;
  calendarType: 'solar' | 'lunar';
}

export const DateTimeSelector: React.FC<DateTimeSelectorProps> = ({
  isOpen,
  onClose,
  onConfirm,
  calendarType,
}) => {
  const [year, setYear] = useState(new Date().getFullYear());
  const [month, setMonth] = useState(new Date().getMonth() + 1);
  const [day, setDay] = useState(new Date().getDate());
  const [hour, setHour] = useState(new Date().getHours());
  const [minute, setMinute] = useState(new Date().getMinutes());

  // 生成年份选项（1900-当前年份）
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: currentYear - 1900 + 1 }, (_, i) => 1900 + i).reverse();
  
  // 生成月份选项
  const months = Array.from({ length: 12 }, (_, i) => i + 1);
  
  // 生成日期选项
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month, 0).getDate();
  };
  
  const days = Array.from({ length: getDaysInMonth(year, month) }, (_, i) => i + 1);
  
  // 生成小时选项
  const hours = Array.from({ length: 24 }, (_, i) => i);
  
  // 生成分钟选项
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  // 当年份或月份改变时，调整日期
  useEffect(() => {
    const maxDay = getDaysInMonth(year, month);
    if (day > maxDay) {
      setDay(maxDay);
    }
  }, [year, month, day]);

  const handleConfirm = () => {
    onConfirm({
      year,
      month,
      day,
      hour,
      minute,
      calendarType,
    });
    onClose();
  };

  return (
    <SimpleModal isOpen={isOpen} onClose={onClose} title={`选择${calendarType === 'solar' ? '公历' : '农历'}日期时间`}>
      <div className="datetime-selector">
        <div className="datetime-selectors">
          <div className="selector-group">
            <label>年份</label>
            <select value={year} onChange={(e) => setYear(Number(e.target.value))}>
              {years.map(y => (
                <option key={y} value={y}>{y}年</option>
              ))}
            </select>
          </div>

          <div className="selector-group">
            <label>月份</label>
            <select value={month} onChange={(e) => setMonth(Number(e.target.value))}>
              {months.map(m => (
                <option key={m} value={m}>{m}月</option>
              ))}
            </select>
          </div>

          <div className="selector-group">
            <label>日期</label>
            <select value={day} onChange={(e) => setDay(Number(e.target.value))}>
              {days.map(d => (
                <option key={d} value={d}>{d}日</option>
              ))}
            </select>
          </div>

          <div className="selector-group">
            <label>小时</label>
            <select value={hour} onChange={(e) => setHour(Number(e.target.value))}>
              {hours.map(h => (
                <option key={h} value={h}>{h.toString().padStart(2, '0')}时</option>
              ))}
            </select>
          </div>

          <div className="selector-group">
            <label>分钟</label>
            <select value={minute} onChange={(e) => setMinute(Number(e.target.value))}>
              {minutes.map(m => (
                <option key={m} value={m}>{m.toString().padStart(2, '0')}分</option>
              ))}
            </select>
          </div>
        </div>

        <div className="datetime-actions">
          <button className="cancel-button" onClick={onClose}>取消</button>
          <button className="confirm-button" onClick={handleConfirm}>确认</button>
        </div>
      </div>
    </SimpleModal>
  );
};
