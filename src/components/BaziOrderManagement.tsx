import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import { 
  Table, Button, Modal, Tag, Space, message, Tooltip, Typography, Descriptions, Card
} from 'antd';
import { 
  EyeOutlined, FileTextOutlined, ReloadOutlined, UserOutlined, CalendarOutlined
} from '@ant-design/icons';
import BaziReport from './BaziReport';

const { Text, Title } = Typography;

interface BaziOrder {
  id: number;
  orderId: string;
  name: string;
  gender: 'male' | 'female';
  calendarType: 'gregorian' | 'lunar';
  birthYear: string;
  birthMonth: string;
  birthDay: string;
  birthHour: string;
  birthMinute: string;
  birthProvince: string;
  birthCity: string;
  reportData: any;
  reportStatus: 'pending' | 'generated' | 'failed';
  errorMessage?: string;
  createdAt: string;
  updatedAt: string;
  order?: {
    id: string;
    amount: number;
    status: string;
    customerPhone: string;
    createdAt: string;
  };
}

interface BaziOrderManagementProps {
  visible: boolean;
  onClose: () => void;
  orderId?: string;
}

const BaziOrderManagement: React.FC<BaziOrderManagementProps> = ({ 
  visible, 
  onClose, 
  orderId 
}) => {
  const { token } = useAuth();
  const [baziOrders, setBaziOrders] = useState<BaziOrder[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<BaziOrder | null>(null);
  const [reportVisible, setReportVisible] = useState(false);

  useEffect(() => {
    if (visible && token) {
      fetchBaziOrders();
    }
  }, [visible, token, orderId]);

  const fetchBaziOrders = async () => {
    try {
      setLoading(true);
      let url = `${config.apiUrl}/api/bazi/orders`;
      
      if (orderId) {
        url += `?orderId=${orderId}`;
      }

      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('获取八字订单失败');
      }

      const data = await response.json();
      setBaziOrders(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('获取八字订单失败:', error);
      message.error('获取八字订单失败');
    } finally {
      setLoading(false);
    }
  };

  const generateReport = async (baziOrderId: number) => {
    try {
      const baziOrder = baziOrders.find(order => order.id === baziOrderId);
      if (!baziOrder) return;

      message.loading('正在生成八字报告...', 0);

      const response = await fetch(`${config.apiUrl}/api/bazi/orders/${baziOrder.orderId}/generate-report`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      message.destroy();

      if (!response.ok) {
        throw new Error('生成报告失败');
      }

      message.success('报告生成成功');
      fetchBaziOrders(); // 刷新数据
    } catch (error) {
      message.destroy();
      console.error('生成报告失败:', error);
      message.error('生成报告失败');
    }
  };

  const viewReport = async (baziOrder: BaziOrder) => {
    if (baziOrder.reportStatus !== 'generated' || !baziOrder.reportData) {
      message.warning('报告尚未生成或生成失败');
      return;
    }

    setSelectedOrder(baziOrder);
    setReportVisible(true);
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'pending':
        return <Tag color="orange">待生成</Tag>;
      case 'generated':
        return <Tag color="green">已生成</Tag>;
      case 'failed':
        return <Tag color="red">生成失败</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  const getGenderText = (gender: string) => {
    return gender === 'male' ? '男' : '女';
  };

  const getCalendarText = (type: string) => {
    return type === 'gregorian' ? '公历' : '农历';
  };

  const columns = [
    {
      title: '订单ID',
      dataIndex: 'orderId',
      key: 'orderId',
      width: 120,
      render: (orderId: string) => (
        <Text code style={{ fontSize: '12px' }}>{orderId}</Text>
      )
    },
    {
      title: '客户信息',
      key: 'customer',
      width: 150,
      render: (record: BaziOrder) => (
        <div>
          <div><UserOutlined /> {record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {getGenderText(record.gender)} | {getCalendarText(record.calendarType)}
          </div>
        </div>
      )
    },
    {
      title: '生辰信息',
      key: 'birth',
      width: 200,
      render: (record: BaziOrder) => (
        <div style={{ fontSize: '12px' }}>
          <div><CalendarOutlined /> {record.birthYear}-{record.birthMonth}-{record.birthDay}</div>
          <div>{record.birthHour}:{record.birthMinute}</div>
          <div>{record.birthProvince} {record.birthCity}</div>
        </div>
      )
    },
    {
      title: '报告状态',
      dataIndex: 'reportStatus',
      key: 'reportStatus',
      width: 100,
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      render: (date: string) => (
        <Text style={{ fontSize: '12px' }}>
          {new Date(date).toLocaleString()}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: BaziOrder) => (
        <Space>
          {record.reportStatus === 'pending' && (
            <Tooltip title="生成报告">
              <Button
                type="primary"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => generateReport(record.id)}
              >
                生成
              </Button>
            </Tooltip>
          )}
          {record.reportStatus === 'generated' && (
            <Tooltip title="查看报告">
              <Button
                type="default"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => viewReport(record)}
              >
                查看
              </Button>
            </Tooltip>
          )}
          {record.reportStatus === 'failed' && (
            <Tooltip title="重新生成">
              <Button
                type="default"
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => generateReport(record.id)}
              >
                重试
              </Button>
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <>
      <Modal
        title="八字查询订单管理"
        open={visible}
        onCancel={onClose}
        width={1200}
        footer={[
          <Button key="refresh" icon={<ReloadOutlined />} onClick={fetchBaziOrders}>
            刷新
          </Button>,
          <Button key="close" onClick={onClose}>
            关闭
          </Button>
        ]}
      >
        <Table
          columns={columns}
          dataSource={baziOrders}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          scroll={{ x: 800 }}
        />
      </Modal>

      {/* 八字报告查看弹窗 */}
      <Modal
        title="八字命理报告"
        open={reportVisible}
        onCancel={() => setReportVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setReportVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {selectedOrder && selectedOrder.reportData && (
          <div>
            <Card size="small" style={{ marginBottom: 16 }}>
              <Descriptions column={2} size="small">
                <Descriptions.Item label="姓名">{selectedOrder.name}</Descriptions.Item>
                <Descriptions.Item label="性别">{getGenderText(selectedOrder.gender)}</Descriptions.Item>
                <Descriptions.Item label="历法">{getCalendarText(selectedOrder.calendarType)}</Descriptions.Item>
                <Descriptions.Item label="生辰">
                  {selectedOrder.birthYear}-{selectedOrder.birthMonth}-{selectedOrder.birthDay} {selectedOrder.birthHour}:{selectedOrder.birthMinute}
                </Descriptions.Item>
                <Descriptions.Item label="出生地" span={2}>
                  {selectedOrder.birthProvince} {selectedOrder.birthCity}
                </Descriptions.Item>
              </Descriptions>
            </Card>
            <BaziReport data={selectedOrder.reportData} />
          </div>
        )}
      </Modal>
    </>
  );
};

export default BaziOrderManagement;