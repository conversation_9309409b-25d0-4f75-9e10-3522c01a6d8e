import React, { useState } from 'react';
import { Field } from './ui/Field';
import { Radio, RadioGroup } from './ui/Radio';
import { Tag } from './ui/Tag';
import { CalendarTypeSelector } from './CalendarTypeSelector';
import { DateTimeSelector } from './DateTimeSelector';
import { AreaSelector } from './AreaSelector';
import { useAppStore } from '@/store';
import { formatDate } from '@/utils';
import { yearTypeOptions, relationOptions } from '@/config';

// 姓名输入组件
export const NameField: React.FC = () => {
  const { formData, setFormData } = useAppStore();

  return (
    <Field label="您的名字">
      <input
        type="text"
        value={formData.name}
        onChange={(e) => setFormData({ name: e.target.value })}
        placeholder="请输入姓名(必须汉字)"
        style={{
          border: 'none',
          outline: 'none',
          background: 'transparent',
          fontSize: '15px',
          color: '#333',
          textAlign: 'right',
          width: '100%',
        }}
      />
    </Field>
  );
};

// 性别选择组件
export const GenderField: React.FC = () => {
  const { formData, setFormData } = useAppStore();

  return (
    <Field label="您的性别">
      <RadioGroup
        value={formData.sex}
        onChange={(value) => setFormData({ sex: value as '1' | '2' })}
      >
        <Radio name="sex" value="1">男</Radio>
        <Radio name="sex" value="2">女</Radio>
      </RadioGroup>
    </Field>
  );
};

// 生日选择组件
export const BirthdayField: React.FC = () => {
  const { formData, setFormData } = useAppStore();
  const [showCalendarTypeSelector, setShowCalendarTypeSelector] = useState(false);
  const [showDateTimeSelector, setShowDateTimeSelector] = useState(false);
  const [calendarType, setCalendarType] = useState<'solar' | 'lunar'>('solar');

  const handleCalendarTypeSelect = (type: 'solar' | 'lunar') => {
    setCalendarType(type);
    setShowDateTimeSelector(true);
  };

  const handleDateTimeConfirm = (data: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  }) => {
    const dateObj = {
      year: data.year,
      month: data.month,
      day: data.day,
      hour: data.hour,
      minute: data.minute,
      yearType: data.calendarType === 'solar' ? 1 : 2,
    };

    const birthday = `${data.year}-${data.month.toString().padStart(2, '0')}-${data.day.toString().padStart(2, '0')} ${data.hour.toString().padStart(2, '0')}:${data.minute.toString().padStart(2, '0')}`;

    setFormData({
      date: dateObj,
      birthday,
      yearType: (data.calendarType === 'solar' ? '1' : '2') as '1' | '2',
    });
  };

  return (
    <>
      <Field
        label="出生日期"
        isLink
        onClick={() => setShowCalendarTypeSelector(true)}
      >
        <span className="field-input">
          {formData.birthday || '点击选择时间'}
        </span>
      </Field>

      <CalendarTypeSelector
        isOpen={showCalendarTypeSelector}
        onClose={() => setShowCalendarTypeSelector(false)}
        onSelect={handleCalendarTypeSelect}
      />

      <DateTimeSelector
        isOpen={showDateTimeSelector}
        onClose={() => setShowDateTimeSelector(false)}
        onConfirm={handleDateTimeConfirm}
        calendarType={calendarType}
      />
    </>
  );
};

// 地区选择组件
export const AddressField: React.FC = () => {
  const { formData, setFormData } = useAppStore();
  const [showAreaSelector, setShowAreaSelector] = useState(false);

  const handleAreaConfirm = (data: { province: string; city: string; district?: string }) => {
    const address = data.district
      ? `${data.province}${data.city}${data.district}`
      : `${data.province}${data.city}`;
    setFormData({ address });
  };

  return (
    <>
      <Field
        label="出生地区"
        isLink
        onClick={() => setShowAreaSelector(true)}
      >
        <span className="field-input">
          {formData.address || '点击选择省市区'}
        </span>
      </Field>

      <AreaSelector
        isOpen={showAreaSelector}
        onClose={() => setShowAreaSelector(false)}
        onConfirm={handleAreaConfirm}
      />
    </>
  );
};

// 关系选择组件
export const RelationField: React.FC = () => {
  const { formData, setFormData, link } = useAppStore();
  const [showFullRelations, setShowFullRelations] = useState(false);

  // 根据link配置决定是否显示关系选择
  const shouldShowRelation = link?.config?.openRelation;

  if (!shouldShowRelation) {
    return null;
  }

  const handleRelationSelect = (relation: string) => {
    if (relation === '更多') {
      setShowFullRelations(true);
    } else {
      setFormData({ relation });
      setShowFullRelations(false);
    }
  };

  return (
    <div className="field-container">
      <div className="flex items-center justify-between w-full">
        <div className="field-label">选择关系</div>
        <div className="flex-1 flex items-center justify-end">
          <div className="flex flex-wrap gap-2">
            {!showFullRelations ? (
              // 显示简化选项
              relationOptions.fragment.map((item) => (
                <Tag
                  key={item}
                  selected={formData.relation === item}
                  onClick={() => handleRelationSelect(item)}
                  color={item === '更多' ? '#ffa648' : '#b87c3d'}
                >
                  {item}
                </Tag>
              ))
            ) : (
              // 显示完整选项
              relationOptions.full.map((item) => (
                <Tag
                  key={item}
                  selected={formData.relation === item}
                  onClick={() => handleRelationSelect(item)}
                  className="mb-2"
                >
                  {item}
                </Tag>
              ))
            )}
          </div>
        </div>
        <button
          className="ml-2 p-1"
          onClick={() => setShowFullRelations(!showFullRelations)}
        >
          <svg
            className={`w-4 h-4 text-gray-400 transition-transform ${
              showFullRelations ? 'rotate-90' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>
  );
};
