#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m'

# 定义变量
SERVER_USER="root"
SERVER_IP="************"
APP_NAME="commission-platform"
REMOTE_FRONTEND_DIR="/data/$APP_NAME"
REMOTE_BACKEND_DIR="/root/$APP_NAME-api"

# 输入服务器密码
echo -e "${GREEN}请输入服务器密码: ${NC}"
read -s SERVER_PASSWORD
echo

# 远程执行命令函数
remote_exec() {
    echo -e "${GREEN}执行: $1${NC}"
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "$1"
}

# 远程执行sudo命令函数
remote_sudo() {
    echo -e "${GREEN}执行: sudo $1${NC}"
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "echo '$SERVER_PASSWORD' | sudo -S $1"
}

echo -e "${GREEN}开始修复权限问题...${NC}"

# 1. 修复前端目录权限
echo -e "${YELLOW}修复前端目录权限...${NC}"
remote_sudo "chown -R www-data:www-data $REMOTE_FRONTEND_DIR"
remote_sudo "chmod -R 755 $REMOTE_FRONTEND_DIR"
remote_sudo "find $REMOTE_FRONTEND_DIR -type d -exec chmod 755 {} \\;"
remote_sudo "find $REMOTE_FRONTEND_DIR -type f -exec chmod 644 {} \\;"

# 2. 修复后端目录权限
echo -e "${YELLOW}修复后端目录权限...${NC}"
remote_sudo "chown -R $SERVER_USER:$SERVER_USER $REMOTE_BACKEND_DIR"
remote_sudo "chmod -R 755 $REMOTE_BACKEND_DIR"

# 3. 检查并创建日志目录
echo -e "${YELLOW}创建和修复日志目录权限...${NC}"
remote_sudo "mkdir -p /var/log/nginx"
remote_sudo "chown -R www-data:adm /var/log/nginx"
remote_sudo "chmod -R 755 /var/log/nginx"

# 4. 重启服务
echo -e "${YELLOW}重启服务...${NC}"
remote_sudo "systemctl restart nginx"
remote_exec "pm2 restart $APP_NAME-api"

# 5. 检查服务状态
echo -e "${YELLOW}检查服务状态...${NC}"
remote_exec "systemctl status nginx | grep active"
remote_exec "pm2 status"

echo -e "${GREEN}修复完成！${NC}"
