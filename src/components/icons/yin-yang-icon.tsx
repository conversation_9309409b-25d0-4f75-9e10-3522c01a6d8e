import React from 'react';

interface YinYangIconProps {
  className?: string;
  size?: number;
}

export function YinYangIcon({ className = "", size = 24 }: YinYangIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/>
      <path
        d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"
        fill="currentColor"
      />
      <circle cx="12" cy="8" r="3" fill="currentColor"/>
      <circle cx="12" cy="16" r="3" fill="currentColor"/>
      <circle cx="12" cy="8" r="1.5" fill="white"/>
      <circle cx="12" cy="16" r="1.5" fill="black"/>
    </svg>
  );
} 