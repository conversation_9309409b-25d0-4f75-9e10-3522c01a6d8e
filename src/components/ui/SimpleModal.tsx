import React from 'react';

interface SimpleModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
}

export const SimpleModal: React.FC<SimpleModalProps> = ({ isOpen, onClose, children, title }) => {
  if (!isOpen) return null;

  return (
    <div className="simple-modal-overlay" onClick={onClose}>
      <div className="simple-modal-content" onClick={(e) => e.stopPropagation()}>
        {title && (
          <div className="simple-modal-header">
            <h3>{title}</h3>
            <button className="simple-modal-close" onClick={onClose}>×</button>
          </div>
        )}
        <div className="simple-modal-body">
          {children}
        </div>
      </div>
    </div>
  );
};
