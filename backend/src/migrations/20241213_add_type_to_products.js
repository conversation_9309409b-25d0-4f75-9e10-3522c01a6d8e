export const up = async (queryInterface, Sequelize) => {
  // 检查type字段是否已存在
  const tableDescription = await queryInterface.describeTable('products');
  
  if (!tableDescription.type) {
    await queryInterface.addColumn('products', 'type', {
      type: Sequelize.ENUM('physical', 'service'),
      defaultValue: 'physical',
      allowNull: false,
      comment: 'Product type: physical for goods, service for services like bazi query'
    });
  }
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.removeColumn('products', 'type');
}; 