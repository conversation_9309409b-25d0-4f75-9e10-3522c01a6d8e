import { Order, BaziOrder } from './src/models/index.js';
import sequelize from './src/config/database.js';
import baziService from './src/services/baziService.js';

async function regenerateWithChenggu() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    const orderId = '250711145529834645';
    console.log(`为订单 ${orderId} 重新生成包含称骨信息的八字报告`);
    
    // 查找订单
    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder'
      }]
    });
    
    if (!order || !order.baziOrder) {
      console.log('❌ 订单或八字订单不存在');
      return;
    }
    
    const baziOrder = order.baziOrder;
    
    console.log('📋 订单信息:');
    console.log('  姓名:', baziOrder.name);
    console.log('  出生年月日时:', `${baziOrder.birthYear}-${baziOrder.birthMonth}-${baziOrder.birthDay} ${baziOrder.birthHour}:${baziOrder.birthMinute}`);
    
    // 重置报告状态
    await baziOrder.update({ 
      reportStatus: 'pending',
      errorMessage: null 
    });
    
    console.log('\n🚀 开始重新生成八字报告（包含称骨信息）...');
    
    try {
      // 调用八字服务生成报告
      const reportResult = await baziService.generateBaziReport({
        name: baziOrder.name,
        gender: baziOrder.gender,
        calendarType: baziOrder.calendarType,
        birthYear: baziOrder.birthYear,
        birthMonth: baziOrder.birthMonth,
        birthDay: baziOrder.birthDay,
        birthHour: baziOrder.birthHour,
        birthMinute: baziOrder.birthMinute,
        birthProvince: baziOrder.birthProvince,
        birthCity: baziOrder.birthCity
      });

      if (reportResult.success) {
        // 保存报告数据
        await baziOrder.update({
          reportData: reportResult.data,
          reportStatus: 'generated',
          apiResponse: reportResult.data
        });

        console.log('✅ 报告重新生成成功');
        
        // 检查称骨信息
        const chengguInfo = reportResult.data['命主八字称骨信息'];
        console.log('\n🔮 称骨信息验证:');
        console.log('  称骨重量:', chengguInfo.称骨重量);
        console.log('  命数:', chengguInfo.称骨重量_命数);
        console.log('  歌诀:', chengguInfo.称骨重量_歌诀 ? '已生成' : '未生成');
        console.log('  歌诀释义:', chengguInfo.称骨重量_歌诀释义 ? '已生成' : '未生成');
        console.log('  命运详解:', chengguInfo.称骨重量_命运详解 ? '已生成' : '未生成');
        
        if (chengguInfo.详细计算) {
          console.log('\n📊 详细计算:');
          console.log('  年骨重:', chengguInfo.详细计算.年骨重);
          console.log('  月骨重:', chengguInfo.详细计算.月骨重);
          console.log('  日骨重:', chengguInfo.详细计算.日骨重);
          console.log('  时骨重:', chengguInfo.详细计算.时骨重);
          console.log('  总骨重:', chengguInfo.详细计算.总骨重);
        }
        
        console.log('\n🎉 八字报告重新生成完成！称骨信息已补充。');
        
      } else {
        console.log('❌ 报告生成失败:', reportResult.error);
        await baziOrder.update({
          reportStatus: 'failed',
          errorMessage: reportResult.error
        });
      }

    } catch (apiError) {
      console.error('❌ 八字API调用失败:', apiError);
      await baziOrder.update({
        reportStatus: 'failed',
        errorMessage: apiError.message
      });
    }
    
  } catch (error) {
    console.error('重新生成失败:', error.message);
  } finally {
    await sequelize.close();
  }
}

regenerateWithChenggu();
