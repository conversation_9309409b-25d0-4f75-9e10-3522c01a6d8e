#!/bin/bash

# 重新部署修复后的后端到远程服务器
REMOTE_HOST="root@************"
PASSWORD="Noending5@"
REMOTE_PATH="/data/commission-platform8z"

echo "🚀 重新部署修复后的后端到远程服务器..."

# 1. 上传修复后的 package.json
echo "📤 上传修复后的 package.json..."
expect << EOF
set timeout 30
spawn scp backend/package.json $REMOTE_HOST:$REMOTE_PATH/backend/
expect {
    "password:" {
        send "$PASSWORD\r"
        exp_continue
    }
    eof
}
EOF

# 2. 在远程服务器上重新安装依赖并启动服务
echo "🔧 在远程服务器上重新安装依赖并启动服务..."
expect << EOF
set timeout 120
spawn ssh -o StrictHostKeyChecking=no $REMOTE_HOST

expect {
    "password:" {
        send "$PASSWORD\r"
        exp_continue
    }
    "# " {
        send "cd $REMOTE_PATH/backend\r"
        expect "# "
        
        send "echo '🛑 停止现有服务...'\r"
        expect "# "
        send "pm2 stop commission-platform-api 2>/dev/null || echo '服务未运行'\r"
        expect "# "
        send "pm2 delete commission-platform-api 2>/dev/null || echo '服务不存在'\r"
        expect "# "
        
        send "echo '🧹 清理 node_modules...'\r"
        expect "# "
        send "rm -rf node_modules package-lock.json\r"
        expect "# "
        
        send "echo '📦 重新安装所有依赖...'\r"
        expect "# "
        send "npm install\r"
        expect "# " { sleep 30 }
        
        send "echo '🔍 验证 node-fetch 安装...'\r"
        expect "# "
        send "ls -la node_modules/node-fetch/ && echo 'node-fetch 安装成功' || echo 'node-fetch 安装失败'\r"
        expect "# "
        
        send "echo '🧪 测试手动启动...'\r"
        expect "# "
        send "timeout 10s node src/index.js &\r"
        expect "# "
        send "sleep 5\r"
        expect "# "
        send "pkill -f 'node src/index.js' 2>/dev/null || echo '测试进程已结束'\r"
        expect "# "
        
        send "echo '🚀 使用PM2启动服务...'\r"
        expect "# "
        send "pm2 start start-production.js --name commission-platform-api\r"
        expect "# "
        
        send "echo '⏳ 等待服务启动...'\r"
        expect "# "
        send "sleep 15\r"
        expect "# "
        
        send "echo '📊 检查服务状态...'\r"
        expect "# "
        send "pm2 status\r"
        expect "# "
        
        send "echo '🔍 查看最新日志...'\r"
        expect "# "
        send "pm2 logs commission-platform-api --lines 15 --nostream\r"
        expect "# "
        
        send "echo '🧪 测试API连接...'\r"
        expect "# "
        send "curl -f http://localhost:3005/api/auth/login -X POST -H 'Content-Type: application/json' -d '{\"username\":\"admin\",\"password\":\"admin123\"}' 2>/dev/null && echo '✅ API测试成功' || echo '❌ API测试失败'\r"
        expect "# "
        
        send "echo '💾 保存PM2配置...'\r"
        expect "# "
        send "pm2 save\r"
        expect "# "
        
        send "echo '✅ 部署完成！'\r"
        expect "# "
        
        send "exit\r"
    }
    timeout {
        puts "连接超时"
        exit 1
    }
}
expect eof
EOF

echo ""
echo "🎉 重新部署完成！"
echo ""
echo "📊 验证部署结果:"
echo "  1. 检查服务状态: ssh root@************ 'pm2 status'"
echo "  2. 查看日志: ssh root@************ 'pm2 logs commission-platform-api'"
echo "  3. 测试API: curl -f https://ye.bzcy.xyz/api/auth/login"
echo ""
echo "🌐 如果一切正常，网站应该可以访问: https://ye.bzcy.xyz"
