# Sequelize 关联查询最佳实践指南

## 常见错误类型

1. **多重关联错误**
   ```
   EagerLoadingError [SequelizeEagerLoadingError]: [Model] is associated to [AnotherModel] multiple times. 
   To identify the correct association, you must use the 'as' keyword to specify the alias of the association you want to include.
   ```

2. **关联未定义错误**
   ```
   EagerLoadingError [SequelizeEagerLoadingError]: [AnotherModel] is not associated to [Model]!
   ```

## 解决方案

### 1. 关联定义规范

#### 1.1 命名规范
- 使用格式：`[model_role]_[relation_type]`
- 例如：
  ```javascript
  // 订单相关
  order_product    // 订单的产品
  order_agent     // 订单的代理
  
  // 代理相关
  agent_orders    // 代理的订单
  agent_commissions // 代理的佣金
  
  // 产品相关
  product_orders  // 产品的订单
  product_links   // 产品的推广链接
  ```

#### 1.2 关联定义模板
```javascript
// 一对多关系
MainModel.hasMany(SubModel, {
  foreignKey: 'mainModelId',
  as: 'main_subs'  // 主模型的子模型集合
});

SubModel.belongsTo(MainModel, {
  foreignKey: 'mainModelId',
  as: 'sub_main'   // 子模型的主模型
});

// 多对多关系
ModelA.belongsToMany(ModelB, {
  through: 'JoinTable',
  foreignKey: 'modelAId',
  otherKey: 'modelBId',
  as: 'a_bs'       // A的B集合
});

ModelB.belongsToMany(ModelA, {
  through: 'JoinTable',
  foreignKey: 'modelBId',
  otherKey: 'modelAId',
  as: 'b_as'       // B的A集合
});
```

### 2. 查询规范

#### 2.1 基本查询模板
```javascript
const result = await MainModel.findOne({
  where: { /* 查询条件 */ },
  include: [
    {
      model: SubModel,
      as: 'main_subs',  // 必须与关联定义中的as保持一致
      attributes: ['id', 'name'], // 明确指定需要的字段
      where: { /* 子模型查询条件 */ }  // 可选
    }
  ],
  attributes: ['id', 'name']  // 明确指定需要的字段
});
```

#### 2.2 多层嵌套查询模板
```javascript
const result = await MainModel.findOne({
  where: { /* 查询条件 */ },
  include: [
    {
      model: SubModel,
      as: 'main_subs',
      include: [
        {
          model: ThirdModel,
          as: 'sub_thirds',
          attributes: ['id', 'name']
        }
      ]
    }
  ]
});
```

### 3. 错误处理最佳实践

#### 3.1 处理多重关联错误
```javascript
// ❌ 错误示例
const order = await Order.findOne({
  include: [Commission]  // 未指定 as
});

// ✅ 正确示例
const order = await Order.findOne({
  include: [{
    model: Commission,
    as: 'order_commissions'  // 明确指定关联别名
  }]
});
```

#### 3.2 处理复杂查询
```javascript
// 定义标准查询选项
const standardInclude = {
  order: [
    {
      model: Product,
      as: 'order_product',
      attributes: ['id', 'title', 'price']
    },
    {
      model: User,
      as: 'order_agent',
      attributes: ['id', 'username']
    }
  ],
  commission: [
    {
      model: Order,
      as: 'commission_order',
      attributes: ['id', 'amount']
    }
  ]
};

// 使用标准查询选项
const result = await Order.findAll({
  include: standardInclude.order
});
```

### 4. 性能优化建议

1. **选择性加载**
   ```javascript
   // 只加载必要的关联
   const result = await Order.findAll({
     include: [
       {
         model: Product,
         as: 'order_product',
         attributes: ['id', 'title']  // 只选择需要的字段
       }
     ]
   });
   ```

2. **分页加载**
   ```javascript
   const { page, pageSize } = req.query;
   const result = await Order.findAndCountAll({
     include: [{
       model: Product,
       as: 'order_product'
     }],
     limit: pageSize,
     offset: (page - 1) * pageSize
   });
   ```

3. **延迟加载**
   ```javascript
   // 先获取主数据
   const order = await Order.findByPk(orderId);
   
   // 需要时再加载关联数据
   if (needProductInfo) {
     const product = await order.getOrder_product();
   }
   ```

## 调试技巧

1. **启用 Sequelize 日志**
   ```javascript
   const sequelize = new Sequelize(config.database, {
     logging: console.log  // 开发环境启用
   });
   ```

2. **检查关联定义**
   ```javascript
   console.log(Model.associations);  // 查看模型的所有关联
   ```

## 常见问题解决方案

1. **关联未找到**
   - 检查 `associations.js` 中的关联定义
   - 确保关联名称（as）完全匹配
   - 验证外键名称正确

2. **多重关联冲突**
   - 始终使用 `as` 指定关联别名
   - 避免在同一查询中使用相同的关联名称
   - 使用标准化的关联命名

3. **性能问题**
   - 使用 `attributes` 限制返回字段
   - 适当使用 `separate` 选项分离查询
   - 考虑使用子查询优化

## 代码审查清单

在提交代码前，确保：

- [ ] 所有关联查询都明确指定了 `as` 别名
- [ ] 关联名称与 `associations.js` 中的定义匹配
- [ ] 查询只加载必要的字段和关联
- [ ] 大数据查询已实现分页
- [ ] 已添加适当的错误处理
- [ ] 关联查询的性能已经过测试
