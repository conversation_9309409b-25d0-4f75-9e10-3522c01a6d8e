import sequelize from '../config/database.js';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 迁移文件列表（按执行顺序）
const migrations = [
  '20241213_add_type_to_products.js',
  '20241213_update_orders_table.js',
  '20241213_create_bazi_orders.js'
];

async function runMigrations() {
  try {
    console.log('开始执行数据库迁移...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // 获取QueryInterface
    const queryInterface = sequelize.getQueryInterface();

    // 执行每个迁移
    for (const migrationFile of migrations) {
      console.log(`\n执行迁移: ${migrationFile}`);
      
      try {
        const migrationPath = join(__dirname, '../migrations', migrationFile);
        
        if (!fs.existsSync(migrationPath)) {
          console.log(`迁移文件不存在，跳过: ${migrationFile}`);
          continue;
        }

        const migration = await import(migrationPath);
        
        if (migration.up) {
          await migration.up(queryInterface, sequelize.Sequelize);
          console.log(`✅ 迁移成功: ${migrationFile}`);
        } else {
          console.log(`⚠️  迁移文件没有up方法: ${migrationFile}`);
        }
      } catch (error) {
        console.error(`❌ 迁移失败: ${migrationFile}`, error.message);
        // 继续执行其他迁移，不中断
      }
    }

    console.log('\n所有迁移执行完成！');
    
  } catch (error) {
    console.error('迁移执行失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigrations();
}

export default runMigrations;