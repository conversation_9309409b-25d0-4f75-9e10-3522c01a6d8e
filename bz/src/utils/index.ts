import { format } from 'date-fns';

// 获取URL参数
export function getQueryString(): Record<string, string> {
  const params = new URLSearchParams(window.location.search);
  const result: Record<string, string> = {};
  
  for (const [key, value] of params.entries()) {
    result[key] = value;
  }
  
  return result;
}

// 计算年龄
export function calcAge(date: { currentDate: number[] }): number {
  const [year] = date.currentDate;
  const currentYear = new Date().getFullYear();
  return currentYear - year;
}

// 格式化日期
export function formatDate(date: { yearType: number; currentDate: number[]; currentTime: number[] }): string {
  const yearTypeText = date.yearType === 1 ? '公历' : '农历';
  const dateStr = date.currentDate.join('-');
  const timeStr = date.currentTime.join(':');
  
  return `${yearTypeText} ${dateStr} ${timeStr}`;
}

// 解析JSON字符串
export function tryParse(str: string): any {
  try {
    return JSON.parse(str);
  } catch {
    return {};
  }
}

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), wait);
    }
  };
}

// 类名合并
export function clsx(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

// 导出 clsx 作为默认导出以兼容其他导入方式
export { clsx as default };

// 生成随机ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9);
}

// 格式化金额
export function formatAmount(amount: string | number): string {
  const num = typeof amount === 'string' ? parseFloat(amount) : amount;
  return num.toFixed(2);
}
