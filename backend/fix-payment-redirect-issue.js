#!/usr/bin/env node

/**
 * 修复支付成功后跳转问题的完整解决方案
 * 
 * 问题分析：
 * 1. 用户支付成功后仍跳转到百度网盘，而不是八字报告页面
 * 2. 根本原因：八字报告状态为 pending，API返回500错误
 * 3. 深层原因：腾讯云API环境变量未配置
 * 
 * 解决步骤：
 * 1. 配置腾讯云API环境变量
 * 2. 重启生产服务
 * 3. 为现有pending订单生成报告
 * 4. 验证修复效果
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载生产环境配置
const envFile = '.env.production';
const envPath = path.resolve(process.cwd(), '..', envFile);
dotenv.config({ path: envPath });

console.log('🔧 支付跳转问题修复指南');
console.log('=' .repeat(50));

// 1. 检查环境变量配置
console.log('\n📋 1. 检查环境变量配置');
const requiredEnvVars = {
  'TENCENT_API_SECRET_ID': process.env.TENCENT_API_SECRET_ID,
  'TENCENT_API_SECRET_KEY': process.env.TENCENT_API_SECRET_KEY,
  'TENCENT_API_BASE_URL': process.env.TENCENT_API_BASE_URL,
  'FRONTEND_URL': process.env.FRONTEND_URL,
  'NODE_ENV': process.env.NODE_ENV
};

let hasConfigIssues = false;
for (const [key, value] of Object.entries(requiredEnvVars)) {
  if (!value || value === 'your_secret_id_here' || value === 'your_secret_key_here' || value.includes('xxxxx')) {
    console.log(`❌ ${key}: 未配置或使用默认值`);
    hasConfigIssues = true;
  } else {
    console.log(`✅ ${key}: 已配置`);
  }
}

if (hasConfigIssues) {
  console.log('\n🚨 发现配置问题！');
  console.log('\n请按以下步骤修复：');
  console.log('\n1️⃣ 编辑 .env.production 文件：');
  console.log('   vi /Users/<USER>/Documents/workspace/Trae/commission-platform8z/.env.production');
  console.log('\n2️⃣ 更新以下配置项为真实值：');
  console.log('   TENCENT_API_SECRET_ID=你的腾讯云SecretId');
  console.log('   TENCENT_API_SECRET_KEY=你的腾讯云SecretKey');
  console.log('   TENCENT_API_BASE_URL=你的腾讯云API网关地址');
  console.log('\n3️⃣ 重启生产服务：');
  console.log('   pm2 restart all  # 或者你使用的其他进程管理器');
  console.log('\n4️⃣ 重新运行此脚本验证配置');
  process.exit(1);
}

console.log('\n✅ 环境变量配置正确！');

// 2. 检查数据库中pending状态的八字订单
console.log('\n📋 2. 检查待处理的八字订单');

try {
  // 动态导入模型
  const { default: sequelize } = await import('./src/config/database.js');
  const { default: Order } = await import('./src/models/Order.js');
  const { default: BaziOrder } = await import('./src/models/BaziOrder.js');
  const { default: Product } = await import('./src/models/Product.js');
  
  // 设置模型关联
  await import('./src/models/associations.js');
  
  // 查找所有已支付但报告状态为pending的八字订单
  const pendingOrders = await Order.findAll({
    where: {
      status: 'paid'
    },
    include: [{
      model: BaziOrder,
      as: 'baziOrder',
      where: {
        reportStatus: 'pending'
      },
      required: true
    }, {
      model: Product,
      as: 'product',
      where: {
        type: 'service'
      },
      required: true
    }]
  });
  
  console.log(`\n找到 ${pendingOrders.length} 个需要生成报告的订单：`);
  
  if (pendingOrders.length === 0) {
    console.log('✅ 没有待处理的订单');
  } else {
    for (const order of pendingOrders) {
      console.log(`📄 订单ID: ${order.id}, 客户: ${order.customerInfo ? JSON.parse(order.customerInfo).name : '未知'}, 创建时间: ${order.createdAt}`);
    }
    
    console.log('\n3️⃣ 生成缺失的报告：');
    console.log('运行以下命令为这些订单生成报告：');
    for (const order of pendingOrders) {
      console.log(`node generate-missing-report.js ${order.id}`);
    }
  }
  
} catch (error) {
  console.error('❌ 检查订单时出错:', error.message);
}

console.log('\n📋 3. 验证修复效果');
console.log('\n修复完成后，请验证：');
console.log('1. 访问八字报告API: https://ye.bzcy.xyz/api/bazi/orders/250707185840805299/report');
console.log('2. 模拟支付回调，确认跳转到八字报告页面');
console.log('3. 检查新订单的支付流程是否正常');

console.log('\n🎉 修复指南完成！');
console.log('\n如果问题仍然存在，请检查：');
console.log('- 生产服务是否已重启');
console.log('- 前端代码是否已部署最新版本');
console.log('- 网络和DNS配置是否正确');