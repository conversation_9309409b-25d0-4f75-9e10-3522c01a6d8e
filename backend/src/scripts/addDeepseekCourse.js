import { Product } from '../models/index.js';

/**
 * 添加Deepseek人工智能课程产品
 */
async function addDeepseekCourse() {
  try {
    console.log('开始创建Deepseek人工智能课程产品...');
    
    const product = await Product.create({
      title: '2025最新Deepseek AI大模型实战课程',
      description: `# Deepseek AI大模型实战课程

## 课程介绍
这是一门全面的Deepseek AI大模型实战课程，专为想要掌握最新人工智能技术的学习者设计。Deepseek是国内领先的开源大模型，本课程将带你深入了解如何有效利用这一强大工具。

## 课程内容
1. **Deepseek模型基础**
   - Deepseek模型架构与特点
   - 与其他大模型的对比分析
   - Deepseek的技术优势

2. **实用提示工程技巧**
   - 掌握高效提示词编写方法
   - 上下文优化与信息提取
   - 解决常见问题的提示模板

3. **Deepseek编程助手应用**
   - 代码生成与调试技巧
   - 自动化编程流程
   - 提高编程效率的最佳实践

4. **大模型微调与定制**
   - 模型微调基础与进阶
   - 领域特定知识注入
   - 构建个性化AI助手

5. **企业级应用案例**
   - 客户服务自动化
   - 内容创作与管理
   - 数据分析与决策支持

## 适合人群
- 程序开发人员
- 产品经理与设计师
- 数据科学家
- 企业决策者
- AI爱好者与学习者

## 学习成果
完成本课程后，你将能够熟练运用Deepseek大模型解决实际问题，提高工作效率，并具备定制和优化AI模型的能力。

## 课程特色
- 实战导向，注重实用技能
- 最新技术，与行业同步
- 案例丰富，易于理解
- 专家指导，解答疑惑
`,
      price: 1999.00,
      baseCommissionRate: 60,
      status: 'active'
    });
    
    console.log('Deepseek人工智能课程产品创建成功:', {
      id: product.id,
      title: product.title,
      price: product.price
    });
    
    return product;
  } catch (error) {
    console.error('创建Deepseek人工智能课程产品失败:', error);
    throw error;
  }
}

// 执行函数
addDeepseekCourse()
  .then(() => {
    console.log('脚本执行完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
