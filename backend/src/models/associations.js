import User from './User.js';
import Product from './Product.js';
import Order from './Order.js';
import Commission from './Commission.js';
import Withdrawal from './Withdrawal.js';
import ProductLink from './ProductLink.js';
import PaymentConfig from './PaymentConfig.js';
import ProductImage from './ProductImage.js';
import BaziOrder from './BaziOrder.js';

// ========== User 相关关联 ==========
// 1. 用户上下级关系
User.belongsTo(User, { 
  as: 'parentAgent',
  foreignKey: 'parentAgentId'
});

User.hasMany(User, { 
  as: 'subAgents',
  foreignKey: 'parentAgentId'
});

// 2. 用户-订单关系
User.hasMany(Order, {
  as: 'orders',
  foreignKey: 'agentId'
});

// 3. 用户-佣金关系
User.hasMany(Commission, {
  as: 'commissions',
  foreignKey: 'agentId'
});

// 4. 用户-推广链接关系
User.hasMany(ProductLink, {
  as: 'productLinks',
  foreignKey: 'agentId'
});

ProductLink.belongsTo(User, {
  as: 'linkAgent',
  foreignKey: 'agentId'
});

// 5. 用户-提现关系
User.hasMany(Withdrawal, {
  as: 'withdrawals',
  foreignKey: 'agentId'
});

// 9. 用户-支付配置关系
User.hasOne(PaymentConfig, {
  as: 'paymentConfig',
  foreignKey: 'userId'
});

PaymentConfig.belongsTo(User, {
  as: 'user',
  foreignKey: 'userId'
});

// ========== Order 相关关联 ==========
// 1. 订单-代理人关系
Order.belongsTo(User, {
  as: 'orderAgent',
  foreignKey: 'agentId'
});

// 2. 订单-上级代理关系
Order.belongsTo(User, {
  as: 'orderParentAgent',
  foreignKey: 'parentAgentId'
});

// 3. 订单-产品关系
Order.belongsTo(Product, {
  as: 'product',
  foreignKey: 'productId'
});

Product.hasMany(Order, {
  as: 'orders',
  foreignKey: 'productId'
});

// 4. 订单-推广链接关系
Order.belongsTo(ProductLink, {
  as: 'productLink',
  foreignKey: 'productLinkId'
});

ProductLink.hasMany(Order, {
  as: 'orders',
  foreignKey: 'productLinkId'
});

// 5. 订单-佣金关系
Order.hasMany(Commission, {
  as: 'commissions',
  foreignKey: 'orderId'
});

// 6. 订单-八字查询关系
Order.hasOne(BaziOrder, {
  as: 'baziOrder',
  foreignKey: 'orderId'
});

BaziOrder.belongsTo(Order, {
  as: 'order',
  foreignKey: 'orderId'
});

// ========== Commission 相关关联 ==========
// 1. 佣金-用户关系
Commission.belongsTo(User, {
  as: 'commissionAgent',
  foreignKey: 'agentId'
});

// 2. 佣金-订单关系
Commission.belongsTo(Order, {
  as: 'order',
  foreignKey: 'orderId'
});

// ========== ProductLink 相关关联 ==========
// 1. 产品-推广链接关系
Product.hasMany(ProductLink, {
  as: 'productLinks',
  foreignKey: 'productId'
});

ProductLink.belongsTo(Product, {
  as: 'product',
  foreignKey: 'productId'
});

// ========== ProductImage 相关关联 ==========
// 1. 产品-图片关系
Product.hasMany(ProductImage, {
  as: 'images',
  foreignKey: 'productId'
});

ProductImage.belongsTo(Product, {
  as: 'product',
  foreignKey: 'productId'
});

// ========== Withdrawal 相关关联 ==========
// 1. 提现-用户关系
Withdrawal.belongsTo(User, {
  as: 'withdrawalAgent',
  foreignKey: 'agentId'
});

// 2. 提现-审批人关系
Withdrawal.belongsTo(User, {
  as: 'withdrawalApprover',  
  foreignKey: 'approverId'
});

User.hasMany(Withdrawal, {
  as: 'approvedWithdrawals',
  foreignKey: 'approverId'
});

// 3. 提现-佣金关系
Withdrawal.hasMany(Commission, {
  as: 'commissions',
  foreignKey: 'withdrawalId'
});

Commission.belongsTo(Withdrawal, {
  as: 'withdrawal',
  foreignKey: 'withdrawalId'
});

const setupAssociations = () => {
  console.log('Database associations have been set up successfully');
};

export {
  User,
  Product,
  Order,
  Commission,
  Withdrawal,
  ProductLink,
  PaymentConfig,
  ProductImage,
  BaziOrder
};

export default setupAssociations;
