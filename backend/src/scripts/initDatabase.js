import { User, Product, Order, Commission, Withdrawal } from '../models/index.js';
import sequelize from '../config/database.js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../../.env') });

console.log('Database Config:', {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  database: process.env.DB_NAME
});

const initDatabase = async () => {
  try {
    console.log('开始初始化数据库...');

    // 强制重新创建所有表
    await sequelize.drop();
    await sequelize.sync({ force: true });

    console.log('所有表创建成功！表结构如下：');
    
    // 获取并打印所有表的结构
    const tables = await sequelize.getQueryInterface().showAllTables();
    for (const tableName of tables) {
      const tableInfo = await sequelize.getQueryInterface().describeTable(tableName);
      console.log(`\n表名: ${tableName}`);
      console.log('字段结构:', JSON.stringify(tableInfo, null, 2));
    }

    console.log('\n数据库初始化完成！');
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
  } finally {
    await sequelize.close();
    process.exit();
  }
};

// 执行初始化
console.log('警告：此操作将删除所有现有数据！');
console.log('3秒后开始执行...');
setTimeout(initDatabase, 3000); 