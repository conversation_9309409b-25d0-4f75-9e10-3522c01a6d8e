/**
 * 测试实际的八字产品支付流程
 * 模拟真实的支付回调请求
 */

import express from 'express';
import { Order, ProductLink, Product, User } from './src/models/index.js';
import sequelize from './src/config/database.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

console.log('🧪 实际八字产品支付流程测试');
console.log('='.repeat(50));

async function testActualBaziPayment() {
  try {
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');
    
    // 1. 查找一个实际的八字订单
    console.log('\n1️⃣ 查找实际的八字订单:');
    
    const baziOrder = await Order.findOne({
      include: [{
        model: ProductLink,
        as: 'productLink',
        include: [{
          model: Product,
          as: 'product',
          where: { 
            type: 'service',
            title: { [sequelize.Sequelize.Op.like]: '%八字%' }
          }
        }, {
          model: User,
          as: 'linkAgent',
          include: [{
            model: User,
            as: 'parentAgent'
          }]
        }]
      }],
      order: [['createdAt', 'DESC']]
    });
    
    if (!baziOrder) {
      console.log('❌ 没有找到八字订单');
      return;
    }
    
    console.log(`找到八字订单: ${baziOrder.id}`);
    console.log(`订单状态: ${baziOrder.status}`);
    console.log(`产品信息: ${baziOrder.productLink.product.title} (${baziOrder.productLink.product.type})`);
    
    // 2. 模拟支付回调处理逻辑（完全复制 payment.js 中的逻辑）
    console.log('\n2️⃣ 模拟支付回调处理:');
    
    const orderId = baziOrder.id;
    const order = baziOrder;
    
    console.log('【支付宝同步回调】订单完整信息:', JSON.stringify({
      id: order.id,
      status: order.status,
      productLink: {
        product: {
          title: order.productLink?.product?.title,
          type: order.productLink?.product?.type
        }
      }
    }, null, 2));
    
    const productTitle = order.productLink?.product?.title || '';
    const productType = order.productLink?.product?.type || '';
    console.log('【支付宝同步回调】提取的产品标题:', productTitle);
    console.log('【支付宝同步回调】提取的产品类型:', productType);
    
    // 判断产品类型
    let redirectUrl;
    let productTypeDesc;
    
    // 1. 检查是否是八字查询服务
    const isBaziService = productType === 'service' && 
                         (productTitle.includes('八字') || productTitle.includes('命理'));
    
    // 2. 检查是否是Deepseek AI课程
    const hasDeepseek = productTitle.includes('Deepseek');
    const hasAI = productTitle.includes('AI');
    const hasModel = productTitle.includes('大模型');
    const isDeepseekCourse = hasDeepseek || hasModel || hasAI;
    
    console.log('【支付宝同步回调】关键词检查结果:', {
      hasDeepseek,
      hasAI,
      hasModel,
      isBaziService
    });
    
    // 根据产品类型决定跳转地址
    if (isBaziService) {
      redirectUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=${orderId}`;
      productTypeDesc = '八字查询服务';
    } else if (isDeepseekCourse) {
      redirectUrl = process.env.PAID_RETURN_URL2;
      productTypeDesc = 'Deepseek AI课程';
    } else {
      redirectUrl = process.env.PAID_RETURN_URL;
      productTypeDesc = '儿童纪录片';
    }
    
    console.log('【支付宝同步回调】产品类型判断结果:', productTypeDesc);
    console.log('【支付宝同步回调】环境变量值:', {
      PAID_RETURN_URL: process.env.PAID_RETURN_URL,
      PAID_RETURN_URL2: process.env.PAID_RETURN_URL2,
      FRONTEND_URL: process.env.FRONTEND_URL
    });
    console.log('【支付宝同步回调】将要跳转到:', redirectUrl);
    
    // 3. 验证跳转结果
    console.log('\n3️⃣ 跳转结果验证:');
    
    if (redirectUrl.includes('/bazi-report')) {
      console.log('✅ 跳转正确：八字报告页面');
      console.log(`   完整地址: ${redirectUrl}?status=success&orderId=${orderId}`);
    } else if (redirectUrl.includes('baidu.com') || redirectUrl.includes('pan.baidu')) {
      console.log('❌ 跳转错误：百度网盘地址');
      console.log(`   这说明产品类型判断有问题`);
    } else if (redirectUrl.includes('quark.cn')) {
      console.log('❌ 跳转错误：夸克网盘地址');
      console.log(`   这说明被误判为Deepseek课程`);
    } else {
      console.log('⚠️  跳转到未知地址:', redirectUrl);
    }
    
    // 4. 检查可能的边缘情况
    console.log('\n4️⃣ 边缘情况检查:');
    
    // 检查订单状态
    if (order.status === 'paid') {
      console.log('ℹ️  订单已支付，会走"订单已处理过"的逻辑分支');
    } else {
      console.log('ℹ️  订单未支付，会走正常的支付处理逻辑分支');
    }
    
    // 检查产品关联
    if (!order.productLink) {
      console.log('❌ 订单没有关联的产品链接');
    } else if (!order.productLink.product) {
      console.log('❌ 产品链接没有关联的产品');
    } else {
      console.log('✅ 订单和产品关联正常');
    }
    
    // 5. 生成测试报告
    console.log('\n5️⃣ 测试报告:');
    console.log(`订单ID: ${orderId}`);
    console.log(`产品标题: "${productTitle}"`);
    console.log(`产品类型: ${productType}`);
    console.log(`是否八字服务: ${isBaziService}`);
    console.log(`预期跳转: ${redirectUrl}`);
    
    if (isBaziService && redirectUrl.includes('/bazi-report')) {
      console.log('\n🎉 测试结果: 八字产品支付跳转逻辑正常！');
      console.log('如果实际还是跳转到百度网盘，可能的原因:');
      console.log('1. 实际支付的不是这个订单');
      console.log('2. 支付回调时订单数据查询失败');
      console.log('3. 环境变量在运行时被覆盖');
      console.log('4. 有其他代码路径处理了跳转');
    } else {
      console.log('\n❌ 测试结果: 发现问题！');
      console.log('八字产品没有正确跳转到报告页面');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await sequelize.close();
  }
}

// 运行测试
testActualBaziPayment().catch(console.error);
