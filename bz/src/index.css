@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  html, body {
    margin: 0;
    padding: 0;
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #ecedee;
  }

  #root {
    min-height: 100vh;
  }

  .top-animate {
    padding: 35px 0;
    background: linear-gradient(141deg, #e87947 6%, #eb8833 52%, #e9543f 100%);
  }

  .compass {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    position: relative;
    z-index: 100;
  }

  .compass .outside {
    width: 200px;
    height: 200px;
    background: url('/assets/images/bz-vip-t1-2.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -100px;
    margin-left: -100px;
  }

  .compass .inside {
    width: 150px;
    height: 150px;
    background: url('/assets/images/bz-vip-t1-1.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -75px;
    margin-left: -75px;
  }

  .compass .cover {
    width: 200px;
    height: 200px;
    background: url('/assets/images/bz-vip-t1-3.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    z-index: 100;
    top: 50%;
    left: 50%;
    margin-top: -100px;
    margin-left: -100px;
  }
}

  .main-form {
    margin: 0 auto;
    padding: 0 16px;
    padding-bottom: 0;
  }

  .main-form .bg-title {
    height: 41.7px;
    background: url('/assets/images/bz-vip-t4.png') no-repeat center;
    background-size: auto 41.7px;
    position: relative;
    z-index: 200;
  }

  .main-form .inner {
    padding: 20px;
    background: #FEF4E0;
    border: 2px solid #E1B467;
    border-radius: 20px;
    margin-top: -21px;
  }

  .main-form .van-field {
    border-radius: 8px;
    margin: 12px 0;
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .main-form .van-field::after {
    display: none;
  }

  .main-form .van-field:last-child {
    margin-bottom: 0;
  }

  .main-form .van-cell__title {
    flex: none;
    font-size: 15px;
    font-weight: bold;
    color: #3f2410;
    width: 80px;
  }

  .main-form .van-radio:first-child {
    margin-right: 30px;
  }

  .van-radio-group {
    display: flex;
    gap: 30px;
  }

  .main-form .submit {
    height: 68px;
    display: block;
    margin-top: 12px;
    background: url('/assets/images/bz-vip-t3.png') no-repeat;
    background-size: auto 100%;
  }

  .main-form .history {
    font-size: 16px;
    color: red;
    padding-top: 12px;
    text-align: center;
  }

@layer components {
  .field-container {
    background: white;
    border-radius: 8px;
    margin: 12px 0;
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .field-label {
    font-weight: bold;
    font-size: 15px;
    color: #3f2410;
    width: 80px;
    flex: none;
    padding-left: 16px;
  }

  .field-input {
    flex: 1;
    text-align: right;
    font-size: 15px;
    color: #666;
    padding-right: 16px;
    outline: none;
  }

  .radio-group {
    display: flex;
    gap: 30px;
  }

  .radio-item {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
  }

  .radio-circle {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    border: 2px solid #b87c3d;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .radio-circle.checked {
    background-color: #b87c3d;
  }

  .radio-circle.checked::after {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
  }

  .tag-button {
    padding: 4px 12px;
    font-size: 14px;
    border: 1px solid #b87c3d;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.2s;
    color: #b87c3d;
    background: white;
  }

  .tag-button.selected {
    background-color: #b87c3d;
    color: white;
  }

  .submit-button {
    width: 100%;
    height: 68px;
    display: block;
    margin-top: 12px;
    background: url('/assets/images/bz-vip-t3.png') no-repeat;
    background-size: auto 100%;
    background-position: center;
    cursor: pointer;
    border: none;
  }

  .image-block {
    padding-top: 23px;
    padding-left: 16px;
    padding-right: 16px;
  }

  .image-block .title {
    height: 41.7px;
    background: url('/assets/images/bz-vip-t5.png') no-repeat center;
    background-size: auto 41.7px;
    position: relative;
    z-index: 200;
    color: #FEF1C3;
    font-size: 20px;
    line-height: 43px;
    text-align: center;
  }

  .image-block .title ~ .content {
    margin-top: -21px;
    padding-top: 20px;
  }

  .image-block .content {
    background: #FEF4E0;
    border: 2px solid #E1B467;
    border-radius: 20px;
    overflow: hidden;
  }

  /* Home2 专用样式 */
  .page.home2 {
    padding-bottom: 0;
    background: #864a30;
  }

  .page.home2 .banner {
    width: 100%;
    display: block;
  }

  .page.home2 section.block {
    padding: 12px;
    background: #864a30;
  }

  .page.home2 section.block img {
    width: 100%;
    display: block;
  }

  .page.home2 .main-form {
    position: relative;
    padding-top: 14px;
    padding-bottom: 16px;
    background: #FFEEC6;
    border-radius: 4px;
    overflow: hidden;
  }

  .page.home2 .main-form .bg-title {
    display: none;
  }

  .page.home2 .main-form .history {
    display: none;
  }

  .page.home2 .main-form .inner {
    border: none;
    background: none;
    border-radius: 0;
    margin-top: 0;
    padding: 10px 22px 12px;
  }

  .page.home2 .main-form .van-field:first-child {
    margin-top: 0;
  }

  .page.home2 .main-form .van-field:last-child {
    margin-bottom: 0;
  }

  .page.home2 .main-form .submit {
    margin-top: 0;
    background: url('/assets/theme/2/6.png') no-repeat center;
    background-size: 90% auto;
    animation: scale-animate 1.8s ease-in-out infinite;
    height: 60px;
    cursor: pointer;
    border: none;
    outline: none;
  }

  /* TopAnimate2 样式 */
  .top-animate2 .inner {
    position: relative;
  }

  .top-animate2 .bg {
    display: block;
    width: 100%;
  }

  .top-animate2 .large,
  .top-animate2 .lite,
  .top-animate2 .cover {
    display: block;
    position: absolute;
    top: 43%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .top-animate2 .large {
    width: 200px;
    height: 200px;
    animation: rotate0 20s linear infinite;
  }

  .top-animate2 .lite {
    width: 130px;
    height: 130px;
    animation: rotate1 20s linear infinite;
  }

  .top-animate2 .cover {
    width: 96px;
    height: 96px;
  }

  /* CSS旋转动画 */
  @keyframes rotate0 {
    from {
      transform: translate(-50%, -50%) rotate(0);
    }
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  @keyframes rotate1 {
    from {
      transform: translate(-50%, -50%) rotate(0);
    }
    to {
      transform: translate(-50%, -50%) rotate(-360deg);
    }
  }

  /* 动画 */
  @keyframes scale-animate {
    from {
      transform: scale3d(1, 1, 1);
    }
    50% {
      transform: scale3d(0.9, 0.9, 0.9);
    }
    to {
      transform: scale3d(1, 1, 1);
    }
  }

  @keyframes marquee {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  /* Vant ActionSheet 样式 */
  .van-action-sheet {
    max-height: 90%;
    color: #323233;
    background-color: #fff;
    border-radius: 16px 16px 0 0;
  }

  .van-action-sheet__header {
    padding: 16px 16px 8px;
    text-align: center;
    border-bottom: 1px solid #ebedf0;
  }

  .van-action-sheet__description {
    padding: 0 16px 16px;
    color: #969799;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
  }

  .van-action-sheet__content {
    max-height: 60vh;
    overflow-y: auto;
  }

  .van-action-sheet__item {
    display: block;
    width: 100%;
    padding: 14px 16px;
    font-size: 16px;
    line-height: 22px;
    color: #323233;
    background-color: transparent;
    border: none;
    cursor: pointer;
    text-align: center;
  }

  .van-action-sheet__item:active {
    background-color: #f2f3f5;
  }

  .van-action-sheet__cancel {
    height: 50px;
    margin-top: 8px;
    background-color: #fff;
    border: none;
    font-size: 16px;
    color: #646566;
    cursor: pointer;
    border-radius: 0;
  }

  /* Vant Popup 样式 */
  .van-popup {
    position: fixed;
    max-height: 100%;
    overflow-y: auto;
    background-color: #fff;
    transition: transform 0.3s;
    -webkit-overflow-scrolling: touch;
  }

  .van-popup--bottom {
    bottom: 0;
    left: 0;
    width: 100%;
    border-radius: 16px 16px 0 0;
  }

  /* Vant Picker 样式 */
  .van-picker__toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 44px;
    padding: 0 16px;
    background-color: #fff;
    border-bottom: 1px solid #ebedf0;
  }

  .van-picker__cancel,
  .van-picker__confirm {
    height: 44px;
    padding: 0;
    font-size: 14px;
    background-color: transparent;
    border: none;
    cursor: pointer;
  }

  .van-picker__cancel {
    color: #969799;
  }

  .van-picker__confirm {
    color: #1989fa;
  }

  .van-picker__title {
    max-width: 50%;
    font-weight: 500;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
  }

  .van-picker__columns {
    display: flex;
    position: relative;
    cursor: grab;
  }

  .van-picker-column {
    flex: 1;
    overflow: hidden;
  }

  .van-picker-column__wrapper {
    transition-timing-function: cubic-bezier(0.23, 1, 0.68, 1);
  }

  .van-picker-column__item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 5px;
    color: #323233;
    font-size: 16px;
    line-height: 44px;
    text-align: center;
  }

  .van-picker-column__item--disabled {
    color: #c8c9cc;
    cursor: not-allowed;
  }

  /* 修复React版本的选择器样式 */
  .van-picker__columns select {
    width: 100%;
    height: 44px;
    border: none;
    outline: none;
    font-size: 16px;
    text-align: center;
    background: transparent;
    color: #323233;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
  }

  .van-picker__columns select:focus {
    outline: none;
    box-shadow: none;
  }

  /* 隐藏select的下拉箭头 */
  .van-picker__columns select::-ms-expand {
    display: none;
  }

  /* ActionSheet样式修复 */
  .van-action-sheet__item {
    border-bottom: 1px solid #ebedf0;
  }

  .van-action-sheet__item:last-child {
    border-bottom: none;
  }

  .van-action-sheet__item:hover {
    background-color: #f2f3f5;
  }

  .van-action-sheet__cancel {
    border-top: 8px solid #f7f8fa;
  }

  /* 新的简单Modal样式 */
  .simple-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
  }

  .simple-modal-content {
    background: white;
    border-radius: 12px;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  }

  .simple-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
  }

  .simple-modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .simple-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #999;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .simple-modal-close:hover {
    color: #666;
  }

  .simple-modal-body {
    padding: 20px;
  }

  /* 公历农历选择器样式 */
  .calendar-type-selector {
    min-width: 300px;
  }

  .calendar-type-button {
    display: block;
    width: 100%;
    padding: 16px;
    margin-bottom: 12px;
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    cursor: pointer;
    text-align: left;
    transition: all 0.2s;
  }

  .calendar-type-button:hover {
    border-color: #1989fa;
    background-color: #f7f8fa;
  }

  .calendar-type-button .button-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    margin-bottom: 4px;
  }

  .calendar-type-button .button-desc {
    font-size: 14px;
    color: #969799;
    line-height: 1.4;
  }

  .cancel-button {
    width: 100%;
    padding: 14px;
    background: #f7f8fa;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    color: #323233;
    cursor: pointer;
    margin-top: 8px;
  }

  .cancel-button:hover {
    background: #ebedf0;
  }

  /* 日期时间选择器样式 */
  .datetime-selector {
    min-width: 350px;
  }

  .datetime-selectors {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 20px;
  }

  .selector-group {
    display: flex;
    flex-direction: column;
  }

  .selector-group label {
    font-size: 14px;
    color: #646566;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .selector-group select {
    padding: 12px;
    border: 1px solid #ebedf0;
    border-radius: 6px;
    font-size: 16px;
    background: white;
    cursor: pointer;
  }

  .selector-group select:focus {
    outline: none;
    border-color: #1989fa;
  }

  .datetime-actions {
    display: flex;
    gap: 12px;
  }

  .datetime-actions button {
    flex: 1;
    padding: 14px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
  }

  .confirm-button {
    background: #1989fa;
    color: white;
  }

  .confirm-button:hover {
    background: #1976d2;
  }

  /* 地区选择器样式 */
  .area-selector {
    min-width: 350px;
  }

  .area-selectors {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
  }

  .area-actions {
    display: flex;
    gap: 12px;
  }

  .area-actions button {
    flex: 1;
    padding: 14px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
  }
}
