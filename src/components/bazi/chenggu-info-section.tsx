import type { MingZhuBaZiChengGuXinXi } from '../../types/bazi';
import { SectionCard } from './section-card';
import { InfoItem, ParagraphText } from './info-item';
import { Scale } from 'lucide-react';

interface ChengGuInfoSectionProps {
  data: MingZhuBaZiChengGuXinXi;
}

export function ChengGuInfoSection({ data }: ChengGuInfoSectionProps) {
  return (
    <SectionCard title="命主八字称骨信息" icon={Scale}>
      <dl className="divide-y divide-border/50">
        <InfoItem label="称骨重量" value={data.称骨重量} />
        <InfoItem label="命数" value={data.称骨重量_命数} />
        <InfoItem label="歌诀" value={<ParagraphText text={data.称骨重量_歌诀} />} />
        <InfoItem label="歌诀释义" value={<ParagraphText text={data.称骨重量_歌诀释义} />} />
        <InfoItem label="命运详解" value={<ParagraphText text={data.称骨重量_命运详解} />} />
      </dl>
    </SectionCard>
  );
} 