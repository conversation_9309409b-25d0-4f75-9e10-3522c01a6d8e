import { Order, User, Product, ProductLink, Commission } from '../models/index.js';
import sequelize from '../config/database.js';
import { Op } from 'sequelize';

// 时间生成函数
function createDate(hoursAgo) {
  const date = new Date();
  date.setHours(date.getHours() - hoursAgo);
  return date;
}

async function generateTestOrders() {
  const t = await sequelize.transaction();

  try {
    // 先删除相关的佣金记录
    await Commission.destroy({
      where: {
        orderId: {
          [Op.like]: 'TEST%'
        }
      }
    }, { transaction: t });

    // 清理现有测试数据
    await Order.destroy({
      where: {
        id: {
          [Op.like]: 'TEST%'
        }
      }
    }, { transaction: t });

    // 创建或获取管理员用户
    let admin = await User.findOne({
      where: { username: 'admin' }
    });

    if (!admin) {
      admin = await User.create({
        username: 'admin',
        password: '123456',
        name: '系统管理员',
        phone: '13900139000',
        role: 'admin',
        level: 0,
        status: 'approved',
        commissionRate: 0
      }, { transaction: t });

      console.log('创建管理员用户成功');
    }

    // 创建测试代理用户
    let agent = await User.findOne({
      where: { username: 'test_agent' }
    });

    if (!agent) {
      agent = await User.create({
        username: 'test_agent',
        password: '123456',
        name: '测试代理',
        phone: '13800138000',
        role: 'user',
        level: 2,
        status: 'approved',
        commissionRate: 25
      }, { transaction: t });

      console.log('创建测试代理用户成功');
    }

    // 创建测试产品
    let product = await Product.findOne({
      where: { title: '测试产品' }
    });

    if (!product) {
      product = await Product.create({
        title: '测试产品',
        description: '这是一个用于测试的产品',
        price: 100,
        status: 'active'
      }, { transaction: t });

      console.log('创建测试产品成功');
    }

    // 创建测试产品链接
    let productLink = await ProductLink.findOne({
      where: { agentId: agent.id }
    });

    if (!productLink) {
      productLink = await ProductLink.create({
        code: 'TEST_LINK',
        productId: product.id,
        agentId: agent.id,
        commissionRate: 25,
        status: 'active'
      }, { transaction: t });

      console.log('创建测试产品链接成功');
    }

    // 生成今天的订单
    const todayOrders = [
      {
        id: 'TEST_TODAY_1',
        amount: 100,
        status: 'paid',
        productId: product.id,
        productLinkId: productLink.id,
        agentId: agent.id,
        parentAgentId: agent.parentAgentId,
        agentCommission: 20,
        parentAgentCommission: 5,
        adminCommission: 75,
        createdAt: createDate(2),
        paidAt: createDate(2)
      },
      {
        id: 'TEST_TODAY_2',
        amount: 150,
        status: 'paid',
        productId: product.id,
        productLinkId: productLink.id,
        agentId: agent.id,
        parentAgentId: agent.parentAgentId,
        agentCommission: 30,
        parentAgentCommission: 7.5,
        adminCommission: 112.5,
        createdAt: createDate(2),
        paidAt: createDate(2)
      },
      {
        id: 'TEST_TODAY_3',
        amount: 200,
        status: 'pending',
        productId: product.id,
        productLinkId: productLink.id,
        agentId: agent.id,
        parentAgentId: agent.parentAgentId,
        agentCommission: 40,
        parentAgentCommission: 10,
        adminCommission: 150,
        createdAt: createDate(2)
      }
    ];

    // 生成昨天的订单
    const yesterdayOrders = [
      {
        id: 'TEST_YESTERDAY_1',
        amount: 120,
        status: 'paid',
        productId: product.id,
        productLinkId: productLink.id,
        agentId: agent.id,
        parentAgentId: agent.parentAgentId,
        agentCommission: 24,
        parentAgentCommission: 6,
        adminCommission: 90,
        createdAt: createDate(26),
        paidAt: createDate(26)
      },
      {
        id: 'TEST_YESTERDAY_2',
        amount: 180,
        status: 'paid',
        productId: product.id,
        productLinkId: productLink.id,
        agentId: agent.id,
        parentAgentId: agent.parentAgentId,
        agentCommission: 36,
        parentAgentCommission: 9,
        adminCommission: 135,
        createdAt: createDate(26),
        paidAt: createDate(26)
      }
    ];

    // 生成更早的订单
    const earlierOrders = [
      {
        id: 'TEST_EARLIER_1',
        amount: 90,
        status: 'paid',
        productId: product.id,
        productLinkId: productLink.id,
        agentId: agent.id,
        parentAgentId: agent.parentAgentId,
        agentCommission: 18,
        parentAgentCommission: 4.5,
        adminCommission: 67.5,
        createdAt: createDate(50),
        paidAt: createDate(50)
      },
      {
        id: 'TEST_EARLIER_2',
        amount: 160,
        status: 'paid',
        productId: product.id,
        productLinkId: productLink.id,
        agentId: agent.id,
        parentAgentId: agent.parentAgentId,
        agentCommission: 32,
        parentAgentCommission: 8,
        adminCommission: 120,
        createdAt: createDate(50),
        paidAt: createDate(50)
      }
    ];

    // 批量创建订单
    const orders = await Order.bulkCreate([
      ...todayOrders,
      ...yesterdayOrders,
      ...earlierOrders
    ], { transaction: t });

    // 为已支付的订单创建佣金记录
    const commissions = [];
    for (const order of orders) {
      if (order.status === 'paid') {
        // 代理商佣金
        if (order.agentCommission > 0) {
          commissions.push({
            orderId: order.id,
            agentId: order.agentId,
            amount: order.agentCommission,
            type: 'agent',
            status: 'pending',
            createdAt: order.paidAt
          });
        }
        
        // 上级代理商佣金
        if (order.parentAgentCommission > 0 && order.parentAgentId) {
          commissions.push({
            orderId: order.id,
            agentId: order.parentAgentId,
            amount: order.parentAgentCommission,
            type: 'parent_agent',
            status: 'pending',
            createdAt: order.paidAt
          });
        }
        
        // 管理员佣金
        if (order.adminCommission > 0) {
          commissions.push({
            orderId: order.id,
            agentId: admin.id,
            amount: order.adminCommission,
            type: 'admin',
            status: 'pending',
            createdAt: order.paidAt
          });
        }
      }
    }

    // 批量创建佣金记录
    await Commission.bulkCreate(commissions, { transaction: t });

    await t.commit();
    console.log('测试数据生成成功！');
    console.log('今日订单:', todayOrders.length, '笔');
    console.log('昨日订单:', yesterdayOrders.length, '笔');
    console.log('更早订单:', earlierOrders.length, '笔');
    console.log('佣金记录:', commissions.length, '笔');

  } catch (error) {
    await t.rollback();
    console.error('生成测试数据失败:', error);
  } finally {
    process.exit(0);
  }
}

// 执行生成函数
generateTestOrders();
