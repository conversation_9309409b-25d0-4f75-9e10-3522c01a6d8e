import express from 'express';
import { auth } from '../middleware/auth.js';
import { User } from '../models/index.js';
import { Op } from 'sequelize';
import { getCommissionStats } from '../controllers/userController.js';

const router = express.Router();

// 获取下级代理列表
router.get('/sub-agents', auth, async (req, res) => {
  try {
    const { page = 1, pageSize = 10 } = req.query;
    const offset = (page - 1) * pageSize;

    console.log('获取下级代理列表:', { userId: req.user.id, page, pageSize });

    // 查询当前用户的下级代理
    const { count, rows } = await User.findAndCountAll({
      where: {
        parentAgentId: req.user.id,
        level: 2
      },
      attributes: ['id', 'username', 'name', 'phone', 'status', 'createdAt', 'commissionRate'],
      order: [['createdAt', 'DESC']],
      offset,
      limit: parseInt(pageSize)
    });

    console.log('查询结果:', { total: count, agentsCount: rows.length });

    res.json({
      total: count,
      agents: rows,
      currentPage: parseInt(page),
      totalPages: Math.ceil(count / pageSize)
    });
  } catch (error) {
    console.error('获取下级代理列表失败:', error);
    res.status(500).json({ message: '获取下级代理列表失败' });
  }
});

// 审核代理
router.post('/:id/approve', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const agent = await User.findOne({
      where: {
        id,
        status: 'pending',
        [Op.or]: [
          { parentAgentId: req.user.id }, // 一级代理审核自己的下级
          { '$User.role$': 'admin' } // 管理员可以审核所有代理
        ]
      }
    });

    if (!agent) {
      return res.status(404).json({ message: '代理不存在或已审核' });
    }

    await agent.update({ 
      status: 'approved',
      approvedAt: new Date(),
      approvedBy: req.user.id
    });
    
    res.json({ message: '审核通过成功' });
  } catch (error) {
    console.error('审核代理失败:', error);
    res.status(500).json({ message: '审核失败' });
  }
});

// 拒绝代理
router.post('/:id/reject', auth, async (req, res) => {
  try {
    const { id } = req.params;
    const agent = await User.findOne({
      where: {
        id,
        status: 'pending',
        [Op.or]: [
          { parentAgentId: req.user.id }, // 一级代理拒绝自己的下级
          { '$User.role$': 'admin' } // 管理员可以拒绝所有代理
        ]
      }
    });

    if (!agent) {
      return res.status(404).json({ message: '代理不存在或已审核' });
    }

    await agent.update({ 
      status: 'rejected',
      rejectedAt: new Date(),
      rejectedBy: req.user.id
    });
    
    res.json({ message: '已拒绝该代理' });
  } catch (error) {
    console.error('拒绝代理失败:', error);
    res.status(500).json({ message: '操作失败' });
  }
});

// 获取佣金统计
router.get('/commission-stats', auth, getCommissionStats);

export default router;
