import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { config } from '../config';
import { Spin, Tooltip, Badge, message, Rate, Carousel, Image } from 'antd';
import { 
  PlayCircleOutlined, 
  SafetyCertificateOutlined, 
  HeartOutlined, 
  RocketOutlined,
  StarFilled,
  CheckCircleOutlined,
  LikeOutlined,
  LeftOutlined,
  RightOutlined,
  GlobalOutlined,
  TeamOutlined,
  BookOutlined,
  EnvironmentOutlined,
  CustomerServiceOutlined,
  StarOutlined,
  CalendarOutlined,
  UserOutlined
} from '@ant-design/icons';
import BaziForm from '../components/BaziForm';
import { BaZiQueryFormValues, BaZiReportData } from '../types/bazi';

interface Product {
  id: number;
  title: string;
  description: string;
  price: string | number;
}

interface ProductImage {
  id: number;
  productId: number;
  url: string;
  isPrimary: boolean;
  order: number;
}

interface ProductLinkData {
  id: number;
  code: string;
  status: string;
  product: Product;
  linkAgent: {
    name: string;
  };
}

// 儿童纪录片的特色标签和信息
const childrenDocumentaryFeatures = [
  { icon: <PlayCircleOutlined />, text: "高清纪录影像" },
  { icon: <SafetyCertificateOutlined />, text: "适合儿童观看" },
  { icon: <HeartOutlined />, text: "寓教于乐" },
  { icon: <RocketOutlined />, text: "开拓视野" }
];

// Deepseek AI大模型课程的特色标签和信息
const aiCourseFeatures = [
  { icon: <GlobalOutlined />, text: "前沿AI技术" },
  { icon: <BookOutlined />, text: "系统化学习" },
  { icon: <TeamOutlined />, text: "实战案例" },
  { icon: <RocketOutlined />, text: "就业加速" }
];

// 儿童纪录片的评论数据
const childrenDocumentaryReviews = [
  { name: "王先生", rating: 5, comment: "纪录片画面精美，内容丰富，孩子看得非常入迷，学到了很多知识！" },
  { name: "李女士", rating: 5, comment: "这套儿童纪录片系列太棒了，既有趣又有教育意义，强烈推荐给有孩子的家庭！" },
  { name: "张同学", rating: 4, comment: "作为小学老师，我经常在课堂上播放这些纪录片，学生们都很喜欢！" }
];

// Deepseek AI大模型课程的评论数据
const aiCourseReviews = [
  { name: "王先生", rating: 5, comment: "Deepseek AI大模型课程内容丰富，讲解清晰，学完后我成功开发了自己的AI应用！" },
  { name: "李女士", rating: 5, comment: "这套AI课程系列太棒了，从零基础到实战应用，让我快速掌握了大模型技术，强烈推荐！" },
  { name: "前端工程师", rating: 4, comment: "作为一名程序员，这门课程帮我解决了很多实际项目中的AI应用问题，性价比超高！" }
];

// 八字命理的特色标签和信息
const baziFeatures = [
  { icon: <StarOutlined />, text: "专业命理分析" },
  { icon: <CalendarOutlined />, text: "精准八字排盘" },
  { icon: <UserOutlined />, text: "个性化解读" },
  { icon: <BookOutlined />, text: "详细运势报告" }
];

// 八字命理的评论数据
const baziReviews = [
  { name: "李女士", rating: 5, comment: "八字分析非常准确，对我的性格和运势都分析得很到位，给了我很多人生指导！" },
  { name: "张先生", rating: 5, comment: "专业的命理分析，报告内容详细全面，让我对自己有了更深的了解，值得推荐！" },
  { name: "王女士", rating: 4, comment: "八字命盘分析很专业，特别是对事业和感情的建议很实用，帮助我做了很多重要决定！" }
];

const ProductDetail: React.FC = () => {
  const { code } = useParams<{ code: string }>();
  const navigate = useNavigate();
  const [data, setData] = useState<ProductLinkData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [paymentLoading, setPaymentLoading] = useState(false);
  const [showTestimonials, setShowTestimonials] = useState(false);
  const [productImages, setProductImages] = useState<ProductImage[]>([]);
  const [loadingImages, setLoadingImages] = useState(false);
  
  // 八字相关状态
  const [baziStep, setBaziStep] = useState<'form' | 'report'>('form');
  const [baziReportData, setBaziReportData] = useState<BaZiReportData | null>(null);

  useEffect(() => {
    fetchProductData();
    
    // 动画效果：2秒后显示用户评价
    const timer = setTimeout(() => {
      setShowTestimonials(true);
    }, 2000);
    
    return () => clearTimeout(timer);
  }, [code]);

  const fetchProductData = async () => {
    try {
      const response = await fetch(`${config.apiUrl}/api/product-links/${code}`);
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || '获取产品信息失败');
      }
      const data = await response.json();
      setData(data);
      
      // 获取产品图片
      if (data && data.product && data.product.id) {
        fetchProductImages(data.product.id);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取产品信息失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const fetchProductImages = async (productId: number) => {
    setLoadingImages(true);
    try {
      // 从localStorage获取token
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${config.apiUrl}/api/product-images/product/${productId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('获取产品图片失败');
      }
      const images = await response.json();
      setProductImages(images);
    } catch (err) {
      console.error('获取产品图片失败:', err);
      // 不显示错误消息，因为这不是关键功能
    } finally {
      setLoadingImages(false);
    }
  };

  const handlePurchase = async () => {
    if (!data) return;
    
    setPaymentLoading(true);
    try {
      console.log('开始创建订单...');
      
      // 生成或获取设备ID
      let deviceId = localStorage.getItem('device_id');
      if (!deviceId) {
        deviceId = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
        localStorage.setItem('device_id', deviceId);
      }
      
      const response = await fetch(`${config.apiUrl}/api/orders/purchase/${code}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': window.navigator.userAgent
        },
        body: JSON.stringify({
          deviceId: deviceId
        })
      });

      console.log('订单创建响应:', response.status);
      const result = await response.json();
      console.log('订单创建结果:', result);

      if (!response.ok) {
        throw new Error(result.message || '创建订单失败');
      }

      // 处理支付链接
      if (result.paymentUrl) {
        console.log('获取到支付链接，准备跳转');
        window.location.href = result.paymentUrl;
      } else if (result.paymentHtml) {
        console.log('准备处理支付表单');
        
        if (result.paymentType === 'wap') {
          // 移动端：直接从HTML中提取支付URL并跳转
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = result.paymentHtml;
          const form = tempDiv.querySelector('form');
          if (form) {
            window.location.href = form.action;
          } else {
            throw new Error('无法获取支付链接');
          }
        } else {
          // PC端：使用表单提交
          const div = document.createElement('div');
          div.innerHTML = result.paymentHtml;
          document.body.appendChild(div);

          const form = div.querySelector('form');
          if (form) {
            console.log('找到支付表单，准备提交');
            form.submit();
          } else {
            throw new Error('支付表单无效');
          }

          // 3秒后删除临时表单
          setTimeout(() => {
            document.body.removeChild(div);
          }, 3000);
        }
      } else {
        throw new Error('未获取到支付信息');
      }
    } catch (err) {
      console.error('订单创建失败:', err);
      setError(err instanceof Error ? err.message : '创建订单失败，请重试');
    } finally {
      setPaymentLoading(false);
    }
  };

  // 判断是否为AI课程的函数
  const isAIProduct = (title?: string) => {
    if (!title) return false;
    return title.includes('AI') || title.includes('大模型') || title.includes('Deepseek');
  };

  // 判断是否为八字产品的函数
  const isBaziProduct = (title?: string) => {
    if (!title) return false;
    return title.includes('八字') || title.includes('命理') || title.includes('命盘') || title.includes('算命');
  };



  if (loading) {
    // 判断是否为AI课程，从网址或其他信息判断
    const isAILoading = code ? code.includes('ai') || code.includes('deepseek') : false;
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="text-center">
          <Spin size="large">
            <div className="p-8 bg-white rounded-lg shadow-md">
              {isAILoading ? (
                <>
                  <p className="mb-4 text-blue-500">正在加载精彩课程...</p>
                  <p className="text-blue-500 animate-bounce">前沿AI知识马上就来~</p>
                </>
              ) : (
                <>
                  <p className="mb-4 text-blue-500">正在加载精彩内容...</p>
                  <p className="text-blue-500 animate-bounce">小朋友请稍等哦~</p>
                </>
              )}
            </div>
          </Spin>
        </div>
      </div>
    );
  }

  if (error) {
    // 判断是否为AI课程，从网址或其他信息判断
    const isAIError = code ? code.includes('ai') || code.includes('deepseek') : false;
    
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="text-center p-8 bg-white rounded-lg shadow-xl">
          <div className="text-red-600 mb-4 text-xl">
            {isAIError ? '加载AI课程失败' : '糟糕！出了点小问题'}
          </div>
          <p className="mb-4">
            {isAIError ? 
              '抱歉，无法加载AI课程信息。请稍后再试。' : 
              error
            }
          </p>
          <button 
            onClick={() => navigate('/')}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="text-gray-600 text-center p-8 bg-white rounded-lg shadow-lg">
          <div className="text-2xl mb-4">哎呀！找不到这个内容</div>
          <p className="mb-4">我们的小精灵可能把它藏起来了</p>
          <button 
            onClick={() => navigate('/products')}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            去看看其他精彩内容
          </button>
        </div>
      </div>
    );
  }

  const formattedPrice = typeof data.product.price === 'string' 
    ? parseFloat(data.product.price).toFixed(2)
    : data.product.price.toFixed(2);

  // 将描述分割成段落，提高可读性
  const descriptionParagraphs = data.product.description.split('\n');
  
  // 判断产品类型
  const isAICourse = isAIProduct(data.product.title);
  const isChildrenDocumentary = data.product.title.includes('儿童') || data.product.title.includes('纪录片');
  const isBaziService = isBaziProduct(data.product.title);

  // 如果是八字产品，渲染八字查询界面
  if (isBaziService) {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5' }}>
        {baziStep === 'form' && (
          <BaziForm
            productCode={code}
            productInfo={{
              name: data.product.title,
              price: typeof data.product.price === 'string' 
                ? parseFloat(data.product.price) 
                : data.product.price,
              description: data.product.description
            }}
          />
        )}
        
        {baziStep === 'report' && baziReportData && (
          <div className="min-h-screen bg-background text-foreground flex flex-col items-center justify-center p-4">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-primary mb-4">八字分析完成！</h1>
              <p className="text-muted-foreground mb-6">正在为您生成详细报告...</p>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        {/* 精美标题区域 */}
        <div className="text-center mb-12 relative">
          <div className="inline-block animate-float">
            <Badge.Ribbon 
              text={isAICourse ? "AI精品课程" : isBaziService ? "专业命理服务" : "儿童精品纪录片"} 
              color={isAICourse ? "purple" : isBaziService ? "gold" : "blue"}
            >
              <h1 className="text-4xl md:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 mb-4 px-8 py-2">
                {data.product.title}
              </h1>
            </Badge.Ribbon>
          </div>
          
          <div className="flex justify-center mb-4">
            <Rate disabled defaultValue={5} />
            <span className="ml-2 text-yellow-500">5.0 评分</span>
          </div>
          
          <div className="mb-8 flex justify-center space-x-6">
            {isAICourse ? 
              aiCourseFeatures.map((feature, index) => (
                <Tooltip key={index} title={feature.text}>
                  <div className="text-center flex flex-col items-center transition-transform hover:scale-110">
                    <div className="text-blue-500 text-2xl mb-1">
                      {feature.icon}
                    </div>
                    <span className="text-xs text-gray-600">{feature.text}</span>
                  </div>
                </Tooltip>
              ))
            : isBaziService ?
              baziFeatures.map((feature, index) => (
                <Tooltip key={index} title={feature.text}>
                  <div className="text-center flex flex-col items-center transition-transform hover:scale-110">
                    <div className="text-blue-500 text-2xl mb-1">
                      {feature.icon}
                    </div>
                    <span className="text-xs text-gray-600">{feature.text}</span>
                  </div>
                </Tooltip>
              ))
            : 
              childrenDocumentaryFeatures.map((feature, index) => (
                <Tooltip key={index} title={feature.text}>
                  <div className="text-center flex flex-col items-center transition-transform hover:scale-110">
                    <div className="text-blue-500 text-2xl mb-1">
                      {feature.icon}
                    </div>
                    <span className="text-xs text-gray-600">{feature.text}</span>
                  </div>
                </Tooltip>
              ))
            }
          </div>
        </div>
        
        {/* 内容展示区 */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-12">
          <div className="md:flex">
            {/* 左侧图片区 */}
            <div className="md:w-1/2 relative overflow-hidden">
              {loadingImages ? (
                <div className="aspect-w-16 aspect-h-9 bg-blue-100 flex items-center justify-center">
                  <Spin />
                </div>
              ) : productImages.length > 0 ? (
                <div className="aspect-w-16 aspect-h-9">
                  <Image 
                    src={productImages[0].url.startsWith('http') ? productImages[0].url : `${config.apiUrl}${productImages[0].url}`} 
                    alt="产品图片" 
                    className="object-cover h-full w-full"
                    fallback="https://img.freepik.com/free-vector/hand-drawn-children-s-day-background_23-2149375747.jpg"
                  />
                </div>
              ) : (
                <div className="aspect-w-16 aspect-h-9 bg-blue-100 flex items-center justify-center">
                  <img 
                    src={isAICourse 
                      ? "https://img.freepik.com/premium-photo/humanoid-robot-profile-view-emphasizing-integration-robotics-ai-humanlike-machines-futuristic-design_600978-20384.jpg?w=2000"
                      : isBaziService 
                        ? "https://img.freepik.com/free-photo/chinese-traditional-pattern-background_1340-23166.jpg"
                        : "https://img.freepik.com/free-vector/hand-drawn-children-s-day-background_23-2149375747.jpg"
                    } 
                    alt={isAICourse ? "AI课程图片" : isBaziService ? "八字命理图片" : "儿童纪录片图片"} 
                    className="object-cover h-full w-full transition-transform hover:scale-105"
                  />
                </div>
              )}
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent flex items-end">
                <div className="p-4 text-white">
                  <PlayCircleOutlined className="text-4xl animate-pulse" />
                  <span className="ml-2">
                    {isAICourse ? "高质量课程 | 实战案例" : isBaziService ? "专业命理 | 精准分析" : "精彩内容 | 寓教于乐"}
                  </span>
                </div>
              </div>
            </div>
            
            {/* 右侧内容区 */}
            <div className="md:w-1/2 p-8">
              <div className="prose max-w-none mb-6">
                {descriptionParagraphs.map((paragraph, i) => (
                  <p key={i} className="text-gray-700 mb-4 leading-relaxed">
                    {paragraph}
                  </p>
                ))}
              </div>
              
              {/* 适合人群和亮点 */}
              <div className="mb-6">
                <div className="font-semibold text-blue-700 mb-2 flex items-center">
                  <CheckCircleOutlined className="mr-2" />
                  {isAICourse 
                    ? "适合人群: 零基础学习者、职场人士、学生" 
                    : isBaziService 
                      ? "适合人群: 对命理感兴趣的成年人"
                      : "适合人群: 3-12岁儿童及家长陪伴观看"
                  }
                </div>
                <div className="font-semibold text-blue-700 flex items-center">
                  <CheckCircleOutlined className="mr-2" />
                  {isAICourse 
                    ? "内容实用，理论与实践结合，快速掌握AI应用" 
                    : isBaziService 
                      ? "专业分析，传统命理与现代解读相结合，深度解析人生运势"
                      : "内容生动有趣，知识性与趣味性并重，激发孩子探索欲"
                  }
                </div>
              </div>
              
              {/* 价格与购买按钮区 */}
              <div className="mt-8">
                <div className="flex items-baseline mb-6">
                  <span className="text-4xl font-bold text-red-600">¥{formattedPrice}</span>
                  <span className="ml-2 text-gray-500 line-through">¥{(parseFloat(formattedPrice) * 5).toFixed(2)}</span>
                  <span className="ml-3 bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">限时优惠</span>
                </div>
                
                <button
                  onClick={handlePurchase}
                  disabled={paymentLoading}
                  className={`w-full py-4 px-6 border border-transparent rounded-2xl shadow-lg text-white text-xl font-medium 
                    ${paymentLoading 
                      ? 'bg-blue-400 cursor-not-allowed' 
                      : 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 transform hover:scale-105 transition-all'
                    } focus:outline-none focus:ring-4 focus:ring-blue-300`}
                >
                  {paymentLoading ? (
                    <>
                      <Spin className="mr-2" /> 正在处理...
                    </>
                  ) : (
                    <>
                      {isAICourse ? 
                        '立即购买，开启AI学习之旅' : 
                        '立即购买，开启精彩探索之旅'
                      }
                    </>
                  )}
                </button>
                
                <div className="mt-4 text-center text-green-600">
                  <SafetyCertificateOutlined className="mr-1" /> 安全支付保障 | 
                  <RocketOutlined className="mx-1" /> 即刻{isAICourse ? '学习' : '观看'} | 
                  <HeartOutlined className="mx-1" /> {isAICourse ? '终身学习权限' : '精彩内容无限观看'}
                </div>
                
                {/* 客服按钮 */}
                <div className="mt-4 text-center">
                  <a 
                    href="https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-500 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-300"
                  >
                    <CustomerServiceOutlined className="mr-1" /> 联系客服
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 用户评价区 */}
        <div className={`bg-white rounded-xl shadow-lg p-8 mb-12 transition-all duration-1000 ${showTestimonials ? 'opacity-100 transform-none' : 'opacity-0 translate-y-10'}`}>
          <h3 className="text-2xl font-bold mb-6 text-center text-gray-800">
            <LikeOutlined className="mr-2 text-yellow-500" />
            学员们的评价
          </h3>
          
          <div className="grid md:grid-cols-3 gap-6">
            {isAICourse ? 
              aiCourseReviews.map((review, index) => (
                <div key={index} className="bg-blue-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 rounded-full bg-blue-200 flex items-center justify-center text-blue-700 font-bold">
                      {review.name.charAt(0)}
                    </div>
                    <div className="ml-3">
                      <div className="font-semibold">{review.name}</div>
                      <Rate disabled defaultValue={review.rating} />
                    </div>
                  </div>
                  <p className="text-gray-700">{review.comment}</p>
                </div>
              ))
            : 
              childrenDocumentaryReviews.map((review, index) => (
                <div key={index} className="bg-blue-50 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 rounded-full bg-blue-200 flex items-center justify-center text-blue-700 font-bold">
                      {review.name.charAt(0)}
                    </div>
                    <div className="ml-3">
                      <div className="font-semibold">{review.name}</div>
                      <Rate disabled defaultValue={review.rating} />
                    </div>
                  </div>
                  <p className="text-gray-700">{review.comment}</p>
                </div>
              ))
            }
          </div>
        </div>
        
        {/* 额外的信任元素 */}
        <div className="text-center">
          <p className="text-gray-500 mb-2">已有 <span className="font-bold text-blue-600">3983</span> 位学员选择了我们的课程</p>
          <p className="text-gray-500">
            <SafetyCertificateOutlined className="text-green-500 mr-1" /> 
            专业内容认证 | 
            <StarFilled className="text-yellow-500 mx-1" /> 
            学员推荐 | 
            <HeartOutlined className="text-red-500 mx-1" /> 
            终身学习权限
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
