{"name": "commission-platform-backend", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "nodemon src/index.js", "dev:local": "NODE_ENV=development DB_HOST=localhost DB_PORT=3306 DB_USER=root DB_PASSWORD=Noending5 DB_NAME=commission_platform4 nodemon src/index.js", "dev:prod": "NODE_ENV=production nodemon src/index.js", "start": "node src/index.js", "init-db": "node src/scripts/initDatabase.js", "seed-data": "node src/scripts/seedData.js", "reset-db": "npm run init-db && npm run seed-data"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "alipay-sdk": "^3.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.3", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "morgan": "^1.10.0", "mysql2": "^3.11.5", "nanoid": "^4.0.2", "node-fetch": "^2.7.0", "sequelize": "^6.37.5", "tailwind-merge": "^3.3.1"}, "devDependencies": {"nodemon": "^3.1.0"}}