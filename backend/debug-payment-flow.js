import sequelize from './src/config/database.js';
import Product from './src/models/Product.js';
import ProductLink from './src/models/ProductLink.js';
import User from './src/models/User.js';
import Order from './src/models/Order.js';

// 设置模型关联
Product.hasMany(ProductLink, { foreignKey: 'productId', as: 'productLinks' });
ProductLink.belongsTo(Product, { foreignKey: 'productId', as: 'product' });
ProductLink.belongsTo(User, { foreignKey: 'agentId', as: 'linkAgent' });
User.hasOne(User, { foreignKey: 'parentAgentId', as: 'parentAgent' });
Order.belongsTo(ProductLink, { foreignKey: 'productLinkId', as: 'productLink' });

async function debugPaymentFlow() {
  try {
    console.log('=== 调试支付流程 ===\n');
    
    // 1. 查看最近的订单
    console.log('1. 查看最近的订单:');
    const recentOrders = await Order.findAll({
      include: [{
        model: ProductLink,
        as: 'productLink',
        include: [{
          model: Product,
          as: 'product'
        }, {
          model: User,
          as: 'linkAgent'
        }]
      }],
      order: [['createdAt', 'DESC']],
      limit: 10
    });
    
    if (recentOrders.length > 0) {
      recentOrders.forEach(order => {
        const product = order.productLink?.product;
        const agent = order.productLink?.linkAgent;
        console.log(`订单ID: ${order.id}, 状态: ${order.status}, 金额: ${order.amount}`);
        console.log(`  产品: ${product?.title || 'N/A'} (类型: ${product?.type || 'N/A'})`);
        console.log(`  代理: ${agent?.name || 'N/A'}`);
        console.log(`  创建时间: ${order.createdAt}`);
        console.log('---');
      });
    } else {
      console.log('没有找到订单');
    }
    
    // 2. 查看八字产品的推广链接
    console.log('\n2. 八字产品的推广链接:');
    const baziProducts = await Product.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.or]: [
            { [sequelize.Sequelize.Op.like]: '%八字%' },
            { [sequelize.Sequelize.Op.like]: '%命理%' },
            { [sequelize.Sequelize.Op.like]: '%命盘%' }
          ]
        }
      },
      include: [{
        model: ProductLink,
        as: 'productLinks',
        include: [{
          model: User,
          as: 'linkAgent',
          attributes: ['id', 'name', 'level']
        }]
      }]
    });
    
    baziProducts.forEach(product => {
      console.log(`\n产品: ${product.title} (ID: ${product.id}, 类型: ${product.type}, 状态: ${product.status})`);
      if (product.productLinks && product.productLinks.length > 0) {
        product.productLinks.forEach(link => {
          console.log(`  推广链接: ${link.code} (状态: ${link.status})`);
          console.log(`  代理: ${link.linkAgent?.name} (等级: ${link.linkAgent?.level})`);
        });
      } else {
        console.log('  没有推广链接');
      }
    });
    
    // 3. 模拟支付跳转逻辑
    console.log('\n3. 模拟支付跳转逻辑:');
    
    // 模拟不同产品的跳转逻辑
    const testProducts = [
      { title: '玄易八字命盘', type: 'service' },
      { title: '专业八字命理查询服务', type: 'service' },
      { title: '儿童纪录片', type: 'physical' },
      { title: 'Deepseek AI大模型使用', type: 'physical' }
    ];
    
    testProducts.forEach(product => {
      console.log(`\n测试产品: ${product.title}`);
      console.log(`产品类型: ${product.type}`);
      
      // 判断产品类型
      const isBaziService = product.type === 'service' && 
                           (product.title.includes('八字') || product.title.includes('命理'));
      
      const isDeepseekCourse = product.title.includes('Deepseek') || 
                              product.title.includes('大模型') || 
                              product.title.includes('AI');
      
      let redirectUrl;
      let productTypeDesc;
      
      if (isBaziService) {
        redirectUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=test123`;
        productTypeDesc = '八字查询服务';
      } else if (isDeepseekCourse) {
        redirectUrl = process.env.PAID_RETURN_URL2;
        productTypeDesc = 'Deepseek AI课程';
      } else {
        redirectUrl = process.env.PAID_RETURN_URL;
        productTypeDesc = '儿童纪录片';
      }
      
      console.log(`判断结果: ${productTypeDesc}`);
      console.log(`跳转地址: ${redirectUrl}`);
      console.log(`是否八字服务: ${isBaziService}`);
    });
    
    // 4. 检查环境变量
    console.log('\n4. 环境变量检查:');
    console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
    console.log(`PAID_RETURN_URL: ${process.env.PAID_RETURN_URL}`);
    console.log(`PAID_RETURN_URL2: ${process.env.PAID_RETURN_URL2}`);
    console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
    
  } catch (error) {
    console.error('调试支付流程时出错:', error);
  } finally {
    await sequelize.close();
  }
}

debugPaymentFlow();