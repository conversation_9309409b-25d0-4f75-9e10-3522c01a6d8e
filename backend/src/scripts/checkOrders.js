import { Order, User, Product } from '../models/index.js';
import sequelize from '../config/database.js';

const checkOrders = async () => {
  try {
    console.log('开始检查订单数据...\n');

    // 查询所有订单
    const orders = await Order.findAll({
      where: {
        status: 'paid'  // 只查询已支付的订单
      }
    });

    console.log('所有订单数据：');
    for (const order of orders) {
      // 查询代理信息
      const agent = await User.findByPk(order.agentId);
      const parentAgent = order.parentAgentId ? await User.findByPk(order.parentAgentId) : null;
      const product = await Product.findByPk(order.productId);

      console.log('\n订单ID:', order.id);
      console.log('订单金额:', order.amount);
      console.log('订单状态:', order.status);
      console.log('管理员佣金:', order.adminCommission);
      console.log('代理佣金:', order.agentCommission);
      console.log('上级代理佣金:', order.parentAgentCommission);
      
      if (agent) {
        console.log('代理信息:', {
          id: agent.id,
          username: agent.username,
          level: agent.level,
          commissionRate: agent.commissionRate
        });
      }

      if (parentAgent) {
        console.log('上级代理信息:', {
          id: parentAgent.id,
          username: parentAgent.username,
          level: parentAgent.level,
          commissionRate: parentAgent.commissionRate
        });
      }

      if (product) {
        console.log('产品信息:', {
          id: product.id,
          title: product.title,
          price: product.price
        });
      }
      
      // 计算佣金总和
      const totalCommission = Number(order.adminCommission) + 
                            Number(order.agentCommission) + 
                            Number(order.parentAgentCommission);
      console.log('佣金总和:', totalCommission);
      console.log('是否等于订单金额:', Math.abs(totalCommission - order.amount) < 0.01);
    }

    // 计算管理员总佣金
    const adminTotalCommission = await Order.sum('adminCommission', {
      where: {
        status: 'paid'  // 只统计已支付的订单
      }
    });

    console.log('\n管理员总佣金:', adminTotalCommission);

  } catch (error) {
    console.error('查询失败:', error);
  } finally {
    process.exit();
  }
};

// 执行查询
console.log('开始查询数据库...');
checkOrders();
