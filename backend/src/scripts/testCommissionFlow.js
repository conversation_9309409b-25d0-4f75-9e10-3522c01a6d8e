/**
 * 佣金分配流程测试脚本
 * 
 * 此脚本测试整个佣金分配流程，包括：
 * 1. 创建测试用户（管理员、一级代理、二级代理）
 * 2. 创建测试产品
 * 3. 创建测试订单
 * 4. 计算并验证佣金分配
 */

import sequelize from '../config/database.js';
import User from '../models/User.js';
import Product from '../models/Product.js';
import Order from '../models/Order.js';
import Commission from '../models/Commission.js';

// 测试数据
const TEST_USERS = {
  admin: {
    username: 'admin_test',
    password: 'password123',
    role: 'admin',
    name: '测试管理员',
    phone: '13800000000'
  },
  agent1: {
    username: 'agent1_test',
    password: 'password123',
    role: 'user',
    name: '测试一级代理',
    phone: '13800000001'
  },
  agent2: {
    username: 'agent2_test',
    password: 'password123',
    role: 'user',
    name: '测试二级代理',
    phone: '13800000002'
  }
};

// 测试产品数据
const TEST_PRODUCT = {
  title: '测试产品',
  description: '这是一个用于测试佣金分配的产品',
  price: 100,
  baseCommissionRate: 80, // 80% 基础佣金率
  status: 'active'
};

// 代理商佣金比例设置
const AGENT1_COMMISSION_RATE = 0.8; // 一级代理获得80%的基础佣金
const AGENT2_COMMISSION_RATE = 0.5; // 二级代理获得一级代理佣金的50%

// 本地佣金计算函数
function calculateCommissions(orderAmount, baseCommissionRate, level1Rate, level2Rate = 0) {
  // 计算基础佣金
  const baseCommission = orderAmount * (baseCommissionRate / 100);
  
  // 管理员固定获得订单金额的 20%
  const adminCommission = orderAmount * 0.2;
  
  // 剩余佣金分配给代理商
  const agentTotalCommission = baseCommission - adminCommission;
  
  // 一级代理佣金
  let level1Commission = agentTotalCommission;
  
  // 二级代理佣金
  let level2Commission = 0;
  
  // 如果有二级代理，则分配佣金
  if (level2Rate > 0) {
    level2Commission = agentTotalCommission * (level2Rate / 100);
    level1Commission = agentTotalCommission - level2Commission;
  }
  
  return { adminCommission, level1Commission, level2Commission };
}

/**
 * 测试场景1：顾客直接从一级代理购买产品
 */
async function testScenario1(users, product) {
  console.log('\n===== 测试场景1：顾客直接从一级代理购买 =====');
  
  try {
    // 创建订单
    const order = await Order.create({
      customerName: '测试顾客1',
      customerPhone: '13900000001',
      amount: product.price,
      status: 'paid',
      agentId: users.agent1.id,
      parentAgentId: null, // 无二级代理
      productId: product.id
    });
    
    // 计算佣金
    const { adminCommission, level1Commission } = calculateCommissions(
      product.price, 
      product.baseCommissionRate, 
      AGENT1_COMMISSION_RATE * 100, 
      null
    );
    
    // 创建佣金记录
    await Commission.create({
      orderId: order.id,
      amount: adminCommission,
      agentId: users.admin.id
    });
    
    await Commission.create({
      orderId: order.id,
      amount: level1Commission,
      agentId: users.agent1.id
    });
    
    // 打印结果
    console.log(`订单总额: ¥${product.price}`);
    console.log(`基础佣金比例: ${product.baseCommissionRate}%`);
    console.log(`一级代理佣金比例: ${AGENT1_COMMISSION_RATE * 100}%`);
    console.log(`管理员获得佣金: ¥${adminCommission}`);
    console.log(`一级代理获得佣金: ¥${level1Commission}`);
    console.log(`佣金总和: ¥${adminCommission + level1Commission}`);
    
    // 验证佣金总和等于基础佣金
    const expectedTotal = product.price * (product.baseCommissionRate / 100);
    const actualTotal = adminCommission + level1Commission;
    
    if (Math.abs(expectedTotal - actualTotal) < 0.01) {
      console.log('✅ 测试通过: 佣金总和正确');
    } else {
      console.log(`❌ 测试失败: 佣金总和(${actualTotal})不等于基础佣金(${expectedTotal})`);
    }
    
    return order;
  } catch (error) {
    console.error('测试场景1失败:', error);
    throw error;
  }
}

/**
 * 测试场景2：顾客从二级代理购买产品
 */
async function testScenario2(users, product) {
  console.log('\n===== 测试场景2：顾客从二级代理购买 =====');
  
  try {
    // 创建订单
    const order = await Order.create({
      customerName: '测试顾客2',
      customerPhone: '13900000002',
      amount: product.price,
      status: 'paid',
      agentId: users.agent2.id,
      parentAgentId: users.agent1.id, // 一级代理作为上级代理
      productId: product.id
    });
    
    // 计算佣金
    const { adminCommission, level1Commission, level2Commission } = calculateCommissions(
      product.price, 
      product.baseCommissionRate, 
      AGENT1_COMMISSION_RATE * 100, 
      AGENT2_COMMISSION_RATE * 100
    );
    
    // 创建佣金记录
    await Commission.create({
      orderId: order.id,
      amount: adminCommission,
      agentId: users.admin.id
    });
    
    await Commission.create({
      orderId: order.id,
      amount: level1Commission,
      agentId: users.agent1.id
    });
    
    await Commission.create({
      orderId: order.id,
      amount: level2Commission,
      agentId: users.agent2.id
    });
    
    // 打印结果
    console.log(`订单总额: ¥${product.price}`);
    console.log(`基础佣金比例: ${product.baseCommissionRate}%`);
    console.log(`一级代理佣金比例: ${AGENT1_COMMISSION_RATE * 100}%`);
    console.log(`二级代理佣金比例: ${AGENT2_COMMISSION_RATE * 100}%`);
    console.log(`管理员获得佣金: ¥${adminCommission}`);
    console.log(`一级代理获得佣金: ¥${level1Commission}`);
    console.log(`二级代理获得佣金: ¥${level2Commission}`);
    console.log(`佣金总和: ¥${adminCommission + level1Commission + level2Commission}`);
    
    // 验证佣金总和等于基础佣金
    const expectedTotal = product.price * (product.baseCommissionRate / 100);
    const actualTotal = adminCommission + level1Commission + level2Commission;
    
    if (Math.abs(expectedTotal - actualTotal) < 0.01) {
      console.log('✅ 测试通过: 佣金总和正确');
    } else {
      console.log(`❌ 测试失败: 佣金总和(${actualTotal})不等于基础佣金(${expectedTotal})`);
    }
    
    return order;
  } catch (error) {
    console.error('测试场景2失败:', error);
    throw error;
  }
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('======= 开始佣金分配测试 =======');
  
  try {
    // 先清理可能存在的测试数据
    await Commission.destroy({ where: {} });
    await Order.destroy({ where: {} });
    await Product.destroy({ where: { title: TEST_PRODUCT.title } });
    await User.destroy({ 
      where: { 
        username: [
          TEST_USERS.admin.username,
          TEST_USERS.agent1.username,
          TEST_USERS.agent2.username
        ] 
      } 
    });
    
    // 创建测试用户
    const admin = await User.create(TEST_USERS.admin);
    const agent1 = await User.create(TEST_USERS.agent1);
    const agent2 = await User.create(TEST_USERS.agent2);
    
    // 设置二级代理的上级
    await agent2.update({ parentAgentId: agent1.id });
    
    const users = {
      admin,
      agent1,
      agent2
    };
    
    console.log('✅ 测试用户创建成功');
    
    // 创建测试产品
    const product = await Product.create(TEST_PRODUCT);
    console.log(`✅ 测试产品创建成功: ${product.title} (¥${product.price})`);
    
    // 运行测试场景
    await testScenario1(users, product);
    await testScenario2(users, product);
    
    console.log('\n======= 佣金分配测试完成 =======');
    
    // 清理测试数据
    await Commission.destroy({ where: {} });
    await Order.destroy({ where: {} });
    await Product.destroy({ where: { id: product.id } });
    await User.destroy({ where: { id: [admin.id, agent1.id, agent2.id] } });
    
    console.log('✅ 测试数据清理完成');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 运行测试
runTest().catch(console.error);
