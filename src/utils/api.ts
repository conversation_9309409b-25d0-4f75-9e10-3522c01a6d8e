/**
 * API请求工具函数
 * 统一处理JSON解析错误和响应格式问题
 */

export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  errors?: Array<{ param: string; msg: string }>;
}

export class ApiError extends Error {
  public status: number;
  public response?: Response;
  
  constructor(message: string, status: number, response?: Response) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.response = response;
  }
}

/**
 * 安全的API请求函数，处理JSON解析错误
 * @param url 请求URL
 * @param options fetch选项
 * @returns Promise<ApiResponse>
 */
export async function apiRequest<T = any>(
  url: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    // 检查响应的Content-Type是否为JSON
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      // 如果不是JSON响应，可能是HTML错误页面
      if (response.status === 404) {
        throw new ApiError('服务暂时不可用，请稍后重试', response.status, response);
      }
      
      const text = await response.text();
      console.error('非JSON响应:', text);
      
      // 检查是否是HTML错误页面
      if (text.trim().startsWith('<')) {
        throw new ApiError('服务器返回了错误页面，请联系管理员', response.status, response);
      }
      
      throw new ApiError('服务器响应格式错误', response.status, response);
    }

    let data: ApiResponse<T>;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('JSON解析失败:', jsonError);
      throw new ApiError('服务器响应格式错误，无法解析JSON', response.status, response);
    }

    if (!response.ok) {
      // 创建一个包含解析后数据的错误对象
      const error = new ApiError(
        data.message || `请求失败 (${response.status})`,
        response.status,
        response
      );
      // 将解析后的数据附加到错误对象上，以便调用者可以访问验证错误等信息
      (error as any).data = data;
      throw error;
    }

    return data;
  } catch (error) {
    // 处理网络错误或其他fetch错误
    if (error instanceof ApiError) {
      throw error;
    }
    
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new ApiError('网络连接失败，请检查网络设置', 0);
    }
    
    // 处理JSON解析错误的特殊情况
    if (error instanceof Error && error.message.includes('Unexpected token')) {
      throw new ApiError('服务暂时不可用，请稍后重试', 0);
    }
    
    throw new ApiError(
      error instanceof Error ? error.message : '未知错误',
      0
    );
  }
}

/**
 * POST请求的便捷方法
 */
export async function apiPost<T = any>(
  url: string, 
  data: any, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(url, {
    method: 'POST',
    body: JSON.stringify(data),
    ...options,
  });
}

/**
 * GET请求的便捷方法
 */
export async function apiGet<T = any>(
  url: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(url, {
    method: 'GET',
    ...options,
  });
}

/**
 * PUT请求的便捷方法
 */
export async function apiPut<T = any>(
  url: string, 
  data: any, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(url, {
    method: 'PUT',
    body: JSON.stringify(data),
    ...options,
  });
}

/**
 * DELETE请求的便捷方法
 */
export async function apiDelete<T = any>(
  url: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  return apiRequest<T>(url, {
    method: 'DELETE',
    ...options,
  });
}

/**
 * 处理API错误的工具函数
 * @param error 错误对象
 * @returns 用户友好的错误信息
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof ApiError) {
    return error.message;
  }
  
  if (error instanceof Error) {
    // 特殊处理常见的错误类型
    if (error.message.includes('Unexpected token')) {
      return '服务暂时不可用，请稍后重试';
    }
    if (error.message.includes('fetch')) {
      return '网络连接失败，请检查网络设置';
    }
    return error.message;
  }
  
  return '未知错误，请重试';
}