#!/bin/bash

# 修复远程服务器 node-fetch 依赖问题
REMOTE_HOST="root@************"
PASSWORD="Noending5@"

echo "🔧 修复远程服务器 node-fetch 依赖问题..."

expect << EOF
set timeout 60
spawn ssh -o StrictHostKeyChecking=no $REMOTE_HOST

expect {
    "password:" {
        send "$PASSWORD\r"
        exp_continue
    }
    "# " {
        send "cd /data/commission-platform8z/backend\r"
        expect "# "
        
        send "echo '🛑 停止现有服务...'\r"
        expect "# "
        send "pm2 stop commission-platform-api 2>/dev/null || echo '服务未运行'\r"
        expect "# "
        send "pm2 delete commission-platform-api 2>/dev/null || echo '服务不存在'\r"
        expect "# "
        
        send "echo '📦 安装 node-fetch 依赖...'\r"
        expect "# "
        send "npm install node-fetch@2.7.0 --save\r"
        expect "# " { sleep 10 }
        
        send "echo '📦 重新安装所有依赖...'\r"
        expect "# "
        send "npm install --production\r"
        expect "# " { sleep 15 }
        
        send "echo '🔍 检查 node-fetch 是否安装成功...'\r"
        expect "# "
        send "ls -la node_modules/node-fetch/ 2>/dev/null && echo 'node-fetch 安装成功' || echo 'node-fetch 安装失败'\r"
        expect "# "
        
        send "echo '🚀 重新启动服务...'\r"
        expect "# "
        send "pm2 start start-production.js --name commission-platform-api\r"
        expect "# "
        
        send "echo '⏳ 等待服务启动...'\r"
        expect "# "
        send "sleep 10\r"
        expect "# "
        
        send "echo '📊 检查服务状态...'\r"
        expect "# "
        send "pm2 status\r"
        expect "# "
        
        send "echo '🔍 查看最新日志...'\r"
        expect "# "
        send "pm2 logs commission-platform-api --lines 10 --nostream\r"
        expect "# "
        
        send "echo '🧪 测试API连接...'\r"
        expect "# "
        send "curl -f http://localhost:3005/api/auth/login -X POST -H 'Content-Type: application/json' -d '{\"username\":\"test\",\"password\":\"test\"}' 2>/dev/null && echo 'API测试成功' || echo 'API测试失败'\r"
        expect "# "
        
        send "echo '💾 保存PM2配置...'\r"
        expect "# "
        send "pm2 save\r"
        expect "# "
        
        send "echo '✅ 修复完成！'\r"
        expect "# "
        
        send "exit\r"
    }
    timeout {
        puts "连接超时"
        exit 1
    }
}
expect eof
EOF

echo ""
echo "🎉 修复脚本执行完成！"
echo ""
echo "📊 验证修复结果:"
echo "  1. 检查服务状态: ssh root@************ 'pm2 status'"
echo "  2. 查看日志: ssh root@************ 'pm2 logs commission-platform-api'"
echo "  3. 测试API: curl -f https://ye.bzcy.xyz/api/auth/login"
