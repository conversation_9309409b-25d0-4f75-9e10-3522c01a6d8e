import React from 'react';
import { clsx } from '@/utils';

interface FieldProps {
  label: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  isLink?: boolean;
}

export const Field: React.FC<FieldProps> = ({
  label,
  children,
  className,
  onClick,
  isLink = false,
}) => {
  return (
    <div
      className={clsx(
        'van-field van-cell',
        isLink && 'cursor-pointer',
        className
      )}
      onClick={onClick}
      style={{
        display: 'flex',
        alignItems: 'center',
        padding: '12px 16px',
        backgroundColor: 'white',
        borderRadius: '4px',
        boxShadow: '0 0 12px rgba(0,0,0,0.09)',
        margin: '12px 0',
      }}
    >
      <div
        className="van-cell__title"
        style={{
          flex: 'none',
          fontSize: '15px',
          fontWeight: 'bold',
          color: '#333',
          width: '80px',
        }}
      >
        {label}
      </div>
      <div
        className="van-cell__value"
        style={{
          flex: '1',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          fontSize: '15px',
          color: '#666',
        }}
      >
        {children}
        {isLink && (
          <svg
            className="w-4 h-4 ml-2 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        )}
      </div>
    </div>
  );
};
