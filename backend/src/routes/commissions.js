import express from 'express';
import { auth, adminAuth } from '../middleware/auth.js';
import { Commission, Order, User, Product } from '../models/index.js';
import sequelize from '../config/database.js';
import { Op } from 'sequelize';

const router = express.Router();

// 获取佣金列表
router.get('/', auth, async (req, res) => {
  try {
    let stats = {
      totalCommission: 0,
      availableCommission: 0,
      pendingCommission: 0,
      withdrawnCommission: 0,
      monthlyCommission: 0
    };

    // 基础查询条件
    const baseConditions = req.user.role === 'admin' 
      ? {} 
      : { agentId: req.user.id };

    // 从佣金表计算所有状态的佣金
    const commissionStats = await Commission.findOne({
      where: baseConditions,
      attributes: [
        [sequelize.fn('SUM', sequelize.col('amount')), 'totalCommission'],
        [sequelize.fn('SUM', 
          sequelize.literal("CASE WHEN status = 'pending' THEN amount ELSE 0 END")
        ), 'pendingCommission'],
        [sequelize.fn('SUM', 
          sequelize.literal("CASE WHEN status = 'available' THEN amount ELSE 0 END")
        ), 'availableCommission'],
        [sequelize.fn('SUM', 
          sequelize.literal("CASE WHEN status = 'withdrawn' THEN amount ELSE 0 END")
        ), 'withdrawnCommission']
      ],
      raw: true
    });

    // 计算当月佣金
    const currentDate = new Date();
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const monthlyStats = await Commission.findOne({
      where: {
        ...baseConditions,
        createdAt: {
          [Op.gte]: startOfMonth
        }
      },
      attributes: [
        [sequelize.fn('SUM', sequelize.col('amount')), 'monthlyCommission']
      ],
      raw: true
    });

    // 数据一致性检查（仅管理员）
    if (req.user.role === 'admin') {
      const orderTotal = await Order.sum('adminCommission', { 
        where: { status: 'paid' } 
      });
      const commissionTotal = await Commission.sum('amount');
      
      if (Math.abs(orderTotal - commissionTotal) > 0.01) { // 考虑浮点数精度，差值大于0.01才认为不一致
        console.error('数据不一致警告: ', {
          orderTotal,
          commissionTotal,
          difference: orderTotal - commissionTotal
        });
        // TODO: 发送警告邮件给管理员
      }
    }

    stats = {
      totalCommission: Number(commissionStats?.totalCommission || 0),
      availableCommission: Number(commissionStats?.availableCommission || 0),
      pendingCommission: Number(commissionStats?.pendingCommission || 0),
      withdrawnCommission: Number(commissionStats?.withdrawnCommission || 0),
      monthlyCommission: Number(monthlyStats?.monthlyCommission || 0)
    };

    res.json({ stats });
  } catch (error) {
    console.error('获取佣金统计失败:', error);
    res.status(500).json({ message: '获取佣金统计失败，请重试' });
  }
});

// 检查佣金数据一致性（仅管理员）
router.get('/check-consistency', adminAuth, async (req, res) => {
  try {
    // 获取所有已支付订单的佣金总和
    const orderTotal = await Order.sum('adminCommission', { 
      where: { status: 'paid' } 
    }) || 0;

    // 获取佣金表中所有记录的总和
    const commissionTotal = await Commission.sum('amount') || 0;

    // 检查订单状态和佣金状态的一致性
    const inconsistentCommissions = await Commission.findAll({
      include: [{
        model: Order,
        as: 'commissionOrder',
        required: true,
        where: {
          status: { [Op.ne]: 'paid' }  // 找出所有关联订单不是paid状态的佣金
        }
      }]
    });

    const result = {
      isConsistent: Math.abs(orderTotal - commissionTotal) <= 0.01,
      orderTotal: Number(orderTotal),
      commissionTotal: Number(commissionTotal),
      difference: Number((orderTotal - commissionTotal).toFixed(2)),
      inconsistentCommissions: inconsistentCommissions.map(comm => ({
        id: comm.id,
        amount: Number(comm.amount),
        status: comm.status,
        orderId: comm.orderId,
        orderStatus: comm.commissionOrder.status
      }))
    };

    res.json(result);
  } catch (error) {
    console.error('检查数据一致性失败:', error);
    res.status(500).json({ message: '检查数据一致性失败，请重试' });
  }
});

// 修复不一致的佣金记录（仅管理员）
router.post('/fix-consistency', adminAuth, async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    // 1. 找出所有已支付订单的佣金记录
    const paidOrders = await Order.findAll({
      where: { 
        status: 'paid',
        adminCommission: {
          [Op.gt]: 0  // 只查找有佣金的订单
        }
      },
      attributes: ['id', 'adminCommission', 'agentId'],
      raw: true
    });

    // 2. 检查每个订单是否有对应的佣金记录
    for (const order of paidOrders) {
      const commission = await Commission.findOne({
        where: { 
          orderId: order.id,
          status: { [Op.ne]: 'cancelled' }  // 排除已取消的佣金记录
        },
        transaction: t
      });

      if (!commission) {
        // 如果没有佣金记录，创建一个
        await Commission.create({
          orderId: order.id,
          agentId: order.agentId,
          amount: order.adminCommission,
          status: 'pending'  // 新创建的佣金记录默认为待结算状态
        }, { transaction: t });
      } else if (Number(commission.amount) !== Number(order.adminCommission)) {
        // 如果金额不一致，更新佣金记录
        await commission.update({
          amount: order.adminCommission
        }, { transaction: t });
      }
    }

    // 3. 找出所有关联订单不是paid状态的佣金记录
    const inconsistentCommissions = await Commission.findAll({
      include: [{
        model: Order,
        as: 'commissionOrder',
        required: true,
        where: {
          status: { [Op.ne]: 'paid' }
        }
      }],
      transaction: t
    });

    // 4. 将这些佣金标记为cancelled
    if (inconsistentCommissions.length > 0) {
      await Promise.all(inconsistentCommissions.map(commission => 
        commission.update({ status: 'cancelled' }, { transaction: t })
      ));
    }

    // 5. 删除没有关联订单的佣金记录
    await Commission.destroy({
      where: {
        orderId: {
          [Op.notIn]: paidOrders.map(order => order.id)
        }
      },
      transaction: t
    });

    await t.commit();
    
    res.json({ 
      message: '成功修复数据一致性',
      fixedCount: inconsistentCommissions.length
    });
  } catch (error) {
    await t.rollback();
    console.error('修复数据一致性失败:', error);
    res.status(500).json({ message: '修复数据一致性失败，请重试' });
  }
});

// 获取佣金明细列表
router.get('/details/list', auth, async (req, res) => {
  try {
    // 获取查询参数
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    const whereCondition = {
      agentId: req.user.id // 只查询当前用户的佣金
    };
    
    // 如果提供了状态参数，添加到查询条件中
    if (status && ['pending', 'available', 'withdrawn'].includes(status)) {
      whereCondition.status = status;
    }
    
    // 查询佣金记录
    const { count, rows: commissions } = await Commission.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: Order,
          as: 'order',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'title', 'price']
            }
          ]
        }
      ],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });
    
    // 格式化响应数据
    const formattedCommissions = commissions.map(commission => {
      const plainCommission = commission.get({ plain: true });
      
      // 转换佣金金额为数字
      const commissionAmount = Number(plainCommission.amount);
      
      // 计算佣金率
      let commissionRate = null;
      if (plainCommission.order && plainCommission.order.amount > 0) {
        // 佣金率 = (佣金金额 / 订单金额) * 100
        commissionRate = Math.round((commissionAmount / Number(plainCommission.order.amount)) * 100);
      }
      
      // 打印调试信息
      console.log(`佣金ID: ${plainCommission.id}, 佣金金额: ${commissionAmount}`);
      
      return {
        id: plainCommission.id,
        amount: commissionAmount, // 保留原始佣金金额（包含小数）
        commissionRate: commissionRate, // 添加佣金率字段
        status: plainCommission.status,
        createdAt: plainCommission.createdAt,
        updatedAt: plainCommission.updatedAt,
        order: plainCommission.order ? {
          id: plainCommission.order.id,
          amount: Number(plainCommission.order.amount),
          status: plainCommission.order.status,
          customerName: plainCommission.order.customerName,
          customerPhone: plainCommission.order.customerPhone,
          createdAt: plainCommission.order.createdAt,
          product: plainCommission.order.product ? {
            id: plainCommission.order.product.id,
            title: plainCommission.order.product.title,
            price: Number(plainCommission.order.product.price)
          } : null
        } : null
      };
    });
    
    res.json({
      commissions: formattedCommissions,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('获取佣金明细列表失败:', error);
    res.status(500).json({ message: '获取佣金明细列表失败，请重试' });
  }
});

// 获取单个佣金记录详情
router.get('/:id', auth, async (req, res) => {
  try {
    const commission = await Commission.findByPk(req.params.id, {
      include: [
        {
          model: Order,
          as: 'order',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'title', 'price']
            },
            {
              model: User,
              as: 'orderAgent',
              attributes: ['id', 'username', 'name']
            },
            {
              model: User,
              as: 'orderParentAgent',
              attributes: ['id', 'username', 'name']
            }
          ]
        },
        {
          model: User,
          as: 'commissionAgent',
          attributes: ['id', 'username', 'name']
        }
      ]
    });

    if (!commission) {
      return res.status(404).json({ message: '佣金记录不存在' });
    }

    // 检查权限
    if (req.user.role !== 'admin' && commission.agentId !== req.user.id) {
      return res.status(403).json({ message: '无权查看此佣金记录' });
    }

    const plainCommission = commission.get({ plain: true });
    plainCommission.amount = Number(plainCommission.amount);

    res.json(plainCommission);
  } catch (error) {
    console.error('获取佣金记录详情失败:', error);
    res.status(500).json({ message: '获取佣金记录详情失败，请重试' });
  }
});

// 创建佣金记录
router.post('/', auth, async (req, res) => {
    try {
        // TODO: 实现创建佣金记录逻辑
        res.status(201).json({ message: "创建佣金记录" });
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
});

// 更新佣金记录
router.put('/:id', adminAuth, async (req, res) => {
  try {
    const commission = await Commission.findByPk(req.params.id);
    if (!commission) {
      return res.status(404).json({ message: '佣金记录不存在' });
    }
    
    await commission.update(req.body);
    const updatedCommission = commission.get({ plain: true });
    updatedCommission.amount = Number(updatedCommission.amount);
    
    res.json(updatedCommission);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});



export default router;