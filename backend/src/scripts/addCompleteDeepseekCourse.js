import { Product, ProductImage } from '../models/index.js';

/**
 * 添加完整的Deepseek人工智能课程产品（包含图片）
 */
async function addCompleteDeepseekCourse() {
  try {
    console.log('开始创建Deepseek人工智能课程产品...');
    
    // 1. 创建产品
    const product = await Product.create({
      title: 'Deepseek AI大模型入门与实战课程',
      description: `# Deepseek AI大模型入门与实战

## 课程简介
这是一门零基础入门的AI大模型实战课程，专为想要快速掌握人工智能应用的学习者设计。通过简单易懂的讲解和丰富的实例，帮助你轻松掌握AI大模型的使用技巧。

## 你将学到
- AI大模型基础知识和工作原理
- 提示词编写和优化技巧
- AI辅助编程和内容创作
- 实用AI应用场景和案例

## 适合人群
- 零基础AI爱好者
- 想提升工作效率的职场人士
- 对AI应用感兴趣的学生
- 希望了解AI最新发展的各行业人士

## 学习方式
- 视频教学 + 实战练习
- 随堂作业 + 专人答疑
- 社群讨论 + 资源分享

无需编程基础，从零开始，轻松掌握AI大模型应用技能！`,
      price: 299.00,
      baseCommissionRate: 60,
      status: 'active'
    });
    
    console.log('Deepseek人工智能课程产品创建成功:', {
      id: product.id,
      title: product.title,
      price: product.price
    });
    
    // 2. 添加产品图片
    const images = [
      {
        url: 'https://img.freepik.com/free-photo/ai-technology-brain-background-digital-transformation-concept_53876-124672.jpg',
        isPrimary: true,
        order: 1
      },
      {
        url: 'https://img.freepik.com/free-photo/ai-nuclear-energy-background-future-innovation-disruptive-technology_53876-129783.jpg',
        isPrimary: false,
        order: 2
      },
      {
        url: 'https://img.freepik.com/free-photo/person-using-ai-tool-work_23-2150794571.jpg',
        isPrimary: false,
        order: 3
      }
    ];
    
    for (const imageData of images) {
      const productImage = await ProductImage.create({
        productId: product.id,
        url: imageData.url,
        isPrimary: imageData.isPrimary,
        order: imageData.order
      });
      
      console.log('产品图片添加成功:', {
        id: productImage.id,
        productId: productImage.productId,
        url: productImage.url,
        isPrimary: productImage.isPrimary
      });
    }
    
    return {
      product,
      imageCount: images.length
    };
  } catch (error) {
    console.error('创建Deepseek人工智能课程产品失败:', error);
    throw error;
  }
}

// 执行函数
addCompleteDeepseekCourse()
  .then(result => {
    console.log('脚本执行完成，创建了产品ID:', result.product.id, '和', result.imageCount, '张图片');
    process.exit(0);
  })
  .catch(error => {
    console.error('脚本执行失败:', error);
    process.exit(1);
  });
