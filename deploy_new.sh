#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m'

# 定义变量
APP_NAME="commission-platform"
SERVER_USER="root"
SERVER_IP="************"
DOMAIN="ye.bzcy.xyz"
LOCAL_DIR="$(pwd)"
REMOTE_FRONTEND_DIR="/data/$APP_NAME"
REMOTE_BACKEND_DIR="/root/$APP_NAME-api"
PM2_APP_NAME="commission-platform-api"

# 检查sshpass
if ! command -v sshpass &> /dev/null; then
    echo -e "${YELLOW}正在安装sshpass...${NC}"
    brew install sshpass
fi

# 远程执行命令函数
remote_exec() {
    echo -e "${GREEN}执行: $1${NC}"
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "$1"
}



# 远程复制文件函数
remote_copy() {
    echo -e "${GREEN}复制: $1 到 $2${NC}"
    if command -v rsync &> /dev/null; then
        # 使用rsync进行同步
        sshpass -p "$SERVER_PASSWORD" rsync -avz --progress -e "ssh -o StrictHostKeyChecking=no" "$1" "$SERVER_USER@$SERVER_IP:$2"
    else
        # 如果没有rsync，则使用scp
        sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no -r "$1" "$SERVER_USER@$SERVER_IP:$2"
    fi
}

# 检查端口80占用函数
# check_and_free_port_80() {
#     echo -e "${GREEN}检查端口80占用情况...${NC}"
#     PORT_80_PID=$(remote_exec "netstat -tlnp | grep ':80 ' | awk '{print \$7}' | cut -d'/' -f1 | head -n1")
    
#     if [ -n "$PORT_80_PID" ]; then
#         echo -e "${YELLOW}端口80被进程 $PORT_80_PID 占用${NC}"
#         IS_NGINX=$(remote_exec "ps -p $PORT_80_PID -o comm= | grep -c nginx")
        
#         if [ "$IS_NGINX" -eq "1" ]; then
#             echo -e "${YELLOW}该进程是Nginx，尝试正常停止...${NC}"
#             remote_sudo "systemctl stop nginx"
#         else
#             echo -e "${RED}该进程不是Nginx，尝试终止该进程...${NC}"
#             remote_sudo "kill -9 $PORT_80_PID"
#         fi
        
#         sleep 2
#         if remote_exec "netstat -tlnp | grep ':80 '" &> /dev/null; then
#             echo -e "${RED}无法释放端口80${NC}"
#             return 1
#         fi
#     fi
#     return 0
# }

# 输入服务器密码
echo -e "${GREEN}请输入服务器密码: ${NC}"
read -s SERVER_PASSWORD
echo

# 测试连接
echo -e "${GREEN}测试服务器连接...${NC}"
if ! remote_exec "echo '连接成功'"; then
    echo -e "${RED}连接失败，请检查密码${NC}"
    exit 1
fi

# 构建前端
echo -e "${GREEN}构建前端...${NC}"
cd "$LOCAL_DIR"
npm install
npm run build

# 构建后端
echo -e "${GREEN}构建后端...${NC}"
cd "$LOCAL_DIR/backend"
npm install
cp .env.production .env

# 创建远程目录
echo -e "${GREEN}创建远程目录...${NC}"
remote_exec "mkdir -p $REMOTE_FRONTEND_DIR"
remote_exec "mkdir -p $REMOTE_BACKEND_DIR"

# 上传前端文件
echo -e "${GREEN}上传前端文件...${NC}"
# remote_exec "rm -rf $REMOTE_FRONTEND_DIR/*"
# remote_exec "mkdir -p $REMOTE_FRONTEND_DIR"
remote_exec "chown -R $SERVER_USER:$SERVER_USER $REMOTE_FRONTEND_DIR"
remote_exec "chmod -R 755 $REMOTE_FRONTEND_DIR"
# 使用更可靠的方式复制前端文件
if command -v rsync &> /dev/null; then
    echo -e "${GREEN}使用rsync同步前端文件...${NC}"
    sshpass -p "$SERVER_PASSWORD" rsync -avz --delete -e "ssh -o StrictHostKeyChecking=no" "$LOCAL_DIR/dist/" "$SERVER_USER@$SERVER_IP:$REMOTE_FRONTEND_DIR/"
else
    echo -e "${GREEN}使用scp复制前端文件...${NC}"
    # 使用点号表示目录内容
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no -r "$LOCAL_DIR/dist/." "$SERVER_USER@$SERVER_IP:$REMOTE_FRONTEND_DIR/"
fi

# 上传后端文件
echo -e "${GREEN}上传后端文件...${NC}"
remote_exec "rm -rf $REMOTE_BACKEND_DIR/*"
remote_exec "mkdir -p $REMOTE_BACKEND_DIR"
remote_exec "chown -R $SERVER_USER:$SERVER_USER $REMOTE_BACKEND_DIR"
remote_exec "chmod -R 755 $REMOTE_BACKEND_DIR"

# 使用rsync或scp上传文件
# 使用更可靠的方式复制后端文件
if command -v rsync &> /dev/null; then
    echo -e "${GREEN}使用rsync同步后端文件...${NC}"
    # 同步src目录
    sshpass -p "$SERVER_PASSWORD" rsync -avz -e "ssh -o StrictHostKeyChecking=no" "$LOCAL_DIR/backend/src/" "$SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_DIR/src/"
    # 同步package.json
    sshpass -p "$SERVER_PASSWORD" rsync -avz -e "ssh -o StrictHostKeyChecking=no" "$LOCAL_DIR/backend/package.json" "$SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_DIR/"
    # 同步.env文件
    sshpass -p "$SERVER_PASSWORD" rsync -avz -e "ssh -o StrictHostKeyChecking=no" "$LOCAL_DIR/backend/.env" "$SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_DIR/"
else
    echo -e "${GREEN}使用scp复制后端文件...${NC}"
    # 复制src目录
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no -r "$LOCAL_DIR/backend/src/." "$SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_DIR/src/"
    # 复制package.json
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no "$LOCAL_DIR/backend/package.json" "$SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_DIR/"
    # 复制.env文件
    sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no "$LOCAL_DIR/backend/.env" "$SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_DIR/"
fi

# 安装后端依赖并启动服务
echo -e "${GREEN}安装后端依赖并启动服务...${NC}"
remote_exec "cd $REMOTE_BACKEND_DIR && npm install --production"
remote_exec "cd $REMOTE_BACKEND_DIR && pm2 delete $PM2_APP_NAME 2>/dev/null || true"
remote_exec "cd $REMOTE_BACKEND_DIR && pm2 start src/index.js --name $PM2_APP_NAME"
remote_exec "pm2 save"

# 上传和配置Nginx
echo -e "${GREEN}配置Nginx...${NC}"
# 创建并设置Nginx配置目录权限
remote_exec "mkdir -p /etc/nginx/conf.d"
remote_exec "chown -R $SERVER_USER:$SERVER_USER /etc/nginx/conf.d"
remote_copy "$LOCAL_DIR/nginx/commission-platform.conf" "/etc/nginx/conf.d/commission-platform.conf"

# # 检查并释放端口80
# check_and_free_port_80

# 测试Nginx配置
echo -e "${GREEN}测试Nginx配置...${NC}"
if ! remote_exec "nginx -t"; then
    echo -e "${RED}Nginx配置测试失败${NC}"
    exit 1
fi

# 重启Nginx
echo -e "${GREEN}重启Nginx...${NC}"
remote_exec "systemctl restart nginx"

# 配置SSL证书
echo -e "${GREEN}配置SSL证书...${NC}"
if remote_exec "test ! -f /etc/letsencrypt/live/$DOMAIN/fullchain.pem"; then
    echo -e "${YELLOW}未检测到SSL证书，开始申请...${NC}"
    remote_exec "certbot --nginx -d \"$DOMAIN\" --non-interactive --agree-tos --email \"admin@$DOMAIN\" --redirect"
chmod +x deploy_new.sh
./deploy_new.sh    
    if [ $? -ne 0 ]; then
        echo -e "${RED}SSL证书申请失败，将使用HTTP模式${NC}"
    else
        echo -e "${GREEN}SSL证书配置成功${NC}"
        # 设置自动续期
        remote_exec "(crontab -l 2>/dev/null; echo \"0 3 * * * /usr/bin/certbot renew --quiet\") | crontab -"
    fi
fi

echo -e "${GREEN}部署完成！${NC}"
echo -e "${GREEN}前端地址: https://$DOMAIN${NC}"
echo -e "${GREEN}后端地址: https://$DOMAIN/api${NC}"

# 显示服务状态
echo -e "\n${GREEN}服务状态：${NC}"
remote_exec "pm2 status $PM2_APP_NAME"
remote_exec "systemctl status nginx | grep Active"
