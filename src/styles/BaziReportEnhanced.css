/* BZ 项目完整样式 */

/* 基础样式 */
.page.home2 {
  background: #864a30;
  min-height: 100vh;
}

.banner {
  width: 100%;
  display: block;
}

.top-animate {
  padding: 35px 0;
  background: linear-gradient(141deg, #e87947 6%, #eb8833 52%, #e9543f 100%);
}

.compass {
  width: 200px;
  height: 200px;
  margin: 0 auto;
  position: relative;
  z-index: 100;
}

.compass .outside {
  width: 200px;
  height: 200px;
  background: url('/assets/images/bz-vip-t1-2.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -100px;
  margin-left: -100px;
}

.compass .inside {
  width: 150px;
  height: 150px;
  background: url('/assets/images/bz-vip-t1-1.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -75px;
  margin-left: -75px;
}

.compass .cover {
  width: 200px;
  height: 200px;
  background: url('/assets/images/bz-vip-t1-3.png') no-repeat;
  background-size: 100% 100%;
  position: absolute;
  z-index: 100;
  top: 50%;
  left: 50%;
  margin-top: -100px;
  margin-left: -100px;
}

.main-form {
  margin: 0 auto;
  padding: 0 16px;
  padding-bottom: 0;
}

.main-form .bg-title {
  height: 41.7px;
  background: url('/assets/images/bz-vip-t4.png') no-repeat center;
  background-size: auto 41.7px;
  position: relative;
  z-index: 200;
}

.main-form .inner {
  padding: 20px;
  background: #FEF4E0;
  border: 2px solid #E1B467;
  border-radius: 20px;
  margin-top: -21px;
}

.main-form .van-field {
  border-radius: 8px;
  margin: 12px 0;
  padding-top: 12px;
  padding-bottom: 12px;
}

.main-form .van-field::after {
  display: none;
}

.main-form .van-field:last-child {
  margin-bottom: 0;
}

.main-form .van-cell__title {
  flex: none;
  font-size: 15px;
  font-weight: bold;
  color: #3f2410;
  width: 80px;
}

.main-form .van-radio:first-child {
  margin-right: 30px;
}

.van-radio-group {
  display: flex;
  gap: 30px;
}

.main-form .submit {
  height: 68px;
  display: block;
  margin-top: 12px;
  background: url('/assets/images/bz-vip-t3.png') no-repeat;
  background-size: auto 100%;
}

.main-form .history {
  font-size: 16px;
  color: red;
  padding-top: 12px;
  text-align: center;
}

/* TopAnimate2 样式 - 完全按照 bz 项目 */
.top-animate2 .inner {
  position: relative;
}

.top-animate2 .bg {
  display: block;
  width: 100%;
}

.top-animate2 .large,
.top-animate2 .lite,
.top-animate2 .cover {
  display: block;
  position: absolute;
  top: 43%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.top-animate2 .large {
  width: 200px;
  height: 200px;
  animation: rotate0 20s linear infinite;
}

.top-animate2 .lite {
  width: 130px;
  height: 130px;
  animation: rotate1 20s linear infinite;
}

.top-animate2 .cover {
  width: 96px;
  height: 96px;
}

/* 动画定义 */
@keyframes rotate0 {
  from {
    transform: translate(-50%, -50%) rotate(0);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes rotate1 {
  from {
    transform: translate(-50%, -50%) rotate(0);
  }
  to {
    transform: translate(-50%, -50%) rotate(-360deg);
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes rotate-reverse {
  from { transform: rotate(0deg); }
  to { transform: rotate(-360deg); }
}

/* 新增样式 - 表单交互元素 */
.field-input.clickable {
  cursor: pointer;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 12px;
  color: #333;
  transition: all 0.3s ease;
  min-height: 20px;
  display: flex;
  align-items: center;
}

.field-input.clickable:hover {
  border-color: #ff6b35;
  box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.1);
}

.submit-button {
  width: 100%;
  margin-top: 20px;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
  cursor: pointer;
}

.field-group {
  margin: 12px 0;
}

.field-label {
  display: block;
  font-size: 15px;
  font-weight: bold;
  color: #3f2410;
  margin-bottom: 8px;
}

@keyframes marquee {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/* 图片区块样式 */
.block {
  margin: 0;
  padding: 0;
}

.block img {
  width: 100%;
  display: block;
}

.block.a8 {
  margin-top: 20px;
}

/* 八字报告页面增强样式 */

/* 新的简单Modal样式 */
.simple-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.simple-modal-content {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.simple-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eee;
}

.simple-modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.simple-modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.simple-modal-close:hover {
  color: #666;
}

.simple-modal-body {
  padding: 20px;
}

/* 公历农历选择器样式 */
.calendar-type-selector {
  min-width: 300px;
}

.calendar-type-button {
  display: block;
  width: 100%;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  text-align: left;
  transition: all 0.2s;
}

.calendar-type-button:hover {
  border-color: #1989fa;
  background-color: #f7f8fa;
}

.calendar-type-button .button-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.calendar-type-button .button-desc {
  font-size: 14px;
  color: #969799;
  line-height: 1.4;
}

.cancel-button {
  width: 100%;
  padding: 14px;
  background: #f7f8fa;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  color: #323233;
  cursor: pointer;
  margin-top: 8px;
}

.cancel-button:hover {
  background: #ebedf0;
}

/* 日期时间选择器样式 */
.datetime-selector {
  min-width: 350px;
}

.datetime-selectors {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.selector-group {
  display: flex;
  flex-direction: column;
}

.selector-group label {
  font-size: 14px;
  color: #646566;
  margin-bottom: 8px;
  font-weight: 500;
}

.selector-group select {
  padding: 12px;
  border: 1px solid #ebedf0;
  border-radius: 6px;
  font-size: 16px;
  background: white;
  cursor: pointer;
}

.selector-group select:focus {
  outline: none;
  border-color: #1989fa;
}

.datetime-actions {
  display: flex;
  gap: 12px;
}

.datetime-actions button {
  flex: 1;
  padding: 14px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

.confirm-button {
  background: #1989fa;
  color: white;
}

.confirm-button:hover {
  background: #1976d2;
}

/* 地区选择器样式 */
.area-selector {
  min-width: 350px;
}

.area-selectors {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.area-actions {
  display: flex;
  gap: 12px;
}

.area-actions button {
  flex: 1;
  padding: 14px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}

/* 全局动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(245, 158, 11, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 报告页面主容器 */
.bazi-report {
  animation: fadeInUp 0.8s ease-out;
}

/* 增强的卡片样式 */
.enhanced-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(245, 158, 11, 0.1);
  border-radius: 16px;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  animation: slideInLeft 0.6s ease-out;
}

.enhanced-card:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 30px rgba(0, 0, 0, 0.12),
    0 2px 6px rgba(0, 0, 0, 0.15);
  border-color: rgba(245, 158, 11, 0.2);
}

/* 标题增强 */
.enhanced-title {
  background: linear-gradient(135deg, #f59e0b, #ea580c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  letter-spacing: -0.025em;
}

/* 按钮增强 */
.enhanced-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.enhanced-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.enhanced-button:hover::before {
  left: 100%;
}

.enhanced-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 加载动画增强 */
.loading-container {
  animation: fadeInUp 0.6s ease-out;
}

.loading-icon {
  animation: float 3s ease-in-out infinite;
}

.progress-bar {
  background: linear-gradient(90deg, #f59e0b, #ea580c, #f59e0b);
  background-size: 200% 100%;
  animation: shimmer 2s linear infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 状态指示器 */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  border-radius: 50%;
  background: inherit;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

/* 内容区域动画 */
.content-section {
  animation: fadeInUp 0.8s ease-out;
  animation-fill-mode: both;
}

.content-section:nth-child(1) { animation-delay: 0.1s; }
.content-section:nth-child(2) { animation-delay: 0.2s; }
.content-section:nth-child(3) { animation-delay: 0.3s; }
.content-section:nth-child(4) { animation-delay: 0.4s; }
.content-section:nth-child(5) { animation-delay: 0.5s; }
.content-section:nth-child(6) { animation-delay: 0.6s; }

/* 响应式优化 */
@media (max-width: 768px) {
  .enhanced-card {
    border-radius: 12px;
    margin: 0 -4px;
  }
  
  .enhanced-title {
    font-size: 1.5rem;
  }
  
  .loading-container {
    padding: 1rem;
  }
}

/* 打印样式 */
@media print {
  .bazi-report {
    background: white !important;
  }
  
  .enhanced-card {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #e5e7eb !important;
    break-inside: avoid;
    margin-bottom: 1rem;
  }
  
  .enhanced-button,
  .loading-container,
  header button {
    display: none !important;
  }
  
  .content-section {
    animation: none !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .enhanced-card {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.95) 0%, rgba(17, 24, 39, 0.9) 100%);
    border-color: rgba(245, 158, 11, 0.2);
  }
  
  .enhanced-title {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* 滚动条美化 */
.bazi-report::-webkit-scrollbar {
  width: 8px;
}

.bazi-report::-webkit-scrollbar-track {
  background: rgba(245, 158, 11, 0.1);
  border-radius: 4px;
}

.bazi-report::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #f59e0b, #ea580c);
  border-radius: 4px;
}

.bazi-report::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #ea580c, #dc2626);
}

/* 特殊效果 */
.glow-effect {
  animation: pulse-glow 2s ease-in-out infinite;
}

.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* 新增动画效果 */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes sparkle {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #f59e0b, #ea580c, #f97316);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 移动端优化样式 */
@media (max-width: 768px) {
  /* 移动端容器优化 */
  .mobile-container {
    padding: 0.75rem;
    margin: 0;
  }

  /* 移动端卡片优化 */
  .enhanced-card {
    margin: 0.5rem 0;
    padding: 1rem;
    border-radius: 12px;
  }

  /* 移动端按钮优化 */
  .enhanced-button {
    min-height: 48px; /* 触摸友好的最小高度 */
    font-size: 1rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
  }

  /* 移动端标题优化 */
  .enhanced-title {
    font-size: 1.5rem !important;
    line-height: 1.3;
    margin-bottom: 0.75rem;
  }

  /* 移动端文字优化 */
  .mobile-text-sm {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .mobile-text-base {
    font-size: 1rem;
    line-height: 1.6;
  }

  /* 移动端间距优化 */
  .mobile-spacing {
    margin: 0.75rem 0;
  }

  .mobile-spacing-lg {
    margin: 1.5rem 0;
  }

  /* 移动端表单优化 */
  .ant-form-item {
    margin-bottom: 1rem;
  }

  .ant-input,
  .ant-select-selector,
  .ant-picker {
    min-height: 44px; /* 触摸友好 */
    font-size: 1rem;
  }

  /* 移动端网格优化 */
  .mobile-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .mobile-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }
}

/* 超小屏幕优化 */
@media (max-width: 480px) {
  .enhanced-card {
    padding: 0.75rem;
    margin: 0.25rem 0;
  }

  .enhanced-button {
    font-size: 0.9rem;
    padding: 0.625rem 1.25rem;
  }

  .enhanced-title {
    font-size: 1.25rem !important;
  }

  /* 超小屏幕的网格都变成单列 */
  .mobile-grid-2 {
    grid-template-columns: 1fr;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .hover-lift:hover {
    transform: none; /* 移动设备不需要悬停效果 */
  }

  .enhanced-button:hover {
    transform: none;
  }

  /* 为触摸设备添加点击效果 */
  .enhanced-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .hover-lift:active {
    transform: scale(0.98);
  }
}

/* 辅助按钮样式 */
.secondary-button {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  border: 1px solid #d1d5db;
  transition: all 0.3s ease;
}

.secondary-button:hover {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.outline-button {
  background: transparent;
  border: 2px solid #f59e0b;
  color: #f59e0b;
  transition: all 0.3s ease;
}

.outline-button:hover {
  background: #f59e0b;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}
