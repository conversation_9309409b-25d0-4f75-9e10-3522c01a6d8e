export const up = async (queryInterface, Sequelize) => {
  const tableDescription = await queryInterface.describeTable('orders');
  
  // 添加orderType字段
  if (!tableDescription.orderType) {
    await queryInterface.addColumn('orders', 'orderType', {
      type: Sequelize.ENUM('physical', 'service'),
      defaultValue: 'physical',
      allowNull: false,
      comment: 'Order type: physical for goods, service for services like bazi query'
    });
  }

  // 添加quantity字段
  if (!tableDescription.quantity) {
    await queryInterface.addColumn('orders', 'quantity', {
      type: Sequelize.INTEGER,
      defaultValue: 1,
      allowNull: false,
      comment: 'Order quantity'
    });
  }

  // 添加unitPrice字段
  if (!tableDescription.unitPrice) {
    await queryInterface.addColumn('orders', 'unitPrice', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      comment: 'Unit price of the product'
    });
  }

  // 添加totalAmount字段
  if (!tableDescription.totalAmount) {
    await queryInterface.addColumn('orders', 'totalAmount', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      comment: 'Total amount of the order'
    });
  }

  // 添加customerAddress字段
  if (!tableDescription.customerAddress) {
    await queryInterface.addColumn('orders', 'customerAddress', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Customer address'
    });
  }

  // 添加orderNumber字段
  if (!tableDescription.orderNumber) {
    await queryInterface.addColumn('orders', 'orderNumber', {
      type: Sequelize.STRING(50),
      allowNull: true,
      unique: true,
      comment: 'Human readable order number'
    });
  }

  // 更新status枚举以包含更多状态
  if (tableDescription.status) {
    await queryInterface.changeColumn('orders', 'status', {
      type: Sequelize.ENUM('pending', 'paid', 'cancelled', 'completed'),
      defaultValue: 'pending',
      allowNull: false
    });
  }
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.removeColumn('orders', 'orderType');
  await queryInterface.removeColumn('orders', 'quantity');
  await queryInterface.removeColumn('orders', 'unitPrice');
  await queryInterface.removeColumn('orders', 'totalAmount');
  await queryInterface.removeColumn('orders', 'customerAddress');
  await queryInterface.removeColumn('orders', 'orderNumber');
};