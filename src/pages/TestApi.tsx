import React, { useState } from 'react';
import { config } from '../config';
import { apiPost, getErrorMessage } from '../utils/api';

/**
 * API测试页面
 * 用于测试JSON解析错误修复效果
 */
const TestApi: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testLogin = async () => {
    setLoading(true);
    setResult('正在测试登录API...');

    try {
      const response = await apiPost(`${config.apiUrl}/api/auth/login`, {
        username: 'admin',
        password: 'admin123'
      });

      // 检查响应数据结构
      const token = response.data?.token || response.token;
      const user = response.data?.user || response.user;

      if (token && user) {
        setResult(`✅ 登录API响应正常:
Token: ${token.substring(0, 20)}...
User: ${JSON.stringify(user, null, 2)}`);
      } else {
        setResult(`⚠️ 登录API响应格式异常: ${JSON.stringify(response, null, 2)}`);
      }
    } catch (err) {
      const errorMsg = getErrorMessage(err);
      setResult(`❌ 登录API错误: ${errorMsg}`);
      console.error('登录测试错误:', err);
    } finally {
      setLoading(false);
    }
  };

  const testRegister = async () => {
    setLoading(true);
    setResult('正在测试注册API...');
    
    try {
      const response = await apiPost(`${config.apiUrl}/api/auth/register`, {
        username: 'testuser' + Date.now(),
        password: 'test123',
        name: '测试用户',
        phone: '13800138000'
      });
      
      setResult(`✅ 注册API响应正常: ${JSON.stringify(response, null, 2)}`);
    } catch (err) {
      const errorMsg = getErrorMessage(err);
      setResult(`❌ 注册API错误: ${errorMsg}`);
      console.error('注册测试错误:', err);
    } finally {
      setLoading(false);
    }
  };

  const testNonExistentApi = async () => {
    setLoading(true);
    setResult('正在测试不存在的API...');
    
    try {
      const response = await apiPost(`${config.apiUrl}/api/nonexistent`, {});
      setResult(`✅ 意外成功: ${JSON.stringify(response, null, 2)}`);
    } catch (err) {
      const errorMsg = getErrorMessage(err);
      setResult(`✅ 正确处理了404错误: ${errorMsg}`);
      console.log('404错误测试成功:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-full flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            API测试页面
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            测试JSON解析错误修复效果
          </p>
        </div>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
            <button
              onClick={testLogin}
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {loading ? '测试中...' : '测试登录API'}
            </button>
            
            <button
              onClick={testRegister}
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              {loading ? '测试中...' : '测试注册API'}
            </button>
            
            <button
              onClick={testNonExistentApi}
              disabled={loading}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
            >
              {loading ? '测试中...' : '测试404错误'}
            </button>
          </div>
          
          {result && (
            <div className="mt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">测试结果:</h3>
              <div className="bg-gray-50 rounded-md p-4">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap">{result}</pre>
              </div>
            </div>
          )}
          
          <div className="mt-6 bg-blue-50 rounded-md p-4">
            <h3 className="text-lg font-medium text-blue-900 mb-2">修复说明:</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 添加了Content-Type检查，防止HTML响应被当作JSON解析</li>
              <li>• 改进了错误处理，提供用户友好的错误信息</li>
              <li>• 创建了统一的API请求工具函数</li>
              <li>• 特殊处理了"Unexpected token"错误</li>
              <li>• 保持了验证错误的正确显示</li>
            </ul>
          </div>
          
          <div className="mt-4 bg-yellow-50 rounded-md p-4">
            <h3 className="text-lg font-medium text-yellow-900 mb-2">当前API地址:</h3>
            <p className="text-sm text-yellow-700">{config.apiUrl}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestApi;