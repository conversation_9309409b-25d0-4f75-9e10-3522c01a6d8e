import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, message, Tabs, Switch, InputNumber, Divider } from 'antd';
import { SettingOutlined, DollarOutlined, UserOutlined } from '@ant-design/icons';
import { PageContainer, PageTitle, PageDescription } from '../styles/AdminTheme';
import { config } from '../config';
import { useAuth } from '../contexts/AuthContext';

const { TabPane } = Tabs;

interface NamingConfig {
  naming_service_min_price: number;
  naming_service_max_price: number;
  naming_service_commission_rate: number;
  naming_service_enabled: boolean;
}

const SystemConfig: React.FC = () => {
  const { token } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [namingConfig, setNamingConfig] = useState<NamingConfig>({
    naming_service_min_price: 99,
    naming_service_max_price: 299,
    naming_service_commission_rate: 20,
    naming_service_enabled: true
  });

  useEffect(() => {
    fetchNamingConfig();
  }, []);

  const fetchNamingConfig = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${config.apiUrl}/api/orders/naming/config`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const configData = {
            naming_service_min_price: Number(data.data.minPrice),
            naming_service_max_price: Number(data.data.maxPrice),
            naming_service_commission_rate: Number(data.data.commissionRate),
            naming_service_enabled: data.data.isEnabled
          };
          setNamingConfig(configData);
          form.setFieldsValue(configData);
        }
      }
    } catch (error) {
      console.error('获取起名业务配置失败:', error);
      message.error('获取配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveNamingConfig = async (values: NamingConfig) => {
    try {
      setSaving(true);
      
      // 这里需要创建一个API来更新系统配置
      const response = await fetch(`${config.apiUrl}/api/system-config/naming`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          minPrice: values.naming_service_min_price,
          maxPrice: values.naming_service_max_price,
          commissionRate: values.naming_service_commission_rate,
          enabled: values.naming_service_enabled
        })
      });

      if (response.ok) {
        message.success('起名业务配置已更新');
        setNamingConfig(values);
      } else {
        throw new Error('更新配置失败');
      }
    } catch (error) {
      console.error('保存起名业务配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  return (
    <PageContainer>
      <div className="page-header">
        <PageTitle level={2}>
          <SettingOutlined className="icon" />
          系统配置
        </PageTitle>
        <PageDescription>
          管理系统全局配置，包括起名业务设置、佣金比例等
        </PageDescription>
      </div>

      <Card>
        <Tabs defaultActiveKey="naming" type="card">
          <TabPane 
            tab={
              <span>
                <DollarOutlined />
                起名业务配置
              </span>
            } 
            key="naming"
          >
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSaveNamingConfig}
              initialValues={namingConfig}
            >
              <div style={{ maxWidth: '600px' }}>
                <Divider orientation="left">基础设置</Divider>
                
                <Form.Item
                  name="naming_service_enabled"
                  label="启用起名业务"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="启用" 
                    unCheckedChildren="禁用"
                  />
                </Form.Item>

                <Divider orientation="left">价格设置</Divider>
                
                <div style={{ display: 'flex', gap: '16px' }}>
                  <Form.Item
                    name="naming_service_min_price"
                    label="最低价格（元）"
                    rules={[
                      { required: true, message: '请输入最低价格' },
                      { type: 'number', min: 1, message: '价格必须大于0' }
                    ]}
                    style={{ flex: 1 }}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={1}
                      max={9999}
                      precision={0}
                      placeholder="请输入最低价格"
                    />
                  </Form.Item>

                  <Form.Item
                    name="naming_service_max_price"
                    label="最高价格（元）"
                    rules={[
                      { required: true, message: '请输入最高价格' },
                      { type: 'number', min: 1, message: '价格必须大于0' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          const minPrice = getFieldValue('naming_service_min_price');
                          if (!value || !minPrice || value >= minPrice) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('最高价格必须大于等于最低价格'));
                        },
                      }),
                    ]}
                    style={{ flex: 1 }}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={1}
                      max={9999}
                      precision={0}
                      placeholder="请输入最高价格"
                    />
                  </Form.Item>
                </div>

                <Divider orientation="left">佣金设置</Divider>
                
                <Form.Item
                  name="naming_service_commission_rate"
                  label="佣金比例（%）"
                  rules={[
                    { required: true, message: '请输入佣金比例' },
                    { type: 'number', min: 0, max: 100, message: '佣金比例必须在0-100之间' }
                  ]}
                  extra="代理商推广起名业务可获得的佣金比例"
                >
                  <InputNumber
                    style={{ width: '200px' }}
                    min={0}
                    max={100}
                    precision={1}
                    placeholder="请输入佣金比例"
                    addonAfter="%"
                  />
                </Form.Item>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={saving}
                    size="large"
                  >
                    保存配置
                  </Button>
                </Form.Item>
              </div>
            </Form>
          </TabPane>

          <TabPane 
            tab={
              <span>
                <UserOutlined />
                其他配置
              </span>
            } 
            key="other"
          >
            <div style={{ padding: '20px', textAlign: 'center', color: '#999' }}>
              其他系统配置功能待开发...
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </PageContainer>
  );
};

export default SystemConfig;
