import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const Commission = sequelize.define('Commission', {
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'available', 'withdrawn'),
    defaultValue: 'pending'
  },
  orderId: {
    type: DataTypes.STRING(20),
    allowNull: false,
    references: {
      model: 'Orders',
      key: 'id'
    }
  },
  agentId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  }
});

export default Commission; 