import { Order, BaziOrder, Product, ProductLink, User } from '../models/index.js';
import baziService from '../services/baziService.js';
import { createCommissionsForOrder } from '../services/commissionService.js';

/**
 * 创建八字查询订单
 */
export const createBaziOrder = async (req, res) => {
  try {
    const { 
      productCode, 
      name, 
      gender, 
      calendarType, 
      birthYear, 
      birthMonth, 
      birthDay, 
      birthHour, 
      birthMinute, 
      birthProvince, 
      birthCity 
    } = req.body;

    // 验证必填字段
    if (!productCode || !name || !gender || !birthYear || !birthMonth || 
        !birthDay || !birthHour || !birthMinute || !birthProvince || !birthCity) {
      return res.status(400).json({
        success: false,
        message: '请填写完整的生辰信息'
      });
    }

    // 查找产品链接
    const productLink = await ProductLink.findOne({
      where: { code: productCode },
      include: [{
        model: Product,
        as: 'product',
        where: { type: 'service', status: 'active' }
      }, {
        model: User,
        as: 'agent'
      }]
    });

    if (!productLink) {
      return res.status(404).json({
        success: false,
        message: '产品链接不存在或产品已下架'
      });
    }

    const product = productLink.Product;
    const agent = productLink.agent;

    // 创建订单
    const order = await Order.create({
      productId: product.id,
      agentId: agent.id,
      productLinkId: productLink.id,
      customerName: name,
      customerPhone: '', // 八字查询不需要手机号
      customerAddress: `${birthProvince} ${birthCity}`, // 使用出生地作为地址
      quantity: 1,
      unitPrice: product.price,
      totalAmount: product.price,
      status: 'pending',
      orderType: 'service'
    });

    // 创建八字查询订单详情
    const baziOrder = await BaziOrder.create({
      orderId: order.id,
      name,
      gender,
      calendarType: calendarType || 'gregorian',
      birthYear,
      birthMonth,
      birthDay,
      birthHour,
      birthMinute,
      birthProvince,
      birthCity,
      reportStatus: 'pending'
    });

    // 更新产品链接点击统计
    await productLink.increment('clickCount');

    res.json({
      success: true,
      message: '订单创建成功',
      data: {
        orderId: order.id,
        orderNumber: order.orderNumber,
        totalAmount: order.totalAmount,
        baziOrderId: baziOrder.id
      }
    });

  } catch (error) {
    console.error('创建八字查询订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建订单失败，请稍后重试'
    });
  }
};

/**
 * 生成八字报告
 */
export const generateBaziReport = async (req, res) => {
  try {
    const { orderId } = req.params;

    // 查找订单和八字查询详情
    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder',
        required: true
      }]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    if (order.status !== 'paid') {
      return res.status(400).json({
        success: false,
        message: '订单尚未支付，无法生成报告'
      });
    }

    const baziOrder = order.baziOrder;
    
    if (baziOrder.reportStatus === 'generated') {
      return res.json({
        success: true,
        message: '报告已存在',
        data: {
          reportData: baziOrder.reportData
        }
      });
    }

    // 更新状态为生成中
    await baziOrder.update({ reportStatus: 'pending' });

    try {
      // 调用八字服务生成报告
      const reportData = await baziService.generateBaziReport({
        name: baziOrder.name,
        gender: baziOrder.gender,
        calendarType: baziOrder.calendarType,
        birthYear: baziOrder.birthYear,
        birthMonth: baziOrder.birthMonth,
        birthDay: baziOrder.birthDay,
        birthHour: baziOrder.birthHour,
        birthMinute: baziOrder.birthMinute,
        birthProvince: baziOrder.birthProvince,
        birthCity: baziOrder.birthCity
      });

      // 保存报告数据
      await baziOrder.update({
        reportData,
        reportStatus: 'generated',
        apiResponse: reportData // 保存完整响应用于调试
      });

      // 更新订单状态为已完成
      await order.update({ status: 'completed' });

      // 计算并分配佣金
      await createCommissionsForOrder(order.id);

      res.json({
        success: true,
        message: '报告生成成功',
        data: {
          reportData
        }
      });

    } catch (apiError) {
      console.error('八字API调用失败:', apiError);
      
      // 更新失败状态
      await baziOrder.update({
        reportStatus: 'failed',
        errorMessage: apiError.message
      });

      res.status(500).json({
        success: false,
        message: '报告生成失败，请联系客服'
      });
    }

  } catch (error) {
    console.error('生成八字报告失败:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误，请稍后重试'
    });
  }
};

/**
 * 获取八字报告
 */
export const getBaziReport = async (req, res) => {
  try {
    const { orderId } = req.params;

    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder',
        required: true
      }]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    const baziOrder = order.baziOrder;

    if (baziOrder.reportStatus !== 'generated') {
      return res.status(400).json({
        success: false,
        message: '报告尚未生成',
        data: {
          reportStatus: baziOrder.reportStatus
        }
      });
    }

    res.json({
      success: true,
      data: {
        reportData: baziOrder.reportData,
        orderInfo: {
          orderNumber: order.orderNumber,
          customerName: order.customerName,
          createdAt: order.createdAt
        }
      }
    });

  } catch (error) {
    console.error('获取八字报告失败:', error);
    res.status(500).json({
      success: false,
      message: '获取报告失败，请稍后重试'
    });
  }
};

/**
 * 获取八字查询订单列表（管理员用）
 */
export const getBaziOrders = async (req, res) => {
  try {
    const { page = 1, limit = 10, status } = req.query;
    const offset = (page - 1) * limit;

    const whereCondition = {};
    if (status) {
      whereCondition.reportStatus = status;
    }

    const { count, rows } = await BaziOrder.findAndCountAll({
      where: whereCondition,
      include: [{
        model: Order,
        include: [{
          model: Product
        }, {
          model: User,
          as: 'agent',
          attributes: ['id', 'username', 'nickname']
        }]
      }],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    res.json({
      success: true,
      data: {
        orders: rows,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(count / limit)
        }
      }
    });

  } catch (error) {
    console.error('获取八字查询订单列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订单列表失败'
    });
  }
};

/**
 * 直接生成八字报告（无需订单）
 */
export const generateBaziReportDirect = async (req, res) => {
  try {
    const { 
      xing, 
      ming, 
      sex, 
      yearType, 
      year, 
      month, 
      day, 
      hour, 
      minute, 
      address 
    } = req.body;

    // 验证必填字段
    if (!xing || !ming || !sex || !yearType || !year || !month || 
        !day || !hour || !minute || !address) {
      return res.status(400).json({
        success: false,
        message: '请填写完整的生辰信息'
      });
    }

    // 解析地址
    const addressParts = address.split('省');
    const birthProvince = addressParts[0] + '省';
    const birthCity = addressParts[1] || '';

    // 转换性别格式
    const gender = sex === '1' ? 'male' : 'female';
    
    // 转换日历类型
    const calendarType = yearType === '1' ? 'gregorian' : 'lunar';

    try {
      // 调用八字服务生成报告
      const reportData = await baziService.generateBaziReport({
        name: `${xing}${ming}`,
        gender,
        calendarType,
        birthYear: year,
        birthMonth: month,
        birthDay: day,
        birthHour: hour,
        birthMinute: minute,
        birthProvince,
        birthCity
      });

      res.json({
        success: true,
        code: 0,
        msg: '报告生成成功',
        data: reportData
      });

    } catch (apiError) {
      console.error('八字API调用失败:', apiError);
      res.status(500).json({
        success: false,
        code: -1,
        msg: '报告生成失败，请稍后重试'
      });
    }

  } catch (error) {
    console.error('直接生成八字报告失败:', error);
    res.status(500).json({
      success: false,
      code: -1,
      msg: '服务器错误，请稍后重试'
    });
  }
}; 