import sequelize from './src/config/database.js';
import Product from './src/models/Product.js';
import ProductLink from './src/models/ProductLink.js';
import User from './src/models/User.js';

// 设置模型关联
Product.hasMany(ProductLink, { foreignKey: 'productId', as: 'productLinks' });
ProductLink.belongsTo(Product, { foreignKey: 'productId', as: 'product' });
ProductLink.belongsTo(User, { foreignKey: 'agentId', as: 'linkAgent' });

async function checkProductTypes() {
  try {
    console.log('=== 检查数据库中的产品类型配置 ===\n');
    
    // 查询所有产品
    const allProducts = await Product.findAll({
      attributes: ['id', 'title', 'type', 'price', 'status'],
      order: [['id', 'ASC']]
    });
    
    console.log('所有产品列表:');
    allProducts.forEach(product => {
      console.log(`ID: ${product.id}, 标题: ${product.title}, 类型: ${product.type}, 价格: ${product.price}, 状态: ${product.status}`);
    });
    
    console.log('\n=== 八字相关产品检查 ===');
    
    // 查询八字相关产品
    const baziProducts = await Product.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.or]: [
            { [sequelize.Sequelize.Op.like]: '%八字%' },
            { [sequelize.Sequelize.Op.like]: '%命理%' },
            { [sequelize.Sequelize.Op.like]: '%命盘%' }
          ]
        }
      },
      attributes: ['id', 'title', 'type', 'price', 'status']
    });
    
    if (baziProducts.length > 0) {
      console.log('\n找到的八字相关产品:');
      baziProducts.forEach(product => {
        console.log(`ID: ${product.id}, 标题: ${product.title}, 类型: ${product.type}, 价格: ${product.price}`);
        if (product.type !== 'service') {
          console.log(`⚠️  警告: 产品 "${product.title}" 的类型是 "${product.type}"，应该是 "service"`);
        } else {
          console.log(`✅ 产品 "${product.title}" 的类型配置正确`);
        }
      });
    } else {
      console.log('❌ 没有找到八字相关产品');
    }
    
    console.log('\n=== 服务类型产品检查 ===');
    
    // 查询所有服务类型产品
    const serviceProducts = await Product.findAll({
      where: {
        type: 'service'
      },
      attributes: ['id', 'title', 'type', 'price', 'status']
    });
    
    if (serviceProducts.length > 0) {
      console.log('\n所有服务类型产品:');
      serviceProducts.forEach(product => {
        console.log(`ID: ${product.id}, 标题: ${product.title}, 类型: ${product.type}, 价格: ${product.price}`);
      });
    } else {
      console.log('❌ 没有找到服务类型产品');
    }
    
    console.log('\n=== 产品链接检查 ===');
    
    // 查询有推广链接的产品
    const productsWithLinks = await Product.findAll({
      include: [{
        model: ProductLink,
        as: 'productLinks',
        where: {
          status: 'active'
        },
        required: true,
        include: [{
          model: User,
          as: 'linkAgent',
          attributes: ['id', 'name', 'level']
        }]
      }],
      attributes: ['id', 'title', 'type', 'price']
    });
    
    if (productsWithLinks.length > 0) {
      console.log('\n有活跃推广链接的产品:');
      productsWithLinks.forEach(product => {
        console.log(`\n产品: ${product.title} (ID: ${product.id}, 类型: ${product.type})`);
        product.productLinks.forEach(link => {
          console.log(`  - 推广链接: ${link.code}, 代理: ${link.linkAgent.name} (等级: ${link.linkAgent.level})`);
        });
      });
    }
    
  } catch (error) {
    console.error('检查产品类型时出错:', error);
  } finally {
    await sequelize.close();
  }
}

checkProductTypes();