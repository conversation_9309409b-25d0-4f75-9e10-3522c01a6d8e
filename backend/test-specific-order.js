import sequelize from './src/config/database.js';
import Product from './src/models/Product.js';
import ProductLink from './src/models/ProductLink.js';
import User from './src/models/User.js';
import Order from './src/models/Order.js';

// 设置模型关联
Product.hasMany(ProductLink, { foreignKey: 'productId', as: 'productLinks' });
ProductLink.belongsTo(Product, { foreignKey: 'productId', as: 'product' });
ProductLink.belongsTo(User, { foreignKey: 'agentId', as: 'linkAgent' });
Order.belongsTo(ProductLink, { foreignKey: 'productLinkId', as: 'productLink' });

async function testSpecificOrder() {
  try {
    console.log('=== 测试具体订单的跳转逻辑 ===\n');
    
    // 查询具体的已支付订单
    const orderId = '250707185840805299';
    console.log(`1. 查询订单 ${orderId} 的详细信息:`);
    
    const order = await Order.findOne({
      where: { id: orderId },
      include: [{
        model: ProductLink,
        as: 'productLink',
        include: [{
          model: Product,
          as: 'product'
        }, {
          model: User,
          as: 'linkAgent'
        }]
      }]
    });
    
    if (!order) {
      console.log('❌ 订单不存在');
      return;
    }
    
    console.log('订单信息:');
    console.log(`  订单ID: ${order.id}`);
    console.log(`  状态: ${order.status}`);
    console.log(`  金额: ${order.totalAmount}`);
    console.log(`  支付时间: ${order.paidAt}`);
    
    if (order.productLink && order.productLink.product) {
      const product = order.productLink.product;
      console.log(`  产品标题: ${product.title}`);
      console.log(`  产品类型: ${product.type}`);
      console.log(`  产品状态: ${product.status}`);
      
      // 2. 模拟支付回调中的跳转逻辑
      console.log('\n2. 模拟支付回调跳转逻辑:');
      
      // 检查是否为八字服务
      const isBaziService = product.type === 'service' && 
                           (product.title.includes('八字') || product.title.includes('命理'));
      
      console.log(`  是否为八字服务: ${isBaziService}`);
      
      let redirectUrl;
      if (isBaziService) {
        // 八字服务跳转逻辑
        redirectUrl = `${process.env.FRONTEND_URL}/bazi-report?orderId=${order.id}`;
        console.log(`  ✅ 应该跳转到八字报告页面`);
      } else if (product.title.includes('Deepseek')) {
        // Deepseek课程跳转
        redirectUrl = process.env.PAID_RETURN_URL2;
        console.log(`  ✅ 应该跳转到Deepseek课程页面`);
      } else {
        // 其他产品跳转
        redirectUrl = process.env.PAID_RETURN_URL;
        console.log(`  ✅ 应该跳转到默认页面`);
      }
      
      console.log(`  计算出的跳转地址: ${redirectUrl}`);
      
      // 3. 检查环境变量
      console.log('\n3. 环境变量检查:');
      console.log(`  NODE_ENV: ${process.env.NODE_ENV}`);
      console.log(`  FRONTEND_URL: ${process.env.FRONTEND_URL}`);
      console.log(`  PAID_RETURN_URL: ${process.env.PAID_RETURN_URL}`);
      console.log(`  PAID_RETURN_URL2: ${process.env.PAID_RETURN_URL2}`);
      
      // 4. 分析问题
      console.log('\n4. 问题分析:');
      
      if (isBaziService && redirectUrl.includes('bazi-report')) {
        console.log('✅ 跳转逻辑正确，应该跳转到八字报告页面');
        console.log('\n🔍 可能的问题原因:');
        console.log('1. 生产环境代码版本过旧，还没有更新到最新的跳转逻辑');
        console.log('2. 生产环境服务没有重启，新代码没有生效');
        console.log('3. 生产环境的环境变量配置不正确');
        console.log('4. 前端路由配置问题，/bazi-report 路径不存在或有问题');
        
        console.log('\n🛠️  建议的解决步骤:');
        console.log('1. 检查生产环境代码是否为最新版本');
        console.log('2. 重启生产环境的后端服务');
        console.log('3. 检查生产环境的 FRONTEND_URL 配置');
        console.log('4. 测试前端 /bazi-report 页面是否可以正常访问');
      } else {
        console.log('❌ 跳转逻辑有问题');
        if (!isBaziService) {
          console.log('   产品未被识别为八字服务');
        }
      }
      
    } else {
      console.log('❌ 订单关联的产品信息不完整');
    }
    
  } catch (error) {
    console.error('测试订单时出错:', error);
  } finally {
    await sequelize.close();
  }
}

testSpecificOrder();