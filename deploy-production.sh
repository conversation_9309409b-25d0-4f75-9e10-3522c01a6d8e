#!/bin/bash

# 远程服务器部署脚本
# 目标服务器: root@************

# 配置变量
REMOTE_HOST="root@************"
REMOTE_PATH="/data/commission-platform8z"
PROJECT_NAME="commission-platform"

echo "🚀 开始远程部署到服务器: $REMOTE_HOST"

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 检查SSH连接
echo "🔗 测试SSH连接..."
ssh -o ConnectTimeout=10 $REMOTE_HOST "echo '✅ SSH连接成功'" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 无法连接到远程服务器 $REMOTE_HOST"
    echo "请确保:"
    echo "  1. 服务器地址正确"
    echo "  2. SSH密钥已配置"
    echo "  3. 网络连接正常"
    exit 1
fi

# 1. 本地构建前端
echo "🏗️ 本地构建前端..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

# 2. 创建部署包
echo "📦 创建部署包..."
DEPLOY_DIR="deploy_$(date +%Y%m%d_%H%M%S)"
mkdir -p $DEPLOY_DIR

# 复制需要的文件
cp -r dist $DEPLOY_DIR/
cp -r backend $DEPLOY_DIR/
cp nginx.conf $DEPLOY_DIR/
cp package.json $DEPLOY_DIR/

# 创建远程部署脚本
cat > $DEPLOY_DIR/remote-deploy.sh << 'EOF'
#!/bin/bash

# 远程服务器上执行的部署脚本
echo "🚀 在远程服务器上开始部署..."

PROJECT_PATH="/data/commission-platform8z"
WEB_PATH="/data/commission-platform"

# 停止现有服务
echo "📦 停止现有服务..."
pm2 stop commission-platform-api 2>/dev/null || echo "服务未运行"
pm2 delete commission-platform-api 2>/dev/null || echo "服务不存在"

# 清理端口3005
echo "🔧 清理端口3005..."
lsof -ti:3005 | xargs kill -9 2>/dev/null || echo "端口3005未被占用"

# 备份现有前端文件
echo "💾 备份现有前端文件..."
if [ -d "$WEB_PATH" ]; then
    cp -r $WEB_PATH ${WEB_PATH}.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || echo "备份失败，继续部署"
fi

# 部署前端文件
echo "📁 部署前端文件..."
mkdir -p $WEB_PATH
rm -rf $WEB_PATH/*
cp -r $PROJECT_PATH/dist/* $WEB_PATH/
chown -R www-data:www-data $WEB_PATH 2>/dev/null || chown -R nginx:nginx $WEB_PATH 2>/dev/null || echo "权限设置跳过"

# 更新Nginx配置
echo "⚙️ 更新Nginx配置..."
cp $PROJECT_PATH/nginx.conf /etc/nginx/sites-available/commission-platform 2>/dev/null || cp $PROJECT_PATH/nginx.conf /etc/nginx/conf.d/commission-platform.conf
if [ -d "/etc/nginx/sites-enabled" ]; then
    ln -sf /etc/nginx/sites-available/commission-platform /etc/nginx/sites-enabled/commission-platform
fi

# 测试Nginx配置
echo "🧪 测试Nginx配置..."
nginx -t
if [ $? -ne 0 ]; then
    echo "❌ Nginx配置测试失败"
    exit 1
fi

# 重新加载Nginx
echo "🔄 重新加载Nginx..."
systemctl reload nginx || service nginx reload

# 安装后端依赖
echo "📦 安装后端依赖..."
cd $PROJECT_PATH/backend
npm install --production
if [ $? -ne 0 ]; then
    echo "❌ 后端依赖安装失败"
    exit 1
fi

# 启动后端服务
echo "🚀 启动后端服务..."
pm2 start start-production.js --name commission-platform-api
if [ $? -ne 0 ]; then
    echo "❌ 后端服务启动失败"
    exit 1
fi

# 保存PM2配置
echo "💾 保存PM2配置..."
pm2 save

echo "✅ 远程部署完成！"
EOF

chmod +x $DEPLOY_DIR/remote-deploy.sh

# 3. 上传文件到远程服务器
echo "📤 上传文件到远程服务器..."
ssh $REMOTE_HOST "mkdir -p $REMOTE_PATH"
rsync -avz --delete $DEPLOY_DIR/ $REMOTE_HOST:$REMOTE_PATH/
if [ $? -ne 0 ]; then
    echo "❌ 文件上传失败"
    rm -rf $DEPLOY_DIR
    exit 1
fi

# 4. 在远程服务器上执行部署
echo "🔧 在远程服务器上执行部署..."
ssh $REMOTE_HOST "cd $REMOTE_PATH && ./remote-deploy.sh"
if [ $? -ne 0 ]; then
    echo "❌ 远程部署执行失败"
    rm -rf $DEPLOY_DIR
    exit 1
fi

# 5. 清理本地临时文件
echo "🧹 清理本地临时文件..."
rm -rf $DEPLOY_DIR

# 6. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 7. 测试远程API
echo "🧪 测试远程API..."
for i in {1..5}; do
    curl -f https://ye.bzcy.xyz/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}' > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ 远程API测试成功"
        break
    else
        echo "⏳ 等待API启动... ($i/5)"
        sleep 5
    fi
done

# 8. 显示部署状态
echo "📊 获取远程服务器状态..."
ssh $REMOTE_HOST "pm2 status commission-platform-api"

echo ""
echo "🎉 远程部署完成！"
echo "🌐 网站地址: https://ye.bzcy.xyz"
echo "🖥️  远程服务器: $REMOTE_HOST"
echo ""
echo "📝 常用远程命令:"
echo "  查看服务状态: ssh $REMOTE_HOST 'pm2 status'"
echo "  查看日志: ssh $REMOTE_HOST 'pm2 logs commission-platform-api'"
echo "  重启服务: ssh $REMOTE_HOST 'pm2 restart commission-platform-api'"
echo "  查看Nginx状态: ssh $REMOTE_HOST 'systemctl status nginx'"
