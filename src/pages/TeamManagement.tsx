import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import { message, Modal, InputNumber, Form, Tabs, Table } from 'antd';



interface Agent {
  id: number;
  username: string;
  name: string;
  phone: string;
  status: string;
  createdAt: string;
  commissionRate?: number;
}

const TeamManagement: React.FC = () => {
  const { token, user } = useAuth();
  const [isGenerating, setIsGenerating] = useState(false);
  const [inviteLink, setInviteLink] = useState('');
  const [agents, setAgents] = useState<Agent[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;
  const [filterStatus, setFilterStatus] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isCommissionModalVisible, setIsCommissionModalVisible] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [teamStats, setTeamStats] = useState({
    totalAgents: 0,
    pendingAgents: 0,
    approvedAgents: 0,
    rejectedAgents: 0
  });
  
  // 佣金统计
  const [commissionData, setCommissionData] = useState([]);
  const [totalCommission, setTotalCommission] = useState(0);
  const [isLoadingCommission, setIsLoadingCommission] = useState(false);
  
  // 佣金率设置

  // 生成邀请链接
  const generateInviteLink = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch(`${config.apiUrl}/api/auth/generate-invite-link`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          baseUrl: window.location.origin
        })
      });

      if (!response.ok) {
        throw new Error('生成邀请链接失败');
      }

      const data = await response.json();
      setInviteLink(data.inviteLink);
    } catch (error) {
      message.error('生成邀请链接失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  // 复制邀请链接
  const copyInviteLink = async () => {
    try {
      await navigator.clipboard.writeText(inviteLink);
      message.success('邀请链接已复制到剪贴板');
    } catch (error) {
      message.error('复制失败，请手动复制');
    }
  };

  // 获取代理列表
  const fetchAgents = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `${config.apiUrl}/api/users/sub-agents?page=${currentPage}&pageSize=${pageSize}`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (!response.ok) {
        throw new Error('获取代理列表失败');
      }

      const data = await response.json();
      // 确保佣金率为整数
      const processedAgents = data.agents.map((agent: any) => ({
        ...agent,
        commissionRate: agent.commissionRate !== undefined && agent.commissionRate !== null
          ? Math.round(agent.commissionRate)
          : undefined
      }));
      // 更新团队统计信息
      if (data.stats) {
        setTeamStats({
          totalAgents: data.stats.totalAgents || 0,
          pendingAgents: data.stats.pendingAgents || 0,
          approvedAgents: data.stats.approvedAgents || 0,
          rejectedAgents: data.stats.rejectedAgents || 0
        });
      }
      setAgents(processedAgents);
      setTotal(data.total);
    } catch (error) {
      message.error('获取代理列表失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 审核代理
  const approveAgent = async (agentId: number) => {
    try {
      const response = await fetch(`${config.apiUrl}/api/users/${agentId}/approve`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('审核失败');
      }

      message.success('审核通过');
      fetchAgents();
    } catch (error) {
      message.error('审核失败，请重试');
    }
  };

  // 拒绝代理
  const rejectAgent = async (agentId: number) => {
    try {
      const response = await fetch(`${config.apiUrl}/api/users/${agentId}/reject`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('拒绝失败');
      }

      message.success('已拒绝该代理');
      fetchAgents();
    } catch (error) {
      message.error('操作失败，请重试');
    }
  };

  // 格式化状态显示
  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      pending: '待审核',
      approved: '已通过',
      rejected: '已拒绝'
    };
    return statusMap[status] || status;
  };

  // 状态样式
  const getStatusClass = (status: string) => {
    const statusClasses: Record<string, string> = {
      pending: 'bg-yellow-100 text-yellow-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };
    return `px-2 py-1 text-xs rounded-full ${statusClasses[status] || ''}`;
  };

  // 格式化日期
  const formatDate = (date: string) => {
    return new Date(date).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 过滤代理列表
  const filteredAgents = agents.filter(agent => {
    const matchStatus = !filterStatus || agent.status === filterStatus;
    const matchSearch = !searchQuery || 
      agent.name.includes(searchQuery) || 
      agent.phone.includes(searchQuery);
    return matchStatus && matchSearch;
  });

  // 设置佣金率模态框
  const showCommissionModal = (agentId: number) => {
    const agent = agents.find(a => a.id === agentId);
    if (agent) {
      setSelectedAgent(agent);
      // 确保佣金率显示正确
      const commissionRate = agent.commissionRate !== undefined && agent.commissionRate !== null
        ? (agent.commissionRate > 1 ? Math.round(agent.commissionRate) : Math.round(agent.commissionRate * 100))
        : undefined;
      form.setFieldsValue({ commissionRate });
      setIsCommissionModalVisible(true);
    }
  };

  // 设置佣金率
  const handleSetCommission = async () => {
    if (!selectedAgent) return;
    
    try {
      setConfirmLoading(true);
      const values = await form.validateFields();
      // 确保佣金率为整数
      const newRate = Math.round(values.commissionRate);
      
      const response = await fetch(`${config.apiUrl}/api/auth/set-sub-agent-commission/${selectedAgent.id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          commissionRate: newRate // 直接使用整数百分比，不需要除以100
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '设置佣金率失败');
      }

      message.success('佣金率设置成功');
      setIsCommissionModalVisible(false);
      
      // 更新本地代理列表
      setAgents(agents.map(agent => 
        agent.id === selectedAgent.id 
          ? { ...agent, commissionRate: newRate } 
          : agent
      ));
      
      // 刷新代理列表
      fetchAgents();
    } catch (error: any) {
      message.error(error.message || '设置佣金率失败，请重试');
    } finally {
      setConfirmLoading(false);
    }
  };

  // 取消设置佣金率
  const handleCancelCommission = () => {
    setIsCommissionModalVisible(false);
  };

  // 获取佣金明细
  const fetchCommissionData = async () => {
    setIsLoadingCommission(true);
    try {
      const response = await fetch(`${config.apiUrl}/api/users/commission-stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || '获取佣金数据失败');
      }

      const data = await response.json();
      setCommissionData(data.commissions || []);
      setTotalCommission(data.totalCommission || 0);
    } catch (error: any) {
      console.error('获取佣金数据失败:', error);
      message.error(`获取佣金数据失败: ${error.message || '请重试'}`);
    } finally {
      setIsLoadingCommission(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    if (token) {
      fetchAgents();
      fetchCommissionData();
    }
  }, [currentPage, token]);

  // 计算团队统计数据
  useEffect(() => {
    const stats = {
      totalAgents: agents.length,
      pendingAgents: agents.filter(agent => agent.status === 'pending').length,
      approvedAgents: agents.filter(agent => agent.status === 'approved').length,
      rejectedAgents: agents.filter(agent => agent.status === 'rejected').length
    };
    setTeamStats(stats);
  }, [agents]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-gray-600">加载中...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
      <h1 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">我的团队</h1>

      <Tabs defaultActiveKey="team" className="mb-4 sm:mb-6" items={[
        {
          key: "team",
          label: "团队管理",
          children: (
            <>
              {/* 团队统计卡片 */}
              <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-2 sm:gap-4 mb-4 sm:mb-8">
                <div className="bg-white rounded-lg shadow p-4">
                  <h3 className="text-lg font-semibold text-gray-700">总代理数</h3>
                  <p className="text-3xl font-bold mt-2">{teamStats.totalAgents}</p>
                </div>
                <div className="bg-white rounded-lg shadow p-4">
                  <h3 className="text-lg font-semibold text-yellow-600">待审核</h3>
                  <p className="text-3xl font-bold mt-2 text-yellow-600">{teamStats.pendingAgents}</p>
                </div>
                <div className="bg-white rounded-lg shadow p-4">
                  <h3 className="text-lg font-semibold text-green-600">已通过</h3>
                  <p className="text-3xl font-bold mt-2 text-green-600">{teamStats.approvedAgents}</p>
                </div>
                <div className="bg-white rounded-lg shadow p-4">
                  <h3 className="text-lg font-semibold text-red-600">已拒绝</h3>
                  <p className="text-3xl font-bold mt-2 text-red-600">{teamStats.rejectedAgents}</p>
                </div>
              </div>
              
              {/* 生成邀请链接部分 */}
              <div className="bg-white rounded-lg shadow p-3 sm:p-6 mb-4 sm:mb-8">
            <h2 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-4">邀请新代理</h2>
            {inviteLink ? (
              <div className="mb-3 sm:mb-4">
                <p className="text-gray-700 mb-2 text-sm sm:text-base">邀请链接已生成，分享给有意向成为下级代理的人:</p>
                <div className="flex flex-col sm:flex-row items-start sm:items-center">
                  <input
                    type="text"
                    value={inviteLink}
                    readOnly
                    className="block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm mb-2 sm:mb-0"
                  />
                  <button
                    onClick={() => {
                      navigator.clipboard.writeText(inviteLink);
                      message.success('邀请链接已复制到剪贴板');
                    }}
                    className="sm:ml-2 w-full sm:w-auto inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    复制
                  </button>
                </div>
              </div>
            ) : (
              <button
                onClick={generateInviteLink}
                disabled={isGenerating}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                {isGenerating ? '生成中...' : '生成邀请链接'}
              </button>
            )}
          </div>
          
          {/* 代理列表 */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-3 sm:px-6 py-3 sm:py-4 border-b border-gray-200 bg-gray-50">
              <h2 className="text-base sm:text-xl font-semibold">下级代理列表</h2>
            </div>
            <div className="p-3 sm:p-6">
              {/* 搜索和筛选 */}
              <div className="flex flex-col md:flex-row md:items-center justify-between mb-3 sm:mb-4">
                <div className="w-full md:w-1/2 mb-3 md:mb-0">
                  <input
                    type="text"
                    placeholder="搜索代理名称或手机号"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full p-2 text-sm border border-gray-300 rounded-md"
                  />
                </div>
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setFilterStatus('')}
                    className={`px-3 py-1 rounded-md ${
                      filterStatus === '' ? 'bg-indigo-100 text-indigo-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    全部
                  </button>
                  <button
                    onClick={() => setFilterStatus('pending')}
                    className={`px-3 py-1 rounded-md ${
                      filterStatus === 'pending' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    待审核
                  </button>
                  <button
                    onClick={() => setFilterStatus('approved')}
                    className={`px-3 py-1 rounded-md ${
                      filterStatus === 'approved' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    已通过
                  </button>
                  <button
                    onClick={() => setFilterStatus('rejected')}
                    className={`px-3 py-1 rounded-md ${
                      filterStatus === 'rejected' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    已拒绝
                  </button>
                </div>
              </div>
              
              {/* 表格 */}
              <div className="responsive-table-container" style={{ overflowX: 'auto', width: '100%' }}>
                <Table
                  dataSource={filteredAgents}
                  rowKey="id"
                  pagination={{
                    current: currentPage,
                    pageSize: pageSize,
                    total: total,
                    onChange: (page) => setCurrentPage(page),
                    size: 'small',
                    showSizeChanger: false
                  }}
                  loading={isLoading}
                  size={window.innerWidth < 768 ? "small" : "middle"}
                  scroll={{ x: 'max-content' }}
                  className="responsive-table"
                  columns={[
                    {
                      title: '代理名称',
                      dataIndex: 'name',
                      key: 'name',
                      render: (_, record) => (
                        <div>
                          <div className="text-sm font-medium text-gray-900">{record.name}</div>
                          <div className="text-sm text-gray-500">{record.username}</div>
                        </div>
                      )
                    },
                    {
                      title: '手机号',
                      dataIndex: 'phone',
                      key: 'phone',
                    },
                    {
                      title: '状态',
                      dataIndex: 'status',
                      key: 'status',
                      render: (status) => (
                        <span className={`inline-flex px-2 text-xs font-semibold leading-5 rounded-full ${
                          status === 'pending'
                            ? 'bg-yellow-100 text-yellow-800'
                            : status === 'approved'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {status === 'pending'
                            ? '待审核'
                            : status === 'approved'
                            ? '已通过'
                            : '已拒绝'}
                        </span>
                      )
                    },
                    {
                      title: '注册时间',
                      dataIndex: 'createdAt',
                      key: 'createdAt',
                      render: (text) => formatDate(text)
                    },
                    {
                      title: '佣金率',
                      dataIndex: 'commissionRate',
                      key: 'commissionRate',
                      render: (_, record) => {
                        // 确保显示正确的佣金率
                        if (record.commissionRate !== undefined && record.commissionRate !== null) {
                          // 检查佣金率是否已经是百分比形式
                          const displayRate = record.commissionRate > 1 ? record.commissionRate : record.commissionRate * 100;
                          return `${Math.round(displayRate)}%`;
                        } else {
                          return '未设置';
                        }
                      }
                    },
                    {
                      title: '操作',
                      dataIndex: 'id',
                      key: 'id',
                      render: (_, record) => (
                        <div>
                          {record.status === 'pending' && (
                            <>
                              <button
                                onClick={() => approveAgent(record.id)}
                                className="text-green-600 hover:text-green-900 mr-4"
                              >
                                通过
                              </button>
                              <button
                                onClick={() => rejectAgent(record.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                拒绝
                              </button>
                            </>
                          )}
                          {record.status === 'approved' && (
                            <button
                              onClick={() => showCommissionModal(record.id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              设置佣金率
                            </button>
                          )}
                        </div>
                      )
                    }
                  ]}
                />
              </div>
              
              {/* 分页 */}
              {total > pageSize && (
                <div className="flex justify-center mt-4">
                  <div className="flex space-x-1">
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="px-3 py-1 rounded border border-gray-300 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <span className="px-3 py-1">
                      第 {currentPage} 页，共 {Math.ceil(total / pageSize)} 页
                    </span>
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage >= Math.ceil(total / pageSize)}
                      className="px-3 py-1 rounded border border-gray-300 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
            </>
          )
        },
        {
          key: "commission",
          label: "佣金明细",
          disabled: true, // 禁用此标签页，使其不可点击
          children: (
            <>
              <div className="bg-white rounded-lg shadow p-3 sm:p-6">
                <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-6">下级代理佣金明细</h2>
                
                <div className="px-2">
                  {isLoadingCommission ? (
                    <div className="text-center py-10">加载中...</div>
                  ) : commissionData.length === 0 ? (
                    <div className="text-center py-10">暂无佣金数据</div>
                  ) : (
                    <div className="responsive-table-container" style={{ overflowX: 'auto', width: '100%' }}>
                      <Table
                        dataSource={commissionData}
                        rowKey="id"
                        pagination={{
                          pageSize: 10,
                          showTotal: (total) => `共 ${total} 条记录`,
                          showSizeChanger: false
                        }}
                        scroll={{ x: 'max-content' }}
                        className="responsive-table"
                        size={window.innerWidth < 768 ? "small" : "middle"}
                        columns={[
                          {
                            title: '下级代理',
                            dataIndex: 'agentName',
                            key: 'agentName',
                          },
                          {
                            title: '订单号',
                            dataIndex: 'orderId',
                            key: 'orderId',
                          },
                          {
                            title: '订单金额',
                            dataIndex: 'orderAmount',
                            key: 'orderAmount',
                            render: (amount) => `¥${amount.toFixed(2)}`,
                          },
                          {
                            title: '佣金比例',
                            dataIndex: 'commissionRate',
                            key: 'commissionRate',
                            render: (rate) => `${Math.round(rate)}%`,
                          },
                          {
                            title: '佣金金额',
                            dataIndex: 'commissionAmount',
                            key: 'commissionAmount',
                            render: (amount) => `¥${amount.toFixed(2)}`,
                          },
                          {
                            title: '时间',
                            dataIndex: 'createdAt',
                            key: 'createdAt',
                            render: (date) => formatDate(date),
                          },
                          {
                            title: '状态',
                            dataIndex: 'status',
                            key: 'status',
                            render: (status) => (
                              <span className={`inline-flex px-2 text-xs font-semibold leading-5 rounded-full ${
                                status === 'pending'
                                  ? 'bg-yellow-100 text-yellow-800'
                                  : status === 'paid'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {status === 'pending'
                                  ? '待结算'
                                  : status === 'paid'
                                  ? '已结算'
                                  : '已取消'}
                              </span>
                            ),
                          },
                    ]}
                    summary={(pageData) => {
                      let totalCommission = 0;
                      pageData.forEach(({ commissionAmount }) => {
                        totalCommission += commissionAmount || 0;
                      });
                      
                      return (
                        <>
                          <Table.Summary.Row>
                            <Table.Summary.Cell index={0} colSpan={4}>
                              <strong>本页佣金总计</strong>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={1}>
                              <strong>¥{totalCommission.toFixed(2)}</strong>
                            </Table.Summary.Cell>
                            <Table.Summary.Cell index={2} colSpan={2}></Table.Summary.Cell>
                          </Table.Summary.Row>
                        </>
                      );
                    }}
                  />
                </div>
              )}
              <p className="text-lg font-bold mt-4">总佣金：¥{totalCommission.toFixed(2)}</p>
            </div>
          </div>
        </>
      )}
    ]} />

      {/* 设置佣金率模态框 */}
      <Modal
        title="设置下级代理佣金率"
        open={isCommissionModalVisible}
        onOk={handleSetCommission}
        onCancel={handleCancelCommission}
        okText="确认"
        cancelText="取消"
        centered
        width={380}
        className="commission-modal"
        confirmLoading={confirmLoading}
      >
        <Form
          form={form}
          layout="vertical"
          size="large"
        >
          <Form.Item
            name="commissionRate"
            label={`设置 ${selectedAgent?.name || ''} 的佣金率`}
            rules={[
              { required: true, message: '请输入佣金率' },
              { 
                type: 'number', 
                min: 0, 
                max: 100, 
                message: '佣金率必须在0-100%之间' 
              }
            ]}
          >
            <InputNumber
              min={0}
              max={100}
              step={1}
              precision={0}
              formatter={value => `${value}%`}
              parser={(value) => value ? parseFloat(value.replace('%', '')) : 0}
              style={{ width: '100%' }}
            />
          </Form.Item>
          <div className="bg-blue-50 p-3 rounded-md mb-3">
            <h4 className="text-blue-700 font-medium mb-2">佣金率说明：</h4>
            <ul className="text-sm text-blue-600 list-disc pl-5 space-y-1">
              <li>佣金率决定了下级代理从产品销售中获得的佣金比例</li>
              <li>设置后，该代理将按照此比例获得佣金</li>
              <li>您可以随时调整下级代理的佣金率</li>
            </ul>
          </div>
          <div className="bg-yellow-50 p-3 rounded-md">
            <p className="text-sm text-yellow-700 font-medium">提示：您当前的佣金率为 <span className="text-red-500 font-bold">{user?.commissionRate ? Math.round(user.commissionRate) : 0}%</span>，您可以为下级代理设置0-100%之间的任意佣金率。</p>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default TeamManagement;