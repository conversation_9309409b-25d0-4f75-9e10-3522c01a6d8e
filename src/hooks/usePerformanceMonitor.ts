// 性能监控Hook - 产品级性能优化

import { useEffect, useRef, useCallback } from 'react';

interface PerformanceMetrics {
  renderTime: number;
  componentMountTime: number;
  memoryUsage?: number;
  networkStatus: boolean;
}

interface PerformanceConfig {
  enableMetrics?: boolean;
  enableMemoryMonitoring?: boolean;
  enableNetworkMonitoring?: boolean;
  reportInterval?: number;
  onMetricsReport?: (metrics: PerformanceMetrics) => void;
}

export function usePerformanceMonitor(config: PerformanceConfig = {}) {
  const {
    enableMetrics = true,
    enableMemoryMonitoring = true,
    enableNetworkMonitoring = true,
    reportInterval = 30000, // 30秒
    onMetricsReport
  } = config;

  const mountTimeRef = useRef<number>(Date.now());
  const renderCountRef = useRef<number>(0);
  const lastReportTimeRef = useRef<number>(Date.now());
  const metricsRef = useRef<PerformanceMetrics>({
    renderTime: 0,
    componentMountTime: 0,
    networkStatus: navigator.onLine
  });

  // 测量渲染性能
  const measureRenderPerformance = useCallback(() => {
    if (!enableMetrics) return;

    const renderStart = performance.now();
    
    // 使用 requestAnimationFrame 来测量实际渲染时间
    requestAnimationFrame(() => {
      const renderEnd = performance.now();
      const renderTime = renderEnd - renderStart;
      
      renderCountRef.current += 1;
      metricsRef.current.renderTime = renderTime;
    });
  }, [enableMetrics]);

  // 获取内存使用情况
  const getMemoryUsage = useCallback((): number | undefined => {
    if (!enableMemoryMonitoring) return undefined;

    // @ts-ignore - performance.memory 在某些浏览器中可用
    if (performance.memory) {
      // @ts-ignore
      return performance.memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    return undefined;
  }, [enableMemoryMonitoring]);

  // 监控网络状态
  const monitorNetworkStatus = useCallback(() => {
    if (!enableNetworkMonitoring) return;

    const updateNetworkStatus = () => {
      metricsRef.current.networkStatus = navigator.onLine;
    };

    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    return () => {
      window.removeEventListener('online', updateNetworkStatus);
      window.removeEventListener('offline', updateNetworkStatus);
    };
  }, [enableNetworkMonitoring]);

  // 报告性能指标
  const reportMetrics = useCallback(() => {
    const now = Date.now();
    const componentMountTime = now - mountTimeRef.current;
    const memoryUsage = getMemoryUsage();

    const metrics: PerformanceMetrics = {
      ...metricsRef.current,
      componentMountTime,
      memoryUsage
    };

    // 调用回调函数
    if (onMetricsReport) {
      onMetricsReport(metrics);
    }

    // 发送到分析服务
    if (enableMetrics) {
      sendMetricsToAnalytics(metrics);
    }

    lastReportTimeRef.current = now;
  }, [enableMetrics, getMemoryUsage, onMetricsReport]);

  // 发送指标到分析服务
  const sendMetricsToAnalytics = useCallback((metrics: PerformanceMetrics) => {
    try {
      // 这里可以集成分析服务，如 Google Analytics, Mixpanel 等
      const analyticsData = {
        event: 'performance_metrics',
        properties: {
          ...metrics,
          timestamp: Date.now(),
          userAgent: navigator.userAgent,
          url: window.location.href,
          renderCount: renderCountRef.current
        }
      };

      console.log('Performance Metrics:', analyticsData);

      // 发送到分析服务
      // analytics.track('performance_metrics', analyticsData);
      
      // 或者发送到自己的服务器
      // fetch('/api/analytics/performance', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(analyticsData)
      // });
    } catch (error) {
      console.warn('Failed to send performance metrics:', error);
    }
  }, []);

  // 检测性能问题
  const detectPerformanceIssues = useCallback((metrics: PerformanceMetrics) => {
    const issues: string[] = [];

    // 渲染时间过长
    if (metrics.renderTime > 16) { // 60fps = 16.67ms per frame
      issues.push(`Slow render: ${metrics.renderTime.toFixed(2)}ms`);
    }

    // 内存使用过高
    if (metrics.memoryUsage && metrics.memoryUsage > 100) { // 100MB
      issues.push(`High memory usage: ${metrics.memoryUsage.toFixed(2)}MB`);
    }

    // 网络离线
    if (!metrics.networkStatus) {
      issues.push('Network offline');
    }

    // 组件挂载时间过长
    if (metrics.componentMountTime > 3000) { // 3秒
      issues.push(`Slow component mount: ${metrics.componentMountTime}ms`);
    }

    if (issues.length > 0) {
      console.warn('Performance Issues Detected:', issues);
      
      // 可以发送警告到监控服务
      // sendAlertToMonitoring(issues);
    }

    return issues;
  }, []);

  // 获取Web Vitals指标
  const getWebVitals = useCallback(() => {
    if (!('PerformanceObserver' in window)) return;

    try {
      // 监控 Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        console.log('LCP:', lastEntry.startTime);
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // 监控 First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          console.log('FID:', entry.processingStart - entry.startTime);
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // 监控 Cumulative Layout Shift (CLS)
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        const entries = list.getEntries();
        entries.forEach((entry) => {
          // @ts-ignore
          if (!entry.hadRecentInput) {
            // @ts-ignore
            clsValue += entry.value;
          }
        });
        console.log('CLS:', clsValue);
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      return () => {
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    } catch (error) {
      console.warn('Failed to setup Web Vitals monitoring:', error);
    }
  }, []);

  // 初始化监控
  useEffect(() => {
    const cleanupNetwork = monitorNetworkStatus();
    const cleanupWebVitals = getWebVitals();

    // 定期报告指标
    const reportInterval_id = setInterval(() => {
      reportMetrics();
      const currentMetrics = {
        ...metricsRef.current,
        componentMountTime: Date.now() - mountTimeRef.current,
        memoryUsage: getMemoryUsage()
      };
      detectPerformanceIssues(currentMetrics);
    }, reportInterval);

    return () => {
      if (cleanupNetwork) cleanupNetwork();
      if (cleanupWebVitals) cleanupWebVitals();
      clearInterval(reportInterval_id);
    };
  }, [monitorNetworkStatus, getWebVitals, reportMetrics, detectPerformanceIssues, reportInterval]);

  // 每次渲染时测量性能
  useEffect(() => {
    measureRenderPerformance();
  });

  // 返回性能工具函数
  return {
    measureRenderPerformance,
    reportMetrics,
    getMemoryUsage,
    getCurrentMetrics: () => ({
      ...metricsRef.current,
      componentMountTime: Date.now() - mountTimeRef.current,
      memoryUsage: getMemoryUsage()
    }),
    renderCount: renderCountRef.current
  };
}
