// 增强的八字测算类型定义

export interface Location {
  province: string;
  city: string;
  district?: string;
  longitude?: number;
  latitude?: number;
  timezone?: string;
}

export interface TimeInfo {
  hour: number;
  minute: number;
  traditionalHour?: string; // 传统时辰
  description?: string;
}

export interface BaziFormData {
  name: string;
  gender: 'male' | 'female';
  birthDate: string;
  birthTime: TimeInfo;
  location: Location;
  calendarType: 'gregorian' | 'lunar';
  isAccurate: boolean; // 时间是否准确
  notes?: string;
}

export interface BaziResult {
  id: string;
  formData: BaziFormData;
  bazi: {
    year: string;
    month: string;
    day: string;
    hour: string;
  };
  elements: {
    year: string;
    month: string;
    day: string;
    hour: string;
  };
  analysis: {
    personality: string;
    career: string;
    wealth: string;
    health: string;
    relationship: string;
    fortune: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface FormState {
  data: Partial<BaziFormData>;
  loading: boolean;
  submitting: boolean;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  timestamp: string;
}

export interface PaymentInfo {
  orderId: string;
  amount: number;
  currency: string;
  productCode: string;
  productName: string;
  redirectUrl: string;
  callbackUrl: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US';
  notifications: boolean;
  autoSave: boolean;
  defaultCalendar: 'gregorian' | 'lunar';
}

// 传统时辰定义
export const TRADITIONAL_HOURS = [
  { value: '子时', range: [23, 1], description: '23:00-01:00' },
  { value: '丑时', range: [1, 3], description: '01:00-03:00' },
  { value: '寅时', range: [3, 5], description: '03:00-05:00' },
  { value: '卯时', range: [5, 7], description: '05:00-07:00' },
  { value: '辰时', range: [7, 9], description: '07:00-09:00' },
  { value: '巳时', range: [9, 11], description: '09:00-11:00' },
  { value: '午时', range: [11, 13], description: '11:00-13:00' },
  { value: '未时', range: [13, 15], description: '13:00-15:00' },
  { value: '申时', range: [15, 17], description: '15:00-17:00' },
  { value: '酉时', range: [17, 19], description: '17:00-19:00' },
  { value: '戌时', range: [19, 21], description: '19:00-21:00' },
  { value: '亥时', range: [21, 23], description: '21:00-23:00' },
] as const;

export type TraditionalHour = typeof TRADITIONAL_HOURS[number]['value'];

// 错误类型
export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  PAYMENT_ERROR = 'PAYMENT_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface AppError {
  code: ErrorCode;
  message: string;
  details?: any;
  timestamp: string;
  userId?: string;
  sessionId?: string;
}

// 事件类型
export interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  userId?: string;
  sessionId: string;
  timestamp: string;
  properties?: Record<string, any>;
}

// 配置类型
export interface AppConfig {
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
  };
  payment: {
    providers: string[];
    currency: string;
    sandbox: boolean;
  };
  features: {
    analytics: boolean;
    errorReporting: boolean;
    autoSave: boolean;
    offlineMode: boolean;
  };
  ui: {
    theme: string;
    animations: boolean;
    reducedMotion: boolean;
  };
}
