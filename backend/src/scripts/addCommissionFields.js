import { User, Order } from '../models/index.js';
import sequelize from '../config/database.js';

const addCommissionFields = async () => {
  try {
    console.log('开始添加佣金字段...\n');

    // 1. 检查字段是否存在
    const [columns] = await sequelize.query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Users' 
      AND TABLE_SCHEMA = 'commission_platform4';
    `);
    
    const existingColumns = columns.map(col => col.COLUMN_NAME.toLowerCase());
    
    // 2. 添加缺失的字段
    const addColumns = [];
    
    if (!existingColumns.includes('totalcommission')) {
      addColumns.push('ADD COLUMN totalCommission DECIMAL(10,2) DEFAULT 0');
    }
    
    if (!existingColumns.includes('monthlycommission')) {
      addColumns.push('ADD COLUMN monthlyCommission DECIMAL(10,2) DEFAULT 0');
    }
    
    if (!existingColumns.includes('lastcommissionreset')) {
      addColumns.push('ADD COLUMN lastCommissionReset DATETIME');
    }
    
    if (addColumns.length > 0) {
      await sequelize.query(`
        ALTER TABLE Users 
        ${addColumns.join(', ')};
      `);
      console.log('新字段添加成功\n');
    } else {
      console.log('所有字段已存在\n');
    }

    // 3. 计算每个用户的累积佣金
    const users = await User.findAll();
    
    for (const user of users) {
      let totalCommission = 0;
      
      if (user.level === 0) {  // 管理员
        // 查询所有已支付订单的管理员佣金
        totalCommission = await Order.sum('adminCommission', {
          where: { status: 'paid' }
        });
      } else {  // 代理
        // 查询该代理的所有已支付订单佣金
        totalCommission = await Order.sum('agentCommission', {
          where: { 
            status: 'paid',
            agentId: user.id
          }
        });
        
        // 如果是一级代理，还要加上作为上级代理的佣金
        if (user.level === 1) {
          const parentCommission = await Order.sum('parentAgentCommission', {
            where: {
              status: 'paid',
              parentAgentId: user.id
            }
          });
          if (parentCommission) {
            totalCommission += parentCommission;
          }
        }
      }

      // 更新用户的累积佣金
      await user.update({
        totalCommission: totalCommission || 0,
        monthlyCommission: 0,  // 月度佣金从现在开始计算
        lastCommissionReset: new Date()
      });

      console.log(`用户 ${user.username} 的累积佣金已更新:`, {
        level: user.level,
        totalCommission: totalCommission || 0
      });
    }

    console.log('\n佣金字段添加和数据迁移完成！');

  } catch (error) {
    console.error('添加佣金字段失败:', error);
  } finally {
    process.exit();
  }
};

// 执行迁移
console.log('开始数据库迁移...');
addCommissionFields();
