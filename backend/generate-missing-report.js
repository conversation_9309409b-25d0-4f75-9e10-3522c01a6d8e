import { Order, BaziOrder } from './src/models/index.js';
import baziService from './src/services/baziService.js';

async function generateMissingReport(orderId) {
  console.log(`=== 为订单 ${orderId} 生成缺失的八字报告 ===\n`);
  
  try {
    // 1. 查找订单和八字订单信息
    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder',
        required: true
      }]
    });
    
    if (!order) {
      console.log('❌ 订单不存在');
      return;
    }
    
    if (!order.baziOrder) {
      console.log('❌ 八字订单不存在');
      return;
    }
    
    const baziOrder = order.baziOrder;
    
    console.log('📋 订单信息:');
    console.log(`  订单ID: ${order.id}`);
    console.log(`  客户姓名: ${order.customerName}`);
    console.log(`  订单状态: ${order.status}`);
    console.log(`  八字订单ID: ${baziOrder.id}`);
    console.log(`  当前报告状态: ${baziOrder.reportStatus}`);
    
    // 2. 检查是否已经有报告
    if (baziOrder.reportStatus === 'generated' && baziOrder.reportData) {
      console.log('\n✅ 报告已存在，无需重新生成');
      return;
    }
    
    // 3. 准备客户信息
    console.log('\n🔄 开始生成报告...');
    
    // 构造客户信息（从八字订单中获取）
    const customerInfo = {
      name: baziOrder.name,
      gender: baziOrder.gender,
      calendarType: baziOrder.calendarType,
      birthYear: baziOrder.birthYear,
      birthMonth: baziOrder.birthMonth,
      birthDay: baziOrder.birthDay,
      birthHour: baziOrder.birthHour,
      birthMinute: baziOrder.birthMinute,
      birthProvince: baziOrder.birthProvince,
      birthCity: baziOrder.birthCity
    };
    
    console.log('👤 客户信息:');
    Object.entries(customerInfo).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
    
    // 4. 调用八字服务生成报告
    try {
      console.log('\n🔮 调用八字服务生成报告...');
      
      const result = await baziService.generateBaziReport(customerInfo);
      
      if (result.success) {
        console.log('✅ 报告生成成功');
        
        // 保存报告数据和客户信息
        await baziOrder.update({
          reportStatus: 'generated',
          reportData: JSON.stringify(result.data),
          customerInfo: JSON.stringify(customerInfo)
        });
        
        console.log('💾 报告数据已保存到数据库');
        
        // 显示报告摘要
        if (result.data && result.data.命主信息) {
          console.log('\n📊 报告摘要:');
          Object.entries(result.data.命主信息).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
        }
        
        console.log('\n🎉 报告生成完成！用户现在可以正常访问八字报告页面了。');
        
      } else {
        console.log('❌ 报告生成失败:', result.error);
        
        // 更新状态为失败
        await baziOrder.update({ 
          reportStatus: 'failed',
          errorMessage: result.error
        });
        
        console.log('💡 建议检查:');
        console.log('1. 腾讯云API配置是否正确');
        console.log('2. 网络连接是否正常');
        console.log('3. API调用参数是否有效');
      }
      
    } catch (apiError) {
      console.error('❌ 调用八字服务时出错:', apiError);
      
      // 更新状态为失败
      await baziOrder.update({ 
        reportStatus: 'failed',
        errorMessage: apiError.message
      });
    }
    
  } catch (error) {
    console.error('生成报告过程中出错:', error);
  }
}

// 从命令行参数获取订单ID
const orderId = process.argv[2] || '250707185840805299';
generateMissingReport(orderId);