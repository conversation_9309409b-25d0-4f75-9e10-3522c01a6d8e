import express from 'express';
import { body } from 'express-validator';
import { register, login, getProfile, approveAgent, setSubAgentCommission } from '../controllers/authController.js';
import { auth, adminAuth } from '../middleware/auth.js';
import { generateInviteLink, verifyInviteToken } from '../utils/inviteLinkUtils.js';
import User from '../models/User.js';

const router = express.Router();

// 生成邀请链接（管理员和一级代理可用）
router.post('/generate-invite-link', auth, async (req, res) => {
  try {
    const user = req.user;
    const baseUrl = req.body.baseUrl || 'http://localhost:3000';

    // 检查用户权限
    if (user.role !== 'admin' && (user.role !== 'user' || user.level !== 1)) {
      return res.status(403).json({ message: '没有权限生成邀请链接' });
    }

    // 检查用户状态
    if (user.status !== 'approved') {
      return res.status(403).json({ message: '账号未获得审核通过' });
    }

    const inviteLink = generateInviteLink(user.id, user.role, baseUrl);
    
    res.json({ 
      message: '邀请链接生成成功',
      inviteLink 
    });
  } catch (error) {
    console.error('生成邀请链接失败:', error);
    res.status(500).json({ message: '生成邀请链接失败' });
  }
});

router.post('/register',
  [
    body('username')
      .trim()
      .notEmpty()
      .withMessage('用户名不能为空')
      .isLength({ min: 3 })
      .withMessage('用户名至少需要3个字符'),
    body('password')
      .trim()
      .notEmpty()
      .withMessage('密码不能为空')
      .isLength({ min: 6 })
      .withMessage('密码至少需要6个字符'),
    body('name')
      .trim()
      .notEmpty()
      .withMessage('姓名不能为空'),
    body('phone')
      .trim()
      .notEmpty()
      .withMessage('手机号不能为空')
      .matches(/^1[3-9]\d{9}$/)
      .withMessage('请输入有效的手机号'),
    body('inviteToken')
      .optional()
      .isString()
      .withMessage('无效的邀请链接')
  ],
  async (req, res) => {
    try {
      const { username, password, name, phone, inviteToken } = req.body;
      console.log('开始处理注册请求:', { username, name, phone });

      // 验证必填字段
      if (!username || !password || !name || !phone) {
        return res.status(400).json({ message: '请填写所有必填字段' });
      }

      // 检查用户名是否已存在
      const existingUser = await User.findOne({ where: { username } });
      if (existingUser) {
        return res.status(400).json({ message: '用户名已存在' });
      }

      let parentAgentId = null;
      let userLevel = 1;

      // 如果有邀请token，验证并获取管理员ID
      if (inviteToken) {
        console.log('验证邀请token');
        const decoded = verifyInviteToken(inviteToken);
        if (!decoded) {
          return res.status(400).json({ message: '邀请链接无效或已过期' });
        }

        // 验证管理员是否存在且有效
        // const admin = await User.findOne({
        //   where: {
        //     id: decoded.adminId,
        //     role: 'admin',
        //     status: 'approved'
        //   }
        // });

        // if (!admin) {
        //   return res.status(400).json({ message: '邀请人不存在或无效' });
        // }

        // parentAgentId = admin.id;
        // console.log('邀请人验证成功，parentAgentId:', parentAgentId);
        // 验证邀请人是否存在且有效
        const inviter = await User.findOne({
          where: {
            id: decoded.inviterId,  // 使用正确的字段名 inviterId
            status: 'approved'
             }
            });

          if (!inviter) {
             return res.status(400).json({ message: '邀请人不存在或无效' });
            }

         // 设置上级代理
        parentAgentId = inviter.id;
        userLevel = inviter.role === 'admin' ? 1 : 2;
        }

      // 创建用户
      console.log('开始创建用户');
      const user = await User.create({
        username,
        password,
        name,
        phone,
        parentAgentId,
        level: userLevel,
        status: 'pending',
        role: 'user',
        commissionRate: userLevel === 1 ? 0.8 : 0.6 // 一级代理80%,二级代理60%
      });

      console.log('用户创建成功:', user.id);

      res.status(201).json({
        message: '注册成功，请等待管理员审核',
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          phone: user.phone,
          level: user.level,
          status: user.status
        }
      });
    } catch (error) {
      console.error('注册失败:', error);
      res.status(500).json({ message: '注册失败，请稍后重试' });
    }
  }
);

router.post('/login',
  [
    body('username').trim().notEmpty().withMessage('请输入用户名'),
    body('password').trim().notEmpty().withMessage('请输入密码')
  ],
  login
);

router.get('/profile', auth, getProfile);

router.post('/approve-agent/:id', adminAuth, approveAgent);

router.post('/set-sub-agent-commission/:id', auth, setSubAgentCommission);

export default router;