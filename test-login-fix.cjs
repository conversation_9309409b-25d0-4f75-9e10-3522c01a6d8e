#!/usr/bin/env node

/**
 * 登录JSON解析错误修复验证脚本
 * 用于测试登录接口的响应格式和错误处理
 */

const https = require('https');
const http = require('http');

const config = {
  domain: 'ye.bzcy.xyz',
  apiPath: '/api/auth/login',
  testCredentials: {
    username: 'test',
    password: 'test123'
  }
};

function makeRequest(options, postData) {
  return new Promise((resolve, reject) => {
    const protocol = options.port === 443 ? https : http;
    const req = protocol.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function testLoginAPI() {
  console.log('🔍 测试登录API响应格式...');
  console.log('=' .repeat(50));
  
  const postData = JSON.stringify(config.testCredentials);
  
  const options = {
    hostname: config.domain,
    port: 443,
    path: config.apiPath,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };
  
  try {
    console.log(`📡 请求URL: https://${config.domain}${config.apiPath}`);
    console.log(`📦 请求数据:`, config.testCredentials);
    console.log('');
    
    const response = await makeRequest(options, postData);
    
    console.log(`📊 响应状态码: ${response.statusCode}`);
    console.log(`📋 Content-Type: ${response.headers['content-type'] || '未设置'}`);
    console.log('');
    
    // 分析响应内容
    const contentType = response.headers['content-type'] || '';
    const isJSON = contentType.includes('application/json');
    const isHTML = contentType.includes('text/html') || response.body.trim().startsWith('<');
    
    console.log('📄 响应内容分析:');
    console.log(`   - 是否为JSON: ${isJSON ? '✅' : '❌'}`);
    console.log(`   - 是否为HTML: ${isHTML ? '⚠️' : '✅'}`);
    console.log('');
    
    if (isHTML) {
      console.log('🚨 问题诊断: 服务器返回HTML而不是JSON');
      console.log('   这会导致前端JSON.parse()失败，出现"Unexpected token \'<\'"错误');
      console.log('');
      console.log('📝 HTML响应内容预览:');
      console.log(response.body.substring(0, 200) + (response.body.length > 200 ? '...' : ''));
    } else if (isJSON) {
      console.log('✅ 响应格式正确: JSON');
      try {
        const jsonData = JSON.parse(response.body);
        console.log('📝 JSON响应内容:', jsonData);
      } catch (e) {
        console.log('❌ JSON解析失败:', e.message);
        console.log('📝 原始响应:', response.body);
      }
    } else {
      console.log('⚠️  未知响应格式');
      console.log('📝 响应内容:', response.body.substring(0, 200));
    }
    
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

async function testBackendService() {
  console.log('\n🔧 测试后端服务状态...');
  console.log('=' .repeat(50));
  
  // 测试直接访问后端端口
  const backendOptions = {
    hostname: config.domain,
    port: 3005,
    path: config.apiPath,
    method: 'GET'
  };
  
  try {
    console.log(`📡 直接测试后端: http://${config.domain}:3005${config.apiPath}`);
    const response = await makeRequest(backendOptions);
    console.log(`📊 后端响应状态: ${response.statusCode}`);
    console.log(`📋 后端Content-Type: ${response.headers['content-type'] || '未设置'}`);
  } catch (error) {
    console.log(`❌ 后端服务不可达: ${error.message}`);
    console.log('   这可能是导致Nginx返回404 HTML页面的原因');
  }
}

function printSolutions() {
  console.log('\n🛠️  解决方案建议:');
  console.log('=' .repeat(50));
  
  console.log('1. 🔄 重启后端服务:');
  console.log('   ssh root@************');
  console.log('   pm2 restart commission-platform-api');
  console.log('   pm2 logs commission-platform-api');
  console.log('');
  
  console.log('2. 🔍 检查服务状态:');
  console.log('   pm2 status');
  console.log('   netstat -tlnp | grep 3005');
  console.log('');
  
  console.log('3. 📝 前端错误处理已优化:');
  console.log('   - 添加了Content-Type检查');
  console.log('   - 改进了JSON解析错误处理');
  console.log('   - 提供了用户友好的错误信息');
  console.log('');
  
  console.log('4. 🔧 如果问题持续存在:');
  console.log('   - 检查Nginx配置中的proxy_pass设置');
  console.log('   - 确认后端服务在3005端口正常运行');
  console.log('   - 查看后端服务日志排查具体错误');
}

async function main() {
  console.log('🚀 登录JSON解析错误诊断工具');
  console.log('时间:', new Date().toLocaleString());
  console.log('');
  
  await testLoginAPI();
  await testBackendService();
  printSolutions();
  
  console.log('\n✅ 诊断完成!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testLoginAPI, testBackendService };