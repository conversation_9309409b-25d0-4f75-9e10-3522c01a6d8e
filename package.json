{"name": "commission-platform", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "backend": "cd backend && npm run dev", "start": "concurrently \"npm run dev\" \"npm run backend\"", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.2.1", "@mui/icons-material": "^6.2.1", "@mui/material": "^6.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.14.2", "@types/crypto-js": "^4.2.2", "@types/styled-components": "^5.1.34", "antd": "^5.22.6", "axios": "^1.8.3", "chalk": "^5.4.1", "class-variance-authority": "^0.7.1", "cli-table3": "^0.6.5", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^12.23.12", "lucide-react": "^0.298.0", "mui": "^0.0.1", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.61.1", "react-router-dom": "^6.21.0", "recharts": "^2.15.1", "styled-components": "^6.1.15", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.2.2", "vite": "^5.0.8"}}