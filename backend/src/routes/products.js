import express from 'express';
import { Op } from 'sequelize';
import { auth, adminAuth } from '../middleware/auth.js';
import Product from '../models/Product.js';
import ProductLink from '../models/ProductLink.js';

const router = express.Router();

// 获取产品列表 - 根据用户角色返回不同内容
router.get('/', auth, async (req, res) => {
  try {
    console.log('查找到的用户:', { id: req.user.id, role: req.user.role });
    
    // 简化查询，不再使用分页和复杂过滤
    const products = await Product.findAll({ 
      attributes: ['id', 'title', 'description', 'price', 'status', 'baseCommissionRate', 'createdAt', 'updatedAt'],
      order: [['updatedAt', 'DESC']]
    });
    
    console.log(`获取到 ${products.length} 个产品`);
    
    // 转换价格为数字类型
    const formattedProducts = products.map(product => ({
      ...product.toJSON(),
      price: Number(product.price || 0),
      baseCommissionRate: Number(product.baseCommissionRate || 0)
    }));

    // 返回简化的响应结构
    res.json(formattedProducts);
  } catch (error) {
    console.error('获取产品列表失败:', error);
    res.status(500).json({ 
      message: '获取产品列表失败',
      error: error.message 
    });
  }
});

// 获取单个产品详情
router.get('/:id', auth, async (req, res) => {
  try {
    const product = await Product.findOne({
      where: {
        id: req.params.id,
        ...(req.user.role !== 'admin' ? { status: 'active' } : {})
      },
      attributes: ['id', 'title', 'description', 'price', 'status', 'baseCommissionRate', 'createdAt', 'updatedAt']
    });

    if (!product) {
      return res.status(404).json({ message: '产品不存在或已下架' });
    }

    // 确保价格是数字类型
    const formattedProduct = {
      ...product.toJSON(),
      price: Number(product.price)
    };

    res.json(formattedProduct);
  } catch (error) {
    console.error('获取产品详情失败:', error);
    res.status(500).json({ 
      message: '获取产品详情失败',
      error: error.message 
    });
  }
});

// 创建产品 - 仅管理员
router.post('/', adminAuth, async (req, res) => {
  try {
    const { title, description, price, baseCommissionRate, status = 'active' } = req.body;

    // 验证必填字段
    if (!title || !description || !price) {
      return res.status(400).json({ message: '产品名称、描述和价格为必填项' });
    }

    // 验证价格格式
    const numericPrice = Number(price);
    if (isNaN(numericPrice) || numericPrice <= 0) {
      return res.status(400).json({ message: '价格必须是大于0的数字' });
    }

    // 验证佣金比例
    const numericRate = Number(baseCommissionRate);
    if (isNaN(numericRate) || numericRate < 0 || numericRate > 100) {
      return res.status(400).json({ message: '佣金比例必须是0-100之间的数字' });
    }

    const product = await Product.create({
      title,
      description,
      price: numericPrice,
      baseCommissionRate: numericRate,
      status
    });

    console.log('产品创建成功:', {
      id: product.id,
      title: product.title,
      price: product.price
    });

    res.status(201).json({
      ...product.toJSON(),
      price: Number(product.price)
    });
  } catch (error) {
    console.error('创建产品失败:', error);
    res.status(400).json({ 
      message: '创建产品失败',
      error: error.message 
    });
  }
});

// 更新产品 - 仅管理员
router.put('/:id', adminAuth, async (req, res) => {
  try {
    const product = await Product.findByPk(req.params.id);
    if (!product) {
      return res.status(404).json({ message: '产品不存在' });
    }

    const { title, description, price, baseCommissionRate, status } = req.body;

    // 验证价格格式（如果提供）
    if (price !== undefined) {
      const numericPrice = Number(price);
      if (isNaN(numericPrice) || numericPrice <= 0) {
        return res.status(400).json({ message: '价格必须是大于0的数字' });
      }
      req.body.price = numericPrice;
    }

    // 验证佣金比例（如果提供）
    if (baseCommissionRate !== undefined) {
      const numericRate = Number(baseCommissionRate);
      if (isNaN(numericRate) || numericRate < 0 || numericRate > 100) {
        return res.status(400).json({ message: '佣金比例必须是0-100之间的数字' });
      }
      req.body.baseCommissionRate = numericRate;
    }

    await product.update(req.body);

    console.log('产品更新成功:', {
      id: product.id,
      title: product.title,
      price: product.price,
      baseCommissionRate: product.baseCommissionRate
    });

    res.json({
      ...product.toJSON(),
      price: Number(product.price)
    });
  } catch (error) {
    console.error('更新产品失败:', error);
    res.status(400).json({ 
      message: '更新产品失败',
      error: error.message 
    });
  }
});

// 删除产品 - 仅管理员
router.delete('/:id', adminAuth, async (req, res) => {
  try {
    const product = await Product.findByPk(req.params.id);
    if (!product) {
      return res.status(404).json({ message: '产品不存在' });
    }

    // 软删除：将状态改为 inactive
    await product.update({ status: 'inactive' });

    console.log('产品已软删除:', {
      id: product.id,
      title: product.title,
      status: 'inactive'
    });

    res.json({ message: '产品已删除' });
  } catch (error) {
    console.error('删除产品失败:', error);
    res.status(500).json({ 
      message: '删除产品失败',
      error: error.message 
    });
  }
});

export default router;