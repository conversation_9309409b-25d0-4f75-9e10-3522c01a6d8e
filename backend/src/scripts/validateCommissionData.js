import dotenv from 'dotenv';
dotenv.config();

import { User, Commission, Order } from '../models/index.js';
import sequelize from '../config/database.js';
import { Op } from 'sequelize';
import { fixCommissionConsistency } from '../services/commissionService.js';

/**
 * 验证佣金数据一致性并修复问题
 * 这个脚本可以通过cron定期运行，确保佣金数据的一致性
 */
async function validateCommissionData() {
  console.log('开始验证佣金数据一致性...');
  
  try {
    // 1. 修复用户佣金统计与实际佣金记录的一致性
    const consistencyResults = await fixCommissionConsistency();
    console.log(`修复了 ${consistencyResults.fixed} 个用户的佣金数据`);
    
    if (consistencyResults.users.length > 0) {
      console.log('修复的用户佣金数据:');
      consistencyResults.users.forEach(user => {
        console.log(`- 用户: ${user.username} (ID: ${user.id})`);
        console.log(`  修改前: 总佣金=${user.before.totalCommission}, 月佣金=${user.before.monthlyCommission}`);
        console.log(`  修改后: 总佣金=${user.after.totalCommission}, 月佣金=${user.after.monthlyCommission}`);
      });
    }
    
    // 2. 查找和修复订单与佣金记录不一致的情况
    const transaction = await sequelize.transaction();
    
    try {
      // 2.1 找出所有已支付但没有对应佣金记录的订单
      const paidOrdersWithoutCommission = await Order.findAll({
        where: {
          status: 'paid',
          [Op.or]: [
            { agentCommission: { [Op.gt]: 0 } },
            { parentAgentCommission: { [Op.gt]: 0 } }
          ]
        },
        include: [
          {
            model: Commission,
            as: 'orderCommissions',
            required: false
          }
        ],
        having: sequelize.literal('COUNT(DISTINCT `orderCommissions`.`id`) = 0'),
        group: ['Order.id'],
        transaction
      });
      
      console.log(`发现 ${paidOrdersWithoutCommission.length} 个已支付但没有佣金记录的订单`);
      
      // 2.2 为这些订单创建佣金记录
      let createdCommissions = 0;
      
      for (const order of paidOrdersWithoutCommission) {
        // 为代理商创建佣金记录
        if (order.agentCommission > 0 && order.agentId) {
          await Commission.create({
            orderId: order.id,
            agentId: order.agentId,
            amount: order.agentCommission,
            status: 'pending',
            description: `订单 ${order.id} 的佣金 (系统自动修复)`
          }, { transaction });
          
          createdCommissions++;
        }
        
        // 为上级代理商创建佣金记录
        if (order.parentAgentCommission > 0 && order.parentAgentId) {
          await Commission.create({
            orderId: order.id,
            agentId: order.parentAgentId,
            amount: order.parentAgentCommission,
            status: 'pending',
            description: `订单 ${order.id} 的上级佣金 (系统自动修复)`
          }, { transaction });
          
          createdCommissions++;
        }
      }
      
      console.log(`为缺失的订单创建了 ${createdCommissions} 条佣金记录`);
      
      // 3. 查找和修复佣金记录与订单状态不一致的情况
      const commissions = await Commission.findAll({
        include: [
          {
            model: Order,
            as: 'commissionOrder',
            required: true
          }
        ],
        transaction
      });
      
      let fixedStatusCount = 0;
      
      for (const commission of commissions) {
        const order = commission.commissionOrder;
        
        // 如果订单未支付，但佣金状态不是'pending'，则将佣金状态设为'pending'
        if (order.status !== 'paid' && commission.status !== 'pending') {
          await commission.update({
            status: 'pending'
          }, { transaction });
          
          fixedStatusCount++;
        }
      }
      
      console.log(`修复了 ${fixedStatusCount} 条佣金状态不一致的记录`);
      
      await transaction.commit();
      console.log('数据验证和修复完成');
      
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
    
  } catch (error) {
    console.error('佣金数据验证失败:', error);
    process.exit(1);
  }
  
  process.exit(0);
}

// 执行验证
validateCommissionData();
