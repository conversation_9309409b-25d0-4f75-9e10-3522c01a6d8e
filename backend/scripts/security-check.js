#!/usr/bin/env node

/**
 * 安全配置检查脚本
 * 检查系统中的安全问题和配置错误
 */

import fs from 'fs';
import path from 'path';

console.log('🔒 开始安全配置检查...\n');

const issues = [];
const warnings = [];

// 1. 检查环境变量配置
function checkEnvironmentVariables() {
  console.log('1. 检查环境变量配置...');
  
  const envFiles = ['.env.develop', '.env.production'];
  
  envFiles.forEach(envFile => {
    const envPath = path.join(process.cwd(), envFile);
    if (fs.existsSync(envPath)) {
      const content = fs.readFileSync(envPath, 'utf8');
      
      // 检查硬编码的默认密钥
      if (content.includes('your-super-secret-jwt-key')) {
        issues.push(`❌ ${envFile}: 使用了默认的JWT密钥，极其危险！`);
      }
      
      // 检查是否有敏感信息
      if (content.includes('PRIVATE_KEY=MII')) {
        warnings.push(`⚠️ ${envFile}: 包含私钥信息，确保文件安全`);
      }
      
      // 检查密码强度
      const passwordMatch = content.match(/PASSWORD=(.+)/);
      if (passwordMatch && passwordMatch[1].length < 8) {
        warnings.push(`⚠️ ${envFile}: 数据库密码过于简单`);
      }
      
      console.log(`   ✅ ${envFile} 已检查`);
    } else {
      warnings.push(`⚠️ 缺少环境变量文件: ${envFile}`);
    }
  });
}

// 2. 检查代码中的硬编码密钥
function checkHardcodedSecrets() {
  console.log('\n2. 检查代码中的硬编码密钥...');
  
  const filesToCheck = [
    'src/middleware/auth.js',
    'src/controllers/authController.js',
    'src/utils/inviteLinkUtils.js'
  ];
  
  filesToCheck.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      
      if (content.includes("'your-super-secret-jwt-key")) {
        issues.push(`❌ ${file}: 包含硬编码的JWT密钥`);
      }
      
      console.log(`   ✅ ${file} 已检查`);
    }
  });
}

// 3. 检查文件权限
function checkFilePermissions() {
  console.log('\n3. 检查敏感文件权限...');
  
  const sensitiveFiles = ['.env.develop', '.env.production'];
  
  sensitiveFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      const mode = stats.mode & parseInt('777', 8);
      
      if (mode > parseInt('600', 8)) {
        warnings.push(`⚠️ ${file}: 文件权限过于宽松 (${mode.toString(8)})`);
      }
      
      console.log(`   ✅ ${file} 权限: ${mode.toString(8)}`);
    }
  });
}

// 4. 生成安全建议
function generateSecurityRecommendations() {
  console.log('\n📋 安全建议:');
  console.log('=' .repeat(50));
  
  console.log('1. 立即更换所有默认密钥');
  console.log('2. 使用强密码（至少12位，包含大小写字母、数字、特殊字符）');
  console.log('3. 定期轮换密钥和密码');
  console.log('4. 限制敏感文件的访问权限');
  console.log('5. 使用密钥管理服务存储敏感信息');
  console.log('6. 启用数据库连接加密');
  console.log('7. 配置防火墙和访问控制');
}

// 执行检查
checkEnvironmentVariables();
checkHardcodedSecrets();
checkFilePermissions();

// 输出结果
console.log('\n🔍 检查结果:');
console.log('=' .repeat(50));

if (issues.length > 0) {
  console.log('\n🚨 严重安全问题:');
  issues.forEach(issue => console.log(issue));
}

if (warnings.length > 0) {
  console.log('\n⚠️ 安全警告:');
  warnings.forEach(warning => console.log(warning));
}

if (issues.length === 0 && warnings.length === 0) {
  console.log('✅ 未发现明显的安全问题');
}

generateSecurityRecommendations();

console.log('\n✅ 安全检查完成!');

// 如果有严重问题，退出码为1
if (issues.length > 0) {
  process.exit(1);
}
