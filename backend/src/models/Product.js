import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const Product = sequelize.define('Product', {
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  price: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    get() {
      const value = this.getDataValue('price');
      return value === null ? null : Number(value);
    }
  },
  minPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Minimum price for variable pricing products like naming service',
    get() {
      const value = this.getDataValue('minPrice');
      return value === null ? null : Number(value);
    }
  },
  maxPrice: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Maximum price for variable pricing products like naming service',
    get() {
      const value = this.getDataValue('maxPrice');
      return value === null ? null : Number(value);
    }
  },
  baseCommissionRate: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active'
  },
  type: {
    type: DataTypes.ENUM('physical', 'service', 'naming'),
    defaultValue: 'physical',
    comment: 'Product type: physical for goods, service for services like bazi query, naming for naming service'
  }
});

export default Product;