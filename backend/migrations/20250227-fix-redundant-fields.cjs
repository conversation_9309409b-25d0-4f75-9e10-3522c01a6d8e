'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 在Users表中添加新字段，标记佣金数据是否已同步
    await queryInterface.addColumn('Users', 'commissionSyncedAt', {
      type: Sequelize.DATE,
      allowNull: true,
      defaultValue: null
    });
    
    // 添加索引以提高佣金查询性能
    await queryInterface.addIndex('Commissions', ['agentId', 'status'], {
      name: 'commissions_agent_status_idx'
    });
    
    await queryInterface.addIndex('Commissions', ['status'], {
      name: 'commissions_status_idx'
    });
    
    await queryInterface.addIndex('Commissions', ['createdAt'], {
      name: 'commissions_created_at_idx'
    });
    
    // 给ProductLinks表添加删除标记字段，实现软删除
    await queryInterface.addColumn('ProductLinks', 'isDeleted', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false
    });
    
    // 给订单表添加更新时间戳字段，记录订单支付时间
    await queryInterface.addColumn('Orders', 'paidAt', {
      type: Sequelize.DATE,
      allowNull: true
    });
    
    // 创建一个触发器，在订单状态更新为'paid'时，自动更新paidAt字段
    // MySQL 5.7+支持JSON类型的触发器
    const triggerSQL = `
    CREATE TRIGGER orders_paid_at_trigger
    BEFORE UPDATE ON Orders
    FOR EACH ROW
    BEGIN
      IF NEW.status = 'paid' AND OLD.status != 'paid' THEN
        SET NEW.paidAt = NOW();
      END IF;
    END;
    `;
    
    try {
      await queryInterface.sequelize.query(triggerSQL);
    } catch (error) {
      console.error('创建触发器失败，可能已存在:', error.message);
    }
  },

  async down(queryInterface, Sequelize) {
    // 移除Users表中添加的字段
    await queryInterface.removeColumn('Users', 'commissionSyncedAt');
    
    // 移除添加的索引
    await queryInterface.removeIndex('Commissions', 'commissions_agent_status_idx');
    await queryInterface.removeIndex('Commissions', 'commissions_status_idx');
    await queryInterface.removeIndex('Commissions', 'commissions_created_at_idx');
    
    // 移除ProductLinks表中添加的字段
    await queryInterface.removeColumn('ProductLinks', 'isDeleted');
    
    // 移除Orders表中添加的字段
    await queryInterface.removeColumn('Orders', 'paidAt');
    
    // 移除触发器
    try {
      await queryInterface.sequelize.query('DROP TRIGGER IF EXISTS orders_paid_at_trigger');
    } catch (error) {
      console.error('移除触发器失败:', error.message);
    }
  }
};
