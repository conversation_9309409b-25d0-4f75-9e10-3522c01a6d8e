#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m'

# 定义变量
APP_NAME="commission-platform"
FRONTEND_DIR="$(pwd)"
BACKEND_DIR="$(pwd)/backend"
PM2_APP_NAME="commission-platform-api"

# 获取部署信息
read -p "请输入您的域名 (例如: example.com): " DOMAIN
if [ -z "$DOMAIN" ]; then
  echo -e "${RED}域名不能为空${NC}"
  exit 1
fi

# 开始更新
echo -e "${GREEN}========== 开始更新 ${APP_NAME} ===========${NC}"

# 前端更新
echo -e "${GREEN}========== 更新前端 ===========${NC}"
cd "$FRONTEND_DIR" || { echo -e "${RED}前端目录不存在${NC}"; exit 1; }

# 更新前端配置
echo -e "${GREEN}更新前端配置...${NC}"
cat > src/config.ts << EOF
export const config = {
  apiUrl: 'https://${DOMAIN}'
};
EOF

echo -e "${GREEN}安装前端依赖...${NC}"
npm install

echo -e "${GREEN}构建前端...${NC}"
npm run build

if [ $? -ne 0 ]; then
  echo -e "${RED}前端构建失败${NC}"
  exit 1
fi

# 复制前端文件到Nginx目录
echo -e "${GREEN}部署前端文件...${NC}"
sudo rm -rf /var/www/html/$APP_NAME/*
sudo cp -r $FRONTEND_DIR/dist/* /var/www/html/$APP_NAME/

# 后端更新
echo -e "${GREEN}========== 更新后端 ===========${NC}"
cd "$BACKEND_DIR" || { echo -e "${RED}后端目录不存在${NC}"; exit 1; }

echo -e "${GREEN}安装后端依赖...${NC}"
npm install

# 更新后端配置
echo -e "${GREEN}更新后端配置...${NC}"
cp .env.production .env

# 重启后端服务
echo -e "${GREEN}重启后端服务...${NC}"
pm2 delete $PM2_APP_NAME 2>/dev/null || true
pm2 start npm --name $PM2_APP_NAME -- start
pm2 save

# 完成更新
echo -e "${GREEN}========== 更新完成 ===========${NC}"
echo ""
echo -e "${GREEN}前端地址: https://$DOMAIN${NC}"
echo -e "${GREEN}API地址: https://$DOMAIN/api${NC}"
echo ""
echo -e "${YELLOW}重要提示:${NC}"
echo -e "1. PM2 日志路径: ~/.pm2/logs/"
echo -e "2. Nginx 日志路径: /var/log/nginx/"
echo ""
echo -e "${GREEN}更新成功！${NC}"
