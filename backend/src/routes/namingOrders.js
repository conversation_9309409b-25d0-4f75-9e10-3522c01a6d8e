import express from 'express';
import { SystemConfig, Order, Product } from '../models/index.js';
import { createPaymentUrl } from '../services/alipay.js';
import { generateOrderNumber } from '../utils/orderUtils.js';

const router = express.Router();

// 创建起名业务订单
router.post('/', async (req, res) => {
  try {
    const { customerName, baziReportId, serviceType = 'naming' } = req.body;

    // 验证必填字段
    if (!customerName) {
      return res.status(400).json({ 
        success: false, 
        message: '客户姓名为必填项' 
      });
    }

    // 获取起名业务配置
    const minPrice = await SystemConfig.getValue('naming_service_min_price', 99);
    const maxPrice = await SystemConfig.getValue('naming_service_max_price', 299);
    const commissionRate = await SystemConfig.getValue('naming_service_commission_rate', 20);
    const isEnabled = await SystemConfig.getValue('naming_service_enabled', true);

    if (!isEnabled) {
      return res.status(400).json({
        success: false,
        message: '起名业务暂时不可用'
      });
    }

    // 使用中间价格作为默认价格，保留小数点后2位
    const defaultPrice = Math.round((Number(minPrice) + Number(maxPrice)) / 2 * 100) / 100;

    // 查找起名业务产品
    const namingProduct = await Product.findOne({
      where: { type: 'naming', status: 'active' }
    });

    if (!namingProduct) {
      return res.status(500).json({
        success: false,
        message: '起名业务产品未配置'
      });
    }

    // 生成订单号
    const orderNo = generateOrderNumber();

    console.log('创建起名业务订单:', {
      orderNo,
      customerName,
      baziReportId,
      price: defaultPrice,
      productId: namingProduct.id
    });

    // 查找管理员用户作为默认代理
    const { User } = await import('../models/index.js');
    const adminUser = await User.findOne({
      where: { role: 'admin' }
    });

    console.log('查找管理员用户结果:', adminUser ? { id: adminUser.id, username: adminUser.username, role: adminUser.role } : null);

    if (!adminUser) {
      return res.status(500).json({
        success: false,
        message: '系统配置错误：找不到管理员用户'
      });
    }

    // 在数据库中创建订单记录
    const order = await Order.create({
      id: orderNo,
      productId: namingProduct.id,
      amount: defaultPrice,
      quantity: 1,
      unitPrice: defaultPrice,
      totalAmount: defaultPrice,
      orderType: 'service', // 起名业务也是服务类型
      status: 'pending',
      customerName,
      customerPhone: null, // 起名业务不需要手机号
      customerAddress: baziReportId ? `八字报告ID: ${baziReportId}` : null, // 将八字报告ID存储在地址字段
      agentId: adminUser.id, // 使用管理员作为默认代理
      parentAgentId: null,
      agentCommission: 0,
      parentAgentCommission: 0,
      adminCommission: defaultPrice * (Number(commissionRate) / 100)
    });

    console.log('订单创建成功:', {
      orderId: order.id,
      amount: order.amount,
      customerName: order.customerName
    });

    // 创建支付链接
    const paymentUrl = await createPaymentUrl({
      orderId: orderNo,
      amount: defaultPrice,
      productName: namingProduct.title,
      userAgent: req.headers['user-agent'] || '',
      agentId: null, // 起名业务暂时不支持代理
      isDeepseekCourse: false,
      isBaziService: false,
      isNamingService: true
    });

    if (!paymentUrl) {
      return res.status(500).json({
        success: false,
        message: '创建支付链接失败'
      });
    }

    res.json({
      success: true,
      data: {
        orderNo,
        paymentUrl,
        price: defaultPrice,
        priceRange: `${minPrice}-${maxPrice}`,
        message: '订单创建成功，请完成支付'
      }
    });

  } catch (error) {
    console.error('创建起名业务订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建订单失败',
      error: error.message
    });
  }
});

// 获取起名业务配置
router.get('/config', async (req, res) => {
  try {
    const config = {
      minPrice: await SystemConfig.getValue('naming_service_min_price', 99),
      maxPrice: await SystemConfig.getValue('naming_service_max_price', 299),
      commissionRate: await SystemConfig.getValue('naming_service_commission_rate', 20),
      isEnabled: await SystemConfig.getValue('naming_service_enabled', true)
    };

    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('获取起名业务配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
});

// 获取起名订单详情
router.get('/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    console.log('获取起名订单详情:', orderId);

    // 查询数据库中的订单信息
    const order = await Order.findOne({
      where: { id: orderId },
      include: [{
        model: Product,
        as: 'product'
      }]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: '订单不存在'
      });
    }

    // 从customerAddress字段中提取八字报告ID
    let baziReportId = null;
    if (order.customerAddress && order.customerAddress.includes('八字报告ID:')) {
      baziReportId = order.customerAddress.replace('八字报告ID: ', '');
    }

    const orderInfo = {
      orderId: order.id,
      customerName: order.customerName,
      amount: Number(order.amount),
      status: order.status,
      baziReportId,
      serviceType: 'naming',
      productTitle: order.product?.title || '专业起名服务',
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      paidAt: order.paidAt
    };

    res.json({
      success: true,
      data: orderInfo
    });
  } catch (error) {
    console.error('获取起名订单详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取订单详情失败',
      error: error.message
    });
  }
});

export default router;
