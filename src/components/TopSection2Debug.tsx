import React from 'react';

// 导入图片
const a1 = '/assets/theme/2/1.png';
const a3 = '/assets/theme/2/3.png';
const a4 = '/assets/theme/2/4.png';
const a5 = '/assets/theme/2/5.png';

export const TopSection2Debug: React.FC = () => {
  return (
    <div className="top-animate2" style={{ border: '2px solid red', margin: '10px' }}>
      <div className="inner" style={{ border: '2px solid blue', minHeight: '300px' }}>
        <img 
          className="bg" 
          src={a1} 
          alt="Background" 
          style={{ border: '2px solid green' }}
          onLoad={() => console.log('bg image loaded')}
          onError={() => console.log('bg image failed to load')}
        />
        <img 
          className="large" 
          src={a3} 
          alt="Large circle" 
          style={{ border: '2px solid yellow' }}
          onLoad={() => console.log('large image loaded')}
          onError={() => console.log('large image failed to load')}
        />
        <img 
          className="lite" 
          src={a4} 
          alt="Small circle" 
          style={{ border: '2px solid purple' }}
          onLoad={() => console.log('lite image loaded')}
          onError={() => console.log('lite image failed to load')}
        />
        <img 
          className="cover" 
          src={a5} 
          alt="Cover" 
          style={{ border: '2px solid orange' }}
          onLoad={() => console.log('cover image loaded')}
          onError={() => console.log('cover image failed to load')}
        />
      </div>
    </div>
  );
};
