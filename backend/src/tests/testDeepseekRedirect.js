/**
 * Deepseek 课程支付跳转测试脚本
 * 专门测试 Deepseek 课程支付成功后的跳转逻辑
 */
import fetch from 'node-fetch';
import { Order, ProductLink, User, Product, Commission } from '../models/index.js';
import sequelize from '../config/database.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

console.log('=== Deepseek 课程支付跳转测试 ===');
console.log('环境变量 PAID_RETURN_URL:', process.env.PAID_RETURN_URL);
console.log('环境变量 PAID_RETURN_URL2:', process.env.PAID_RETURN_URL2);

/**
 * 创建测试数据
 */
async function createTestData() {
  console.log('\n--- 创建测试数据 ---');
  
  // 1. 查找一个 Deepseek 产品
  const product = await Product.findOne({
    where: {
      title: {
        [sequelize.Op.like]: '%Deepseek%'
      }
    }
  });
  
  if (!product) {
    throw new Error('找不到 Deepseek 产品，请先添加产品');
  }
  
  console.log(`找到 Deepseek 产品: ${product.title}, ID: ${product.id}`);
  
  // 2. 查找一个二级代理
  const agent = await User.findOne({
    where: {
      level: 2
    },
    include: [{
      model: User,
      as: 'parentAgent'
    }]
  });
  
  if (!agent) {
    throw new Error('找不到二级代理用户');
  }
  
  console.log(`找到二级代理: ${agent.username}, ID: ${agent.id}`);
  if (agent.parentAgent) {
    console.log(`该代理的上级代理: ${agent.parentAgent.username}, ID: ${agent.parentAgent.id}`);
  }
  
  // 3. 创建产品推广链接
  const productLink = await ProductLink.create({
    productId: product.id,
    agentId: agent.id,
    code: `test_${Date.now()}`,
    sales: 0,
    totalCommission: 0
  });
  
  console.log(`创建产品推广链接成功: ${productLink.code}`);
  
  // 4. 创建订单
  const orderId = `TEST${Date.now()}`;
  const order = await Order.create({
    id: orderId,
    productLinkId: productLink.id,
    productId: product.id,
    amount: product.price,
    status: 'pending',
    agentId: agent.id,
    buyerName: '测试买家',
    buyerPhone: '13800001234',
    buyerEmail: '<EMAIL>'
  });
  
  console.log(`创建订单成功: ${order.id}`);
  
  return { product, agent, productLink, order };
}

/**
 * 测试支付成功跳转
 */
async function testPaymentRedirect(testData) {
  console.log('\n--- 测试支付成功跳转 ---');
  
  // 1. 获取订单详情，确保关联正确
  const order = await Order.findOne({
    where: { id: testData.order.id },
    include: [{
      model: ProductLink,
      as: 'productLink',
      include: [{
        model: Product,
        as: 'product'
      }, {
        model: User,
        as: 'linkAgent',
        include: [{
          model: User,
          as: 'parentAgent'
        }]
      }]
    }]
  });
  
  if (!order) {
    throw new Error(`找不到订单: ${testData.order.id}`);
  }
  
  console.log('订单关联数据获取成功:');
  console.log(`- 产品: ${order.productLink.product.title}`);
  console.log(`- 代理: ${order.productLink.linkAgent.username}`);
  
  // 2. 验证产品标题是否包含 Deepseek
  if (!order.productLink.product.title.includes('Deepseek')) {
    throw new Error(`产品标题不包含 Deepseek: ${order.productLink.product.title}`);
  }
  
  console.log('产品标题验证通过，确认为 Deepseek 课程');
  
  // 3. 模拟支付宝同步回调
  const params = {
    out_trade_no: order.id,
    trade_no: `test_payment_${Date.now()}`,
    total_amount: order.amount.toString(),
    seller_id: 'test_seller',
    app_id: 'test_app',
    sign: 'test_sign',
    sign_type: 'RSA2'
  };
  
  // 构建查询字符串
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  // 发送同步回调请求
  console.log(`发送同步回调请求: /payment/alipay/return?${queryString}`);
  
  const response = await fetch(`http://localhost:3005/payment/alipay/return?${queryString}`, {
    method: 'GET',
    redirect: 'manual' // 不自动跟随重定向
  });
  
  // 检查重定向URL
  const redirectUrl = response.headers.get('location');
  
  if (!redirectUrl) {
    throw new Error('没有获取到重定向URL');
  }
  
  console.log(`获取到重定向URL: ${redirectUrl}`);
  
  // 4. 验证重定向URL是否正确
  if (!redirectUrl.includes(process.env.PAID_RETURN_URL2)) {
    throw new Error(`重定向URL不正确，应该包含 ${process.env.PAID_RETURN_URL2}，但实际是 ${redirectUrl}`);
  }
  
  console.log('重定向URL验证通过，成功跳转到 PAID_RETURN_URL2');
  
  // 5. 检查订单状态
  const updatedOrder = await Order.findByPk(order.id);
  
  if (updatedOrder.status !== 'paid') {
    throw new Error(`订单状态不正确: ${updatedOrder.status}`);
  }
  
  console.log('订单状态已更新为 paid');
  
  // 6. 检查佣金记录
  const commissions = await Commission.findAll({
    where: { orderId: order.id }
  });
  
  if (commissions.length === 0) {
    throw new Error('没有创建佣金记录');
  }
  
  console.log(`佣金记录创建成功，共 ${commissions.length} 条记录`);
  commissions.forEach((comm, index) => {
    console.log(`佣金记录 ${index + 1}: 代理ID ${comm.agentId}, 金额 ${comm.amount}, 类型 ${comm.type}`);
  });
  
  // 7. 检查推广链接销量和佣金
  const updatedLink = await ProductLink.findByPk(testData.productLink.id);
  
  if (updatedLink.sales !== 1) {
    throw new Error(`推广链接销量不正确: ${updatedLink.sales}`);
  }
  
  if (updatedLink.totalCommission <= 0) {
    throw new Error(`推广链接总佣金不正确: ${updatedLink.totalCommission}`);
  }
  
  console.log(`推广链接销量已更新: ${updatedLink.sales}`);
  console.log(`推广链接总佣金已更新: ${updatedLink.totalCommission}`);
  
  return { updatedOrder, commissions, updatedLink };
}

/**
 * 清理测试数据
 */
async function cleanupTestData(testData) {
  console.log('\n--- 清理测试数据 ---');
  
  // 1. 删除佣金记录
  await Commission.destroy({
    where: { orderId: testData.order.id }
  });
  console.log(`删除佣金记录: 订单ID ${testData.order.id}`);
  
  // 2. 删除订单
  await testData.order.destroy();
  console.log(`删除测试订单: ${testData.order.id}`);
  
  // 3. 删除产品推广链接
  await testData.productLink.destroy();
  console.log(`删除测试产品推广链接: ${testData.productLink.code}`);
  
  console.log('测试数据清理完成');
}

/**
 * 运行测试
 */
async function runTests() {
  let testData = null;
  
  try {
    // 创建测试数据
    testData = await createTestData();
    
    // 测试支付成功跳转
    await testPaymentRedirect(testData);
    
    console.log('\n=== 测试通过 ===');
    console.log('Deepseek 课程支付跳转功能正常工作！');
    
  } catch (error) {
    console.error('\n=== 测试失败 ===');
    console.error(error);
  } finally {
    // 清理测试数据
    if (testData) {
      await cleanupTestData(testData);
    }
    
    process.exit(0);
  }
}

// 运行测试
runTests();
