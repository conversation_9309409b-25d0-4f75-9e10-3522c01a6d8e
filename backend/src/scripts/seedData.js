import { User, Product, Order, Commission } from '../models/index.js';
import sequelize from '../config/database.js';
import { generateOrderNumber } from '../utils/orderUtils.js';

const seedData = async () => {
  try {
    console.log('开始填充初始数据...');

    // 创建管理员账号
    let admin = await User.findOne({ where: { username: 'admin' } });
    if (!admin) {
      admin = await User.create({
        username: 'admin1',
        password: 'admin123',
        name: '测试管理员',
        phone: '13800000000',
        role: 'admin',
        status: 'approved',
        level: 0,
        commissionRate: null
      });
      console.log('管理员账号创建成功:', admin.username);
    } else {
      console.log('管理员账号已存在，跳过创建');
    }

    // 创建示例产品
    const products = await Product.bulkCreate([
      {
        title: '示例产品1',
        description: '这是一个示例产品1的描述',
        price: 99.99,
        baseCommissionRate: 10,
        status: 'active'
      },
      {
        title: '示例产品2',
        description: '这是一个示例产品2的描述',
        price: 199.99,
        baseCommissionRate: 15,
        status: 'active'
      }
    ], {
      ignoreDuplicates: true
    });
    console.log('示例产品创建成功:', products.length, '个');

    // 创建一级代理
    let agent1 = await User.findOne({ where: { username: 'agent1' } });
    if (!agent1) {
      agent1 = await User.create({
        username: 'agent1',
        password: 'password123',
        name: '一级代理A',
        phone: '13800000001',
        role: 'user',
        status: 'approved',
        level: 1,
        commissionRate: 30
      });
      console.log('一级代理创建成功:', agent1.username);
    } else {
      console.log('一级代理已存在，跳过创建');
    }

    // 创建二级代理
    let agent2 = await User.findOne({ where: { username: 'agent2' } });
    if (!agent2) {
      agent2 = await User.create({
        username: 'agent2',
        password: 'password123',
        name: '二级代理A',
        phone: '13800000002',
        role: 'user',
        status: 'approved',
        level: 2,
        commissionRate: 20,
        parentAgentId: agent1.id
      });
      console.log('二级代理创建成功:', agent2.username);
    } else {
      console.log('二级代理已存在，跳过创建');
    }

    // 如果需要创建示例订单和佣金记录
    if (products.length > 0 && agent1 && agent2) {
      // 创建示例订单
      const order1 = await Order.create({
        id: generateOrderNumber(),  // 使用生成的订单号
        amount: 99.99,
        status: 'paid',  // 修改为有效的状态值
        productId: products[0].id,
        agentId: agent2.id,
        parentAgentId: agent1.id,
        agentCommission: 10,
        parentAgentCommission: 5,
        customerName: '测试客户1',
        customerPhone: '13900000001',
        paidAt: new Date()
      });
      console.log('示例订单创建成功:', order1.id);

      // 创建佣金记录
      const commission1 = await Commission.create({
        amount: order1.agentCommission,
        status: 'pending',
        orderId: order1.id,
        agentId: agent2.id
      });
      console.log('二级代理佣金记录创建成功:', commission1.id);

      const commission2 = await Commission.create({
        amount: order1.parentAgentCommission,
        status: 'pending',
        orderId: order1.id,
        agentId: agent1.id
      });
      console.log('一级代理佣金记录创建成功:', commission2.id);
    }

    console.log('\n初始数据填充完成！');
    console.log('\n以下是测试账号信息：');
    console.log('管理员：admin / admin123');
    console.log('一级代理：agent1 / password123');
    console.log('二级代理：agent2 / password123');
    
  } catch (error) {
    console.error('数据填充失败:', error);
  } finally {
    await sequelize.close();
    process.exit();
  }
};

// 等待3秒后开始执行
console.log('3秒后开始填充数据...');
setTimeout(seedData, 3000);