#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m'

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}请使用root权限运行此脚本${NC}"
    exit 1
fi

# 读取域名
read -p "请输入您的域名（例如：example.com）: " DOMAIN
if [ -z "$DOMAIN" ]; then
    echo -e "${RED}域名不能为空${NC}"
    exit 1
fi

echo -e "${GREEN}开始安装SSL证书...${NC}"

# 安装Certbot
echo -e "${GREEN}安装Certbot...${NC}"
apt-get update
apt-get install -y certbot python3-certbot-nginx

# 获取SSL证书
echo -e "${GREEN}获取SSL证书...${NC}"
certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN

if [ $? -ne 0 ]; then
    echo -e "${RED}SSL证书获取失败${NC}"
    exit 1
fi

# 备份原有的Nginx配置
echo -e "${GREEN}备份Nginx配置...${NC}"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
cp /etc/nginx/conf.d/commission-platform.conf "/etc/nginx/conf.d/commission-platform.conf.backup.$TIMESTAMP"

# 更新Nginx配置
echo -e "${GREEN}更新Nginx配置...${NC}"
cat > /etc/nginx/conf.d/commission-platform.conf << EOF
server {
    listen 80;
    server_name $DOMAIN;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;

    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_trusted_certificate /etc/letsencrypt/live/$DOMAIN/chain.pem;

    # SSL配置
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # 现代配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # HSTS (uncomment if you're sure)
    # add_header Strict-Transport-Security "max-age=63072000" always;

    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;

    # 前端静态文件
    location / {
        root /var/www/html/commission-platform;
        index index.html;
        try_files \$uri \$uri/ /index.html;

        # 缓存配置
        expires 7d;
        add_header Cache-Control "public, no-transform";
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";
        add_header Referrer-Policy "strict-origin-when-cross-origin";
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:3005/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # API请求超时设置
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
        proxy_read_timeout 60;
    }

    # 日志配置
    access_log /var/log/nginx/commission-platform.access.log;
    error_log /var/log/nginx/commission-platform.error.log;

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
}
EOF

# 测试Nginx配置
echo -e "${GREEN}测试Nginx配置...${NC}"
nginx -t

if [ $? -ne 0 ]; then
    echo -e "${RED}Nginx配置测试失败${NC}"
    echo -e "${GREEN}正在还原备份...${NC}"
    cp "/etc/nginx/conf.d/commission-platform.conf.backup.$TIMESTAMP" /etc/nginx/conf.d/commission-platform.conf
    exit 1
fi

# 重启Nginx
echo -e "${GREEN}重启Nginx...${NC}"
systemctl restart nginx

# 设置自动续期
echo -e "${GREEN}配置证书自动续期...${NC}"
(crontab -l 2>/dev/null; echo "0 3 * * * /usr/bin/certbot renew --quiet") | crontab -

echo -e "${GREEN}SSL证书安装完成！${NC}"
echo -e "${GREEN}您的网站现在可以通过 https://$DOMAIN 访问${NC}"

# 显示证书信息
echo -e "${GREEN}证书信息：${NC}"
certbot certificates
