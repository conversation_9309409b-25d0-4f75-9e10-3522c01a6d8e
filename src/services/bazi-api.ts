import CryptoJS from 'crypto-js';
import type { BaZiReportData, FullBaZiData } from '@/types/bazi';

interface BaZiApiParams {
  address: string;
  day: string;
  hour: string;
  ming: string;
  minute: string;
  month: string;
  sex: '1' | '2';
  xing: string;
  year: string;
  yearType: '1' | '2';
}

export async function fetchBaZiReport(params: BaZiApiParams): Promise<BaZiReportData> {
  // 在前端环境中，我们通过后端API来调用腾讯云API
  // 这样可以保护API密钥，避免在前端暴露敏感信息
  
  try {
    // 调用我们自己的后端API
    const response = await fetch('/api/bazi/report', {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const errorBody = await response.text();
      console.error("API Error Response:", errorBody);
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (!result.success || result.code !== 0) {
      throw new Error(`API returned an error: ${result.msg || 'Unknown error'} (code: ${result.code || 'unknown'})`);
    }
    
    // 后端返回的数据结构是 { success: true, data: { success: true, data: { 命主信息: ... } } }
    // 我们需要提取内层的data
    if (result.data && result.data.data) {
      return result.data.data;
    } else {
      throw new Error('Invalid response structure from backend');
    }

  } catch (error) {
    console.error("Error fetching BaZi report:", error);
    if (error instanceof Error) {
      throw new Error(`Failed to fetch BaZi report: ${error.message}`);
    }
    throw new Error("An unknown error occurred while fetching the BaZi report.");
  }
} 