'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 删除主键约束
    await queryInterface.removeConstraint('Orders', 'PRIMARY');

    // 修改 id 字段的长度为 20
    await queryInterface.changeColumn('Orders', 'id', {
      type: Sequelize.STRING(20),
      allowNull: false
    });

    // 重新添加主键约束
    await queryInterface.addConstraint('Orders', {
      fields: ['id'],
      type: 'primary key',
      name: 'Orders_pkey'
    });
  },

  async down(queryInterface, Sequelize) {
    // 删除主键约束
    await queryInterface.removeConstraint('Orders', 'Orders_pkey');

    // 恢复 id 字段的长度为 16
    await queryInterface.changeColumn('Orders', 'id', {
      type: Sequelize.STRING(16),
      allowNull: false
    });

    // 重新添加主键约束
    await queryInterface.addConstraint('Orders', {
      fields: ['id'],
      type: 'primary key',
      name: 'PRIMARY'
    });
  }
};
