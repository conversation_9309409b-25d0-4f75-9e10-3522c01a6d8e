import { Order, BaziOrder, Product, User, ProductLink } from './src/models/index.js';
import sequelize from './src/config/database.js';

async function checkBaziOrderStatus() {
  try {
    console.log('🔍 检查八字命盘订单状态...\n');
    
    // 查找所有八字订单
    const baziOrders = await BaziOrder.findAll({
      include: [{
        model: Order,
        as: 'order',
        include: [{
          model: Product,
          as: 'product'
        }, {
          model: ProductLink,
          as: 'productLink',
          include: [{
            model: Product,
            as: 'product'
          }]
        }]
      }],
      order: [['createdAt', 'DESC']],
      limit: 5
    });
    
    console.log(`找到 ${baziOrders.length} 个八字订单:\n`);
    
    baziOrders.forEach((baziOrder, index) => {
      const order = baziOrder.order;
      console.log(`${index + 1}. 八字订单 ID: ${baziOrder.id}`);
      console.log(`   关联订单 ID: ${baziOrder.orderId}`);
      console.log(`   客户姓名: ${baziOrder.name}`);
      console.log(`   报告状态: ${baziOrder.reportStatus}`);

      if (order) {
        console.log(`   订单支付状态: ${order.status}`);
        console.log(`   订单金额: ${order.amount}`);
        console.log(`   支付时间: ${order.paidAt || '未支付'}`);
        console.log(`   创建时间: ${order.createdAt}`);

        // 检查产品信息
        if (order.product) {
          console.log(`   产品标题: ${order.product.title}`);
          console.log(`   产品类型: ${order.product.type}`);
        } else if (order.productLink?.product) {
          console.log(`   产品标题: ${order.productLink.product.title}`);
          console.log(`   产品类型: ${order.productLink.product.type}`);
        } else {
          console.log(`   ⚠️  产品信息缺失`);
        }
      } else {
        console.log(`   ❌ 关联订单不存在!`);
      }
      console.log('');
    });
    
    // 统计状态分布
    const statusStats = await BaziOrder.findAll({
      include: [{
        model: Order,
        as: 'order',
        attributes: ['status']
      }],
      attributes: ['reportStatus', [sequelize.fn('COUNT', sequelize.col('BaziOrder.id')), 'count']],
      group: ['reportStatus', 'order.status'],
      raw: true
    });
    
    console.log('📊 状态统计:');
    statusStats.forEach(stat => {
      console.log(`   报告状态: ${stat.reportStatus}, 订单状态: ${stat['order.status']}, 数量: ${stat.count}`);
    });
    
  } catch (error) {
    console.error('检查失败:', error);
  } finally {
    process.exit(0);
  }
}

checkBaziOrderStatus();
