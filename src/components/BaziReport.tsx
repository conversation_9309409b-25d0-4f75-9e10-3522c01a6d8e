import React from 'react';
import {
  Card,
  Descriptions,
  Typography,
  Row,
  Col,
  Tag,
  Progress,
  Divider,
  Space,
  Alert,
  Table
} from 'antd';
import {
  UserOutlined,
  CalendarOutlined,
  StarOutlined,
  BookOutlined,
  FireOutlined,
  HeartOutlined
} from '@ant-design/icons';
import { BaZiReportData } from '../types/bazi';

const { Title, Paragraph, Text } = Typography;

interface BaziReportProps {
  data: BaZiReportData;
  customerName?: string;
}

const BaziReport: React.FC<BaziReportProps> = ({ data, customerName }) => {
  // 命主信息组件
  const MingZhuInfoSection = () => (
    <Card 
      title={
        <span>
          <UserOutlined style={{ marginRight: '8px' }} />
          命主信息
        </span>
      }
      style={{ marginBottom: '24px' }}
    >
      <Descriptions column={{ xs: 1, sm: 2, md: 3 }} bordered>
        <Descriptions.Item label="姓名">{data.命主信息.姓名}</Descriptions.Item>
        <Descriptions.Item label="性别">{data.命主信息.性别}</Descriptions.Item>
        <Descriptions.Item label="生肖">{data.命主信息.生肖}</Descriptions.Item>
        <Descriptions.Item label="星座">{data.命主信息.星座}</Descriptions.Item>
        <Descriptions.Item label="公历生日">{data.命主信息.公历生日}</Descriptions.Item>
        <Descriptions.Item label="农历生日">{data.命主信息.农历生日}</Descriptions.Item>
        <Descriptions.Item label="出生地" span={3}>{data.命主信息.出生地}</Descriptions.Item>
      </Descriptions>
    </Card>
  );

  // 八字称骨信息组件
  const ChengGuInfoSection = () => (
    <Card 
      title={
        <span>
          <StarOutlined style={{ marginRight: '8px' }} />
          八字称骨信息
        </span>
      }
      style={{ marginBottom: '24px' }}
    >
      <Row gutter={24}>
        <Col xs={24} md={12}>
          <Descriptions column={1} bordered>
            <Descriptions.Item label="总骨重">{data.命主八字称骨信息.总骨重}</Descriptions.Item>
            <Descriptions.Item label="年骨重">{data.命主八字称骨信息.年骨重}</Descriptions.Item>
            <Descriptions.Item label="月骨重">{data.命主八字称骨信息.月骨重}</Descriptions.Item>
            <Descriptions.Item label="日骨重">{data.命主八字称骨信息.日骨重}</Descriptions.Item>
            <Descriptions.Item label="时骨重">{data.命主八字称骨信息.时骨重}</Descriptions.Item>
          </Descriptions>
        </Col>
        <Col xs={24} md={12}>
          <Card size="small" style={{ backgroundColor: '#f6ffed' }}>
            <Title level={4} style={{ color: '#389e0d' }}>称骨歌</Title>
            <Paragraph style={{ whiteSpace: 'pre-line', color: '#333' }}>
              {data.命主八字称骨信息.称骨歌}
            </Paragraph>
          </Card>
        </Col>
      </Row>
    </Card>
  );

  // 姓名数理信息组件
  const XingMingShuLiSection = () => {
    const shuliData = [
      { name: '天格', ...data.命主姓名数理信息.天格 },
      { name: '人格', ...data.命主姓名数理信息.人格 },
      { name: '地格', ...data.命主姓名数理信息.地格 },
      { name: '总格', ...data.命主姓名数理信息.总格 },
      { name: '外格', ...data.命主姓名数理信息.外格 },
    ];

    const columns = [
      {
        title: '格局',
        dataIndex: 'name',
        key: 'name',
        width: 80,
      },
      {
        title: '数字',
        dataIndex: '数字',
        key: 'number',
        width: 80,
      },
      {
        title: '吉凶',
        dataIndex: '吉凶',
        key: 'luck',
        width: 80,
        render: (text: string) => (
          <Tag color={text.includes('吉') ? 'green' : 'red'}>{text}</Tag>
        ),
      },
      {
        title: '含义',
        dataIndex: '含义',
        key: 'meaning',
        ellipsis: true,
      },
    ];

    return (
      <Card 
        title={
          <span>
            <BookOutlined style={{ marginRight: '8px' }} />
            姓名数理信息
          </span>
        }
        style={{ marginBottom: '24px' }}
      >
        <Table 
          dataSource={shuliData} 
          columns={columns} 
          pagination={false}
          size="small"
          rowKey="name"
        />
      </Card>
    );
  };

  // 八字命盘信息组件
  const MingPanInfoSection = () => (
    <Card 
      title={
        <span>
          <CalendarOutlined style={{ marginRight: '8px' }} />
          八字命盘信息
        </span>
      }
      style={{ marginBottom: '24px' }}
    >
      <Row gutter={24}>
        <Col xs={24} lg={12}>
          <Title level={4}>八字信息</Title>
          <Descriptions column={1} bordered size="small">
            <Descriptions.Item label="年柱">{data.命主八字命盘信息.八字信息.年柱}</Descriptions.Item>
            <Descriptions.Item label="月柱">{data.命主八字命盘信息.八字信息.月柱}</Descriptions.Item>
            <Descriptions.Item label="日柱">{data.命主八字命盘信息.八字信息.日柱}</Descriptions.Item>
            <Descriptions.Item label="时柱">{data.命主八字命盘信息.八字信息.时柱}</Descriptions.Item>
          </Descriptions>
        </Col>
        <Col xs={24} lg={12}>
          <Title level={4}>十神信息</Title>
          <Descriptions column={1} bordered size="small">
            {Object.entries(data.命主八字命盘信息.十神信息).map(([key, value]) => (
              <Descriptions.Item key={key} label={key}>{value}</Descriptions.Item>
            ))}
          </Descriptions>
        </Col>
      </Row>
      
      <Divider />
      
      <Title level={4}>命理综述</Title>
      <Alert
        message="八字命理分析"
        description={data.命主八字命盘信息.八字信息.命理综述}
        type="info"
        showIcon
        style={{ marginTop: '16px' }}
      />
    </Card>
  );

  // 五行喜神信息组件
  const WuXingXiShenSection = () => {
    const wuxingColors = {
      '金': '#FFD700',
      '木': '#228B22',
      '水': '#4169E1',
      '火': '#FF4500',
      '土': '#8B4513'
    };

    return (
      <Card 
        title={
          <span>
            <FireOutlined style={{ marginRight: '8px' }} />
            五行喜神信息
          </span>
        }
        style={{ marginBottom: '24px' }}
      >
        <Row gutter={24}>
          <Col xs={24} md={12}>
            <Title level={4}>五行统计</Title>
            <Space direction="vertical" style={{ width: '100%' }}>
              {Object.entries(data.命主五行和喜神信息.五行统计).map(([element, count]) => (
                <div key={element} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                  <Text style={{ minWidth: '40px', color: wuxingColors[element as keyof typeof wuxingColors] }}>
                    {element}:
                  </Text>
                  <Progress 
                    percent={(count / 8) * 100} 
                    size="small" 
                    strokeColor={wuxingColors[element as keyof typeof wuxingColors]}
                    format={() => count}
                    style={{ flex: 1, marginLeft: '8px' }}
                  />
                </div>
              ))}
            </Space>
          </Col>
          <Col xs={24} md={12}>
            <Title level={4}>喜神分析</Title>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label="喜用神">
                <Tag color="green">{data.命主五行和喜神信息.喜用神}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="忌神">
                <Tag color="red">{data.命主五行和喜神信息.忌神}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="五行缺失">
                <Space>
                  {data.命主五行和喜神信息.五行缺失.map(element => (
                    <Tag key={element} color="orange">{element}</Tag>
                  ))}
                  {data.命主五行和喜神信息.五行缺失.length === 0 && (
                    <Tag color="blue">五行齐全</Tag>
                  )}
                </Space>
              </Descriptions.Item>
            </Descriptions>
            
            <Title level={5} style={{ marginTop: '16px' }}>改运建议</Title>
            <Alert
              message={data.命主五行和喜神信息.建议}
              type="success"
              showIcon
            />
          </Col>
        </Row>
      </Card>
    );
  };

  // 日干论命信息组件
  const RiGanLunMingSection = () => (
    <Card 
      title={
        <span>
          <HeartOutlined style={{ marginRight: '8px' }} />
          日干论命信息
        </span>
      }
      style={{ marginBottom: '24px' }}
    >
      <Row gutter={24}>
        <Col span={24}>
          <Title level={4}>
            日干：<Tag color="blue" style={{ fontSize: '16px' }}>{data.命主日干论命信息.日干}</Tag>
          </Title>
        </Col>
      </Row>
      
      <Row gutter={24}>
        <Col xs={24} lg={12}>
          <Card size="small" title="性格特点" style={{ marginBottom: '16px' }}>
            <Paragraph>{data.命主日干论命信息.性格特点}</Paragraph>
          </Card>
          
          <Card size="small" title="事业运势" style={{ marginBottom: '16px' }}>
            <Paragraph>{data.命主日干论命信息.事业运势}</Paragraph>
          </Card>
          
          <Card size="small" title="财运分析">
            <Paragraph>{data.命主日干论命信息.财运分析}</Paragraph>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card size="small" title="感情运势" style={{ marginBottom: '16px' }}>
            <Paragraph>{data.命主日干论命信息.感情运势}</Paragraph>
          </Card>
          
          <Card size="small" title="健康状况" style={{ marginBottom: '16px' }}>
            <Paragraph>{data.命主日干论命信息.健康状况}</Paragraph>
          </Card>
          
          <Card size="small" title="总体建议">
            <Alert
              message={data.命主日干论命信息.总体建议}
              type="info"
              showIcon
            />
          </Card>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ textAlign: 'center', marginBottom: '32px' }}>
        <Title level={1} style={{ color: '#1890ff' }}>
          {customerName || data.命主信息.姓名} 命盘报告
        </Title>
        <Paragraph style={{ fontSize: '16px', color: '#666' }}>
          公历生日: {data.命主信息.公历生日}
        </Paragraph>
      </div>

      <MingZhuInfoSection />
      <ChengGuInfoSection />
      <XingMingShuLiSection />
      <MingPanInfoSection />
      <WuXingXiShenSection />
      <RiGanLunMingSection />

      <Card style={{ textAlign: 'center', backgroundColor: '#fafafa' }}>
        <Paragraph style={{ color: '#999', margin: 0 }}>
          仅供参考，命运掌握在自己手中
        </Paragraph>
        <Paragraph style={{ color: '#999', fontSize: '12px', margin: 0 }}>
          &copy; {new Date().getFullYear()} 八字命盘. All rights reserved.
        </Paragraph>
      </Card>
    </div>
  );
};

export default BaziReport; 