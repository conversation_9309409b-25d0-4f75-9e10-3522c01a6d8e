#!/usr/bin/env node
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// 服务器信息
const SERVER_IP = '************';
const SERVER_USER = 'root';

async function testServerConnection() {
  console.log('🔍 开始检测服务器状态...');
  console.log('=' .repeat(50));

  try {
    // 1. 测试服务器连通性
    console.log('\n1. 测试服务器连通性');
    try {
      const { stdout } = await execAsync(`ping -c 1 ${SERVER_IP}`);
      console.log('✅ 服务器连通正常');
    } catch (error) {
      console.log('❌ 服务器连通失败:', error.message);
      return;
    }

    // 2. 测试HTTPS端口
    console.log('\n2. 测试HTTPS端口 (443)');
    try {
      const { stdout } = await execAsync(`nc -z -v ${SERVER_IP} 443`);
      console.log('✅ HTTPS端口 443 开放');
    } catch (error) {
      console.log('❌ HTTPS端口 443 不可达');
    }

    // 3. 测试HTTP端口
    console.log('\n3. 测试HTTP端口 (80)');
    try {
      const { stdout } = await execAsync(`nc -z -v ${SERVER_IP} 80`);
      console.log('✅ HTTP端口 80 开放');
    } catch (error) {
      console.log('❌ HTTP端口 80 不可达');
    }

    // 4. 测试后端端口
    console.log('\n4. 测试后端端口 (3005)');
    try {
      const { stdout } = await execAsync(`nc -z -v ${SERVER_IP} 3005`);
      console.log('✅ 后端端口 3005 开放');
    } catch (error) {
      console.log('❌ 后端端口 3005 不可达');
    }

    // 5. 测试Nginx配置
    console.log('\n5. 测试Nginx响应');
    try {
      const { stdout } = await execAsync(`curl -s -I https://${SERVER_IP}`);
      console.log('✅ Nginx响应正常:');
      console.log(stdout.split('\n').slice(0, 3).join('\n'));
    } catch (error) {
      console.log('❌ Nginx响应异常:', error.message);
    }

    // 6. 测试API路径
    console.log('\n6. 测试API路径');
    const apiPaths = [
      '/api',
      '/api/auth',
      '/api/auth/login'
    ];

    for (const path of apiPaths) {
      try {
        const { stdout } = await execAsync(`curl -s -I https://ye.bzcy.xyz${path}`);
        const statusLine = stdout.split('\n')[0];
        const statusCode = statusLine.split(' ')[1];
        
        if (statusCode === '404') {
          console.log(`❌ ${path}: ${statusCode} Not Found`);
        } else if (statusCode === '405') {
          console.log(`✅ ${path}: ${statusCode} Method Not Allowed (路由存在)`);
        } else {
          console.log(`ℹ️  ${path}: ${statusCode} ${statusLine.split(' ').slice(2).join(' ')}`);
        }
      } catch (error) {
        console.log(`❌ ${path}: 请求失败`);
      }
    }

    // 7. 建议解决方案
    console.log('\n' + '=' .repeat(50));
    console.log('🔧 建议解决方案:');
    console.log('\n1. 检查后端服务状态:');
    console.log('   ssh root@************ "pm2 status"');
    console.log('\n2. 检查后端服务日志:');
    console.log('   ssh root@************ "pm2 logs commission-platform-api"');
    console.log('\n3. 重启后端服务:');
    console.log('   ssh root@************ "pm2 restart commission-platform-api"');
    console.log('\n4. 检查Nginx配置:');
    console.log('   ssh root@************ "nginx -t && systemctl status nginx"');
    console.log('\n5. 检查端口监听:');
    console.log('   ssh root@************ "netstat -tlnp | grep :3005"');

  } catch (error) {
    console.error('❌ 检测过程中发生错误:', error.message);
  }
}

// 运行检测
testServerConnection();