import React from 'react';

interface RadioProps {
  name: string;
  value: string;
  checked?: boolean;
  onChange?: (value: string) => void;
  children: React.ReactNode;
}

export const Radio: React.FC<RadioProps> = ({
  name,
  value,
  checked,
  onChange,
  children,
}) => {
  return (
    <label
      className="radio-item"
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        cursor: 'pointer',
      }}
    >
      <div
        className={`radio-circle ${checked ? 'checked' : ''}`}
        style={{
          width: '16px',
          height: '16px',
          borderRadius: '50%',
          border: '2px solid #b87c3d',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: checked ? '#b87c3d' : 'transparent',
        }}
      >
        {checked && (
          <div
            style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: 'white',
            }}
          />
        )}
      </div>
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={(e) => onChange?.(e.target.value)}
        style={{ display: 'none' }}
      />
      <span style={{ fontSize: '15px', color: '#333' }}>{children}</span>
    </label>
  );
};

interface RadioGroupProps {
  value: string;
  onChange: (value: string) => void;
  children: React.ReactNode;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({
  value,
  onChange,
  children,
}) => {
  return (
    <div
      className="radio-group"
      style={{
        display: 'flex',
        gap: '30px',
      }}
    >
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === Radio) {
          return React.cloneElement(child as React.ReactElement<RadioProps>, {
            checked: child.props.value === value,
            onChange,
          });
        }
        return child;
      })}
    </div>
  );
};
