import AlipaySdk from 'alipay-sdk';
import AlipayFormData from 'alipay-sdk/lib/form.js';
import dotenv from 'dotenv';
import { PaymentConfig, User, SystemConfig } from '../models/index.js';

dotenv.config();

// 默认使用系统配置的SDK
const getSystemAlipaySdk = () => {
  console.log('初始化系统支付宝SDK，环境:', process.env.NODE_ENV);
  
  // 确保环境变量已正确加载
  if (!process.env.ALIPAY_APPID || !process.env.ALIPAY_PRIVATE_KEY || !process.env.ALIPAY_PUBLIC_KEY) {
    console.error('支付宝配置不完整，请检查环境变量');
    console.log('ALIPAY_APPID存在:', !!process.env.ALIPAY_APPID);
    console.log('ALIPAY_PRIVATE_KEY存在:', !!process.env.ALIPAY_PRIVATE_KEY);
    console.log('ALIPAY_PUBLIC_KEY存在:', !!process.env.ALIPAY_PUBLIC_KEY);
  }
  
  return new AlipaySdk({
    appId: process.env.ALIPAY_APPID,
    privateKey: process.env.ALIPAY_PRIVATE_KEY,
    gateway: 'https://openapi.alipay.com/gateway.do',
    alipayPublicKey: process.env.ALIPAY_PUBLIC_KEY,
  });
};

// 根据代理ID获取支付配置
const getAgentPaymentConfig = async (agentId) => {
  try {
    // 如果提供了代理ID，尝试获取该代理的支付配置
    if (agentId) {
      // 首先查找代理用户信息
      const agent = await User.findByPk(agentId);
      
      // 确保是一级代理
      if (agent && agent.role === 'user' && agent.level === 1) {
        // 获取该代理的支付配置
        const config = await PaymentConfig.findOne({
          where: { 
            userId: agentId,
            isActive: true 
          }
        });
        
        // 如果存在有效配置，则使用该配置
        if (config) {
          console.log(`使用代理 ${agentId} 的个人支付配置`);
          return {
            sdk: new AlipaySdk({
              appId: config.alipayAppId,
              privateKey: config.alipayPrivateKey,
              gateway: 'https://openapi.alipay.com/gateway.do',
              alipayPublicKey: config.alipayPublicKey,
            }),
            returnUrl: process.env.ALIPAY_RETURN_URL,
            notifyUrl: process.env.ALIPAY_NOTIFY_URL
          };
        }
      }
      
      // 如果是二级代理，尝试使用其上级代理的配置
      if (agent && agent.role === 'user' && agent.level === 2 && agent.parentAgentId) {
        // 获取上级代理的支付配置
        const config = await PaymentConfig.findOne({
          where: { 
            userId: agent.parentAgentId,
            isActive: true 
          }
        });
        
        // 如果存在有效配置，则使用该配置
        if (config) {
          console.log(`使用二级代理 ${agentId} 的上级代理 ${agent.parentAgentId} 的支付配置`);
          return {
            sdk: new AlipaySdk({
              appId: config.alipayAppId,
              privateKey: config.alipayPrivateKey,
              gateway: 'https://openapi.alipay.com/gateway.do',
              alipayPublicKey: config.alipayPublicKey,
            }),
            returnUrl: process.env.ALIPAY_RETURN_URL,
            notifyUrl: process.env.ALIPAY_NOTIFY_URL
          };
        }
      }
    }
    
    // 如果无法获取代理配置，则使用系统默认配置
    console.log('使用系统默认支付配置');
    return {
      sdk: getSystemAlipaySdk(),
      returnUrl: process.env.ALIPAY_RETURN_URL,
      notifyUrl: process.env.ALIPAY_NOTIFY_URL
    };
  } catch (error) {
    console.error('获取支付配置失败:', error);
    // 出错时使用系统默认配置
    return {
      sdk: getSystemAlipaySdk(),
      returnUrl: process.env.ALIPAY_RETURN_URL,
      notifyUrl: process.env.ALIPAY_NOTIFY_URL
    };
  }
};

export const createPaymentUrl = async ({ orderId, amount, productName, userAgent, agentId, isDeepseekCourse, isBaziService, isNamingService }) => {
  try {
    console.log('创建支付链接，参数:', { orderId, amount, productName, userAgent, agentId, isDeepseekCourse, isBaziService, isNamingService });
    
    // 获取适用的支付SDK和配置
    const { sdk: alipaySdk, returnUrl, notifyUrl } = await getAgentPaymentConfig(agentId);
    
    const formData = new AlipayFormData();
    
    // 检测是否为移动设备
    const isMobile = /Mobile|Android|iPhone/i.test(userAgent);
    console.log('设备类型:', isMobile ? '移动端' : 'PC端');

    // 设置请求参数
    formData.setMethod('get');
    
    // 根据设备类型设置不同的支付产品
    const method = isMobile ? 'alipay.trade.wap.pay' : 'alipay.trade.page.pay';
    
    // 根据产品类型选择正确的跳转地址
    let productSpecificReturnUrl;
    let productTypeDesc;

    if (isNamingService) {
      // 起名业务直接跳转到企业微信客服页面
      const frontendUrl = process.env.FRONTEND_URL || 'https://ye.bzcy.xyz';

      // 从起名业务订单号中提取baziReportId
      let baziReportId = orderId;
      if (orderId.startsWith('NAMING')) {
        // 订单号格式：NAMING{baziReportId}{timestamp}
        const match = orderId.match(/^NAMING(.+?)(\d{6})$/);
        if (match && match[1]) {
          baziReportId = match[1];
          console.log('【支付链接创建】从起名订单号提取八字报告ID:', baziReportId);
        }
      }

      const baziReportUrl = `${frontendUrl}/bazi-report?orderId=${baziReportId}`;
      const message = `您好！我已完成起名服务支付，这是我的八字报告链接：${baziReportUrl}，请为我提供专业的起名服务。订单号：${orderId}`;
      const encodedMessage = encodeURIComponent(message);
      productSpecificReturnUrl = `https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd?msg=${encodedMessage}`;
      productTypeDesc = '起名业务服务';
      console.log('【支付链接创建】起名业务跳转到企业微信客服，消息:', message);
    } else if (isBaziService) {
      productSpecificReturnUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=${orderId}&status=success`;
      productTypeDesc = '八字查询服务';
    } else if (isDeepseekCourse) {
      productSpecificReturnUrl = `${process.env.PAID_RETURN_URL2}?status=success&orderId=${orderId}`;
      productTypeDesc = 'Deepseek AI课程';
    } else {
      productSpecificReturnUrl = `${process.env.PAID_RETURN_URL}?status=success&orderId=${orderId}`;
      productTypeDesc = '儿童纪录片';
    }
    
    console.log('产品类型:', productTypeDesc);
    console.log('产品专用跳转地址:', productSpecificReturnUrl);
    
    // 设置业务参数
    const bizContent = {
      out_trade_no: orderId.toString(),
      total_amount: amount.toFixed(2),
      subject: productName,
      product_code: isMobile ? 'QUICK_WAP_WAY' : 'FAST_INSTANT_TRADE_PAY'
    };
    
    // 强化处理移动端支付跳转问题 - 根据支付宝官方文档强化处理
    if (isMobile) {
      // 移动端跳转策略重新设计:
      // 1. 将跳转地址直接包含产品类型信息
      // 2. 同时处理quit_url和return_url两种跳转机制
      // 3. 使用订单ID作为唯一标识符，确保可以查询到正确的产品类型
      
      // 不再使用中间跳转页，直接设置最终目标地址
      const finalDestination = productSpecificReturnUrl;
      
      // 设置quit_url - 用户点击“返回”按钮时触发
      bizContent.quit_url = finalDestination;
      
      // 设置全局return_url - 覆盖默认的returnUrl
      // 注意：这一步是解决移动端支付跳转问题的关键
      formData.addField('returnUrl', finalDestination);
      
      // 添加订单ID到extend_params中，便于异步通知时查询
      let serviceProviderId, orderProductType;
      if (isNamingService) {
        serviceProviderId = 'naming_service';
        orderProductType = 'naming';
      } else if (isBaziService) {
        serviceProviderId = 'bazi_service';
        orderProductType = 'bazi';
      } else if (isDeepseekCourse) {
        serviceProviderId = 'deepseek_course';
        orderProductType = 'deepseek';
      } else {
        serviceProviderId = 'children_documentary';
        orderProductType = 'children';
      }
      
      bizContent.extend_params = {
        sys_service_provider_id: serviceProviderId,
        order_product_type: orderProductType
      };
      
      console.log('移动端支付增强跳转机制:', {
        quit_url: finalDestination,
        returnUrl: finalDestination,
        productType: productTypeDesc,
        orderId
      });
    }
    
    formData.addField('bizContent', bizContent);
    
    // 设置同步返回地址
    formData.addField('returnUrl', returnUrl);
    
    // 设置异步通知地址
    formData.addField('notifyUrl', notifyUrl);

    console.log('支付宝配置信息:', {
      returnUrl,
      notifyUrl
    });

    console.log('支付宝请求参数:', {
      method,
      bizContent,
      returnUrl,
      notifyUrl
    });

    // 生成支付链接
    const result = await alipaySdk.exec(method, {}, { formData });
    console.log('支付链接生成结果:', result);
    
    return result;
  } catch (error) {
    console.error('生成支付链接失败:', error);
    throw error;
  }
};

export const verifyPayment = async (params) => {
  try {
    console.log('开始验证支付结果, 完整参数:', JSON.stringify(params, null, 2));
    console.log('当前环境:', process.env.NODE_ENV);

    // 检查参数是否完整
    if (!params || !params.out_trade_no) {
      console.error('支付验证失败: 缺少必要参数');
      return false;
    }
    
    // 使用系统默认SDK进行验证
    const alipaySdk = getSystemAlipaySdk();
    
    // 打印当前使用的支付宝配置信息（不包含敏感信息）
    console.log('验证使用的支付宝配置:', {
      appId: process.env.ALIPAY_APPID,
      gateway: 'https://openapi.alipay.com/gateway.do',
      publicKeyLength: process.env.ALIPAY_PUBLIC_KEY ? process.env.ALIPAY_PUBLIC_KEY.length : 0,
      privateKeyLength: process.env.ALIPAY_PRIVATE_KEY ? process.env.ALIPAY_PRIVATE_KEY.length : 0
    });

    // 同步回调处理
    if (params.method === 'alipay.trade.page.pay.return') {
      // 验证签名
      console.log('开始验证同步回调签名...');
      const signVerified = await alipaySdk.checkNotifySign(params);
      console.log('同步回调签名验证结果:', signVerified);
      
      if (!signVerified) {
        console.error('同步回调签名验证失败');
        // 打印签名相关信息用于调试
        console.log('签名字段:', params.sign);
        console.log('签名类型:', params.sign_type);
        return false;
      }
      
      // 同步回调直接返回true
      console.log('同步回调验证通过');
      return true;
    }

    // 异步通知处理
    // 检查是否为异步通知 - 通过trade_status字段判断，而不是通过method字段
    if (params.trade_status && params.notify_type === 'trade_status_sync') {
      // 验证签名 - 不依赖method字段
      console.log('开始验证异步通知签名...');
      
      // 创建一个不包含method字段的参数对象用于验证
      const verifyParams = { ...params };
      if (verifyParams.method) {
        delete verifyParams.method; // 删除我们手动添加的method字段
      }
      
      const signVerified = await alipaySdk.checkNotifySign(verifyParams);
      console.log('异步通知签名验证结果:', signVerified);
      
      if (!signVerified) {
        console.error('异步通知签名验证失败');
        // 打印签名相关信息用于调试
        console.log('签名字段:', params.sign);
        console.log('签名类型:', params.sign_type);
        return false;
      }

      // 验证交易状态
      const tradeStatus = params.trade_status;
      console.log('交易状态:', tradeStatus);
      
      if (!tradeStatus || tradeStatus !== 'TRADE_SUCCESS') {
        console.error('交易状态不正确:', tradeStatus);
        return false;
      }
    }
    console.log('支付验证通过');
    return true;
  } catch (error) {
    console.error('支付验证失败, 错误详情:', error);
    console.error('错误堆栈:', error.stack);
    return false;
  }
};
