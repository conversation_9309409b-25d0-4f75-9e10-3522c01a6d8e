// 八字测算工具函数

import dayjs from 'dayjs';
import { BaziFormData, TimeInfo, Location, TRADITIONAL_HOURS, TraditionalHour } from '../types/bazi-enhanced';

// 时辰转换工具
export class TimeUtils {
  /**
   * 将现代时间转换为传统时辰
   */
  static getTraditionalHour(hour: number): TraditionalHour {
    // 处理子时的特殊情况（23:00-01:00）
    if (hour === 23 || hour === 0) {
      return '子时';
    }
    
    const traditionalHour = TRADITIONAL_HOURS.find(th => {
      const [start, end] = th.range;
      if (start < end) {
        return hour >= start && hour < end;
      } else {
        // 跨日的情况（如子时）
        return hour >= start || hour < end;
      }
    });
    
    return traditionalHour?.value || '子时';
  }

  /**
   * 获取时辰描述
   */
  static getHourDescription(hour: number): string {
    const traditional = this.getTraditionalHour(hour);
    const hourInfo = TRADITIONAL_HOURS.find(th => th.value === traditional);
    return hourInfo?.description || '';
  }

  /**
   * 验证时间格式
   */
  static validateTime(hour: number, minute: number): boolean {
    return hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59;
  }

  /**
   * 格式化时间显示
   */
  static formatTime(hour: number, minute: number): string {
    return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
  }
}

// 日期处理工具
export class DateUtils {
  /**
   * 验证日期是否合理
   */
  static validateBirthDate(date: string): boolean {
    const birthDate = dayjs(date);
    const now = dayjs();
    const minDate = dayjs('1900-01-01');
    
    return birthDate.isValid() && 
           birthDate.isBefore(now) && 
           birthDate.isAfter(minDate);
  }

  /**
   * 计算年龄
   */
  static calculateAge(birthDate: string): number {
    return dayjs().diff(dayjs(birthDate), 'year');
  }

  /**
   * 格式化日期显示
   */
  static formatDate(date: string, format: string = 'YYYY年MM月DD日'): string {
    return dayjs(date).format(format);
  }

  /**
   * 获取星期几
   */
  static getDayOfWeek(date: string): string {
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return `星期${days[dayjs(date).day()]}`;
  }
}

// 地区处理工具
export class LocationUtils {
  /**
   * 验证地区信息
   */
  static validateLocation(location: Location): boolean {
    return !!(location.province && location.city);
  }

  /**
   * 格式化地区显示
   */
  static formatLocation(location: Location): string {
    const parts = [location.province, location.city, location.district].filter(Boolean);
    return parts.join(' ');
  }

  /**
   * 根据地区获取时区（简化版本）
   */
  static getTimezone(location: Location): string {
    // 中国统一使用东八区
    if (location.province.includes('中国') || 
        ['北京', '上海', '广东', '江苏'].some(p => location.province.includes(p))) {
      return 'Asia/Shanghai';
    }
    return 'Asia/Shanghai'; // 默认东八区
  }
}

// 表单验证工具
export class ValidationUtils {
  /**
   * 验证姓名
   */
  static validateName(name: string): { valid: boolean; message?: string } {
    if (!name || name.trim().length === 0) {
      return { valid: false, message: '请输入姓名' };
    }
    
    if (name.length < 2 || name.length > 10) {
      return { valid: false, message: '姓名长度应在2-10个字符之间' };
    }
    
    if (!/^[\u4e00-\u9fa5]+$/.test(name)) {
      return { valid: false, message: '姓名只能包含中文字符' };
    }
    
    return { valid: true };
  }

  /**
   * 验证完整表单数据
   */
  static validateFormData(data: Partial<BaziFormData>): Record<string, string> {
    const errors: Record<string, string> = {};

    // 验证姓名
    if (data.name) {
      const nameValidation = this.validateName(data.name);
      if (!nameValidation.valid) {
        errors.name = nameValidation.message!;
      }
    } else {
      errors.name = '请输入姓名';
    }

    // 验证性别
    if (!data.gender) {
      errors.gender = '请选择性别';
    }

    // 验证出生日期
    if (!data.birthDate) {
      errors.birthDate = '请选择出生日期';
    } else if (!DateUtils.validateBirthDate(data.birthDate)) {
      errors.birthDate = '请选择有效的出生日期';
    }

    // 验证出生时间
    if (!data.birthTime) {
      errors.birthTime = '请选择出生时间';
    } else if (!TimeUtils.validateTime(data.birthTime.hour, data.birthTime.minute)) {
      errors.birthTime = '请选择有效的出生时间';
    }

    // 验证出生地
    if (!data.location) {
      errors.location = '请选择出生地区';
    } else if (!LocationUtils.validateLocation(data.location)) {
      errors.location = '请选择完整的出生地区';
    }

    return errors;
  }
}

// 本地存储工具
export class StorageUtils {
  private static readonly STORAGE_KEY = 'bazi_form_data';
  private static readonly HISTORY_KEY = 'bazi_history';

  /**
   * 保存表单数据
   */
  static saveFormData(data: Partial<BaziFormData>): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to save form data:', error);
    }
  }

  /**
   * 加载表单数据
   */
  static loadFormData(): Partial<BaziFormData> | null {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn('Failed to load form data:', error);
      return null;
    }
  }

  /**
   * 清除表单数据
   */
  static clearFormData(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear form data:', error);
    }
  }

  /**
   * 保存历史记录
   */
  static saveToHistory(data: BaziFormData): void {
    try {
      const history = this.getHistory();
      const newRecord = {
        ...data,
        id: Date.now().toString(),
        timestamp: new Date().toISOString()
      };
      
      history.unshift(newRecord);
      
      // 只保留最近10条记录
      const limitedHistory = history.slice(0, 10);
      
      localStorage.setItem(this.HISTORY_KEY, JSON.stringify(limitedHistory));
    } catch (error) {
      console.warn('Failed to save history:', error);
    }
  }

  /**
   * 获取历史记录
   */
  static getHistory(): any[] {
    try {
      const history = localStorage.getItem(this.HISTORY_KEY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.warn('Failed to get history:', error);
      return [];
    }
  }
}

// 错误处理工具
export class ErrorUtils {
  /**
   * 格式化错误消息
   */
  static formatError(error: any): string {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error?.response?.data?.message) {
      return error.response.data.message;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    return '发生未知错误，请稍后重试';
  }

  /**
   * 判断是否为网络错误
   */
  static isNetworkError(error: any): boolean {
    return !navigator.onLine || 
           error?.code === 'NETWORK_ERROR' ||
           error?.message?.includes('Network Error');
  }
}
