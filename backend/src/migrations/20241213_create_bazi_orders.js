export const up = async (queryInterface, Sequelize) => {
  await queryInterface.createTable('bazi_orders', {
    id: {
      type: Sequelize.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    orderId: {
      type: Sequelize.STRING(20),
      allowNull: false,
      references: {
        model: 'orders',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      comment: 'Associated order ID'
    },
    name: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'Customer name'
    },
    gender: {
      type: Sequelize.ENUM('male', 'female'),
      allowNull: false,
      comment: 'Customer gender'
    },
    calendarType: {
      type: Sequelize.ENUM('gregorian', 'lunar'),
      allowNull: false,
      defaultValue: 'gregorian',
      comment: 'Calendar type: gregorian or lunar'
    },
    birthYear: {
      type: Sequelize.STRING(4),
      allowNull: false,
      comment: 'Birth year'
    },
    birthMonth: {
      type: Sequelize.STRING(2),
      allowNull: false,
      comment: 'Birth month'
    },
    birthDay: {
      type: Sequelize.STRING(2),
      allowNull: false,
      comment: 'Birth day'
    },
    birthHour: {
      type: Sequelize.STRING(2),
      allowNull: false,
      comment: 'Birth hour'
    },
    birthMinute: {
      type: Sequelize.STRING(2),
      allowNull: false,
      comment: 'Birth minute'
    },
    birthProvince: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'Birth province'
    },
    birthCity: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'Birth city'
    },
    reportData: {
      type: Sequelize.JSON,
      allowNull: true,
      comment: 'Generated bazi report data in JSON format'
    },
    reportStatus: {
      type: Sequelize.ENUM('pending', 'generated', 'failed'),
      defaultValue: 'pending',
      comment: 'Report generation status'
    },
    apiRequestParams: {
      type: Sequelize.JSON,
      allowNull: true,
      comment: 'API request parameters for debugging'
    },
    apiResponse: {
      type: Sequelize.JSON,
      allowNull: true,
      comment: 'Full API response for debugging'
    },
    errorMessage: {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Error message if report generation failed'
    },
    createdAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
    },
    updatedAt: {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
    }
  });

  // 添加索引
  await queryInterface.addIndex('bazi_orders', ['orderId'], {
    unique: true,
    name: 'bazi_orders_order_id_unique'
  });

  await queryInterface.addIndex('bazi_orders', ['reportStatus'], {
    name: 'bazi_orders_report_status_index'
  });
};

export const down = async (queryInterface, Sequelize) => {
  await queryInterface.dropTable('bazi_orders');
}; 