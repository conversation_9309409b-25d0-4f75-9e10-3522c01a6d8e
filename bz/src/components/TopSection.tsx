import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

export const TopSection: React.FC = () => {
  const outsideRef = useRef<HTMLDivElement>(null);
  const insideRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // 模拟罗盘旋转动画
    const animateCompass = () => {
      if (outsideRef.current && insideRef.current) {
        // 外圈慢速旋转
        outsideRef.current.style.animation = 'spin 20s linear infinite';
        // 内圈快速旋转
        insideRef.current.style.animation = 'spin 10s linear infinite reverse';
      }
    };

    const timer = setTimeout(animateCompass, 500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="top-animate">
      <div className="compass">
        <div className="relative w-52 h-52">
          {/* 外圈 */}
          <motion.div
            ref={outsideRef}
            className="outside"
            initial={{ rotate: 0 }}
            animate={{ rotate: 360 }}
            transition={{
              duration: 20,
              repeat: Infinity,
              ease: 'linear',
            }}
          />

          {/* 内圈 */}
          <motion.div
            ref={insideRef}
            className="inside"
            initial={{ rotate: 0 }}
            animate={{ rotate: -360 }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: 'linear',
            }}
          />

          {/* 中心覆盖层 */}
          <div className="cover" />
        </div>
      </div>
    </div>
  );
};
