import React, { useEffect } from 'react';

const BaziTestSimple: React.FC = () => {
  useEffect(() => {
    // 设置页面背景色
    document.body.style.background = '#864a30';
    
    return () => {
      document.body.removeAttribute('style');
    };
  }, []);

  return (
    <div className="page home2">
      <h1 style={{ color: 'white', textAlign: 'center', padding: '20px' }}>
        八字查询测试页面
      </h1>
      
      <div style={{ 
        background: 'white', 
        margin: '20px', 
        padding: '20px', 
        borderRadius: '10px' 
      }}>
        <p>这是一个简单的测试页面，用于验证路由是否正常工作。</p>
        <p>如果您能看到这个页面，说明基本的页面渲染是正常的。</p>
      </div>
    </div>
  );
};

export default BaziTestSimple;
