#!/bin/bash

# 智能启动脚本 - 自动检测前端端口并配置后端

echo "🚀 智能启动脚本"
echo "📍 自动检测前端服务端口并配置后端..."

# 检测前端服务端口
detect_frontend_port() {
    local ports=(3000 3001 5173 5174 8080)
    
    for port in "${ports[@]}"; do
        if lsof -i :$port 2>/dev/null | grep -q "node\|npm\|vite"; then
            echo "✅ 发现前端服务运行在端口 $port"
            return $port
        fi
    done
    
    echo "⚠️ 未发现前端服务，使用默认端口 3000"
    return 3000
}

# 更新环境配置
update_env_config() {
    local port=$1
    local env_file="backend/.env.develop"
    local frontend_url="http://localhost:$port"
    
    echo "🔧 更新环境配置..."
    
    # 备份原文件
    cp "$env_file" "$env_file.backup.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
    
    # 更新FRONTEND_URL
    if grep -q "FRONTEND_URL=" "$env_file"; then
        # 替换现有配置
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s|FRONTEND_URL=.*|FRONTEND_URL=$frontend_url|" "$env_file"
        else
            # Linux
            sed -i "s|FRONTEND_URL=.*|FRONTEND_URL=$frontend_url|" "$env_file"
        fi
    else
        # 添加新配置
        echo "FRONTEND_URL=$frontend_url" >> "$env_file"
    fi
    
    echo "✅ 已更新 $env_file: FRONTEND_URL=$frontend_url"
}

# 启动后端服务
start_backend() {
    echo "🚀 启动后端服务..."
    
    # 停止现有服务
    pm2 stop commission-platform-api 2>/dev/null || true
    pm2 delete commission-platform-api 2>/dev/null || true
    
    # 清理端口
    lsof -ti:3005 | xargs kill -9 2>/dev/null || true
    
    # 启动服务
    cd backend
    pm2 start start-production.js --name commission-platform-api
    pm2 save
    
    echo "✅ 后端服务已启动"
}

# 显示配置信息
show_config() {
    local frontend_port=$1
    
    echo ""
    echo "📋 当前配置信息:"
    echo "   前端服务: http://localhost:$frontend_port"
    echo "   后端服务: http://localhost:3005"
    echo "   八字报告支付跳转: http://localhost:$frontend_port/bazi-report?orderId=xxx&status=success"
    echo ""
    echo "🔗 相关链接:"
    echo "   前端页面: http://localhost:$frontend_port"
    echo "   八字服务: http://localhost:$frontend_port/bazi"
    echo "   后端API: http://localhost:3005/api"
    echo ""
    echo "📊 服务状态:"
    pm2 status commission-platform-api
}

# 主函数
main() {
    # 检查是否在项目根目录
    if [ ! -f "package.json" ]; then
        echo "❌ 错误: 请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检测前端端口
    detect_frontend_port
    local frontend_port=$?
    
    # 更新环境配置
    update_env_config $frontend_port
    
    # 启动后端服务
    start_backend
    
    # 等待服务启动
    echo "⏳ 等待服务启动..."
    sleep 5
    
    # 测试API
    echo "🧪 测试API连接..."
    if curl -f http://localhost:3005/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}' > /dev/null 2>&1; then
        echo "✅ API连接正常"
    else
        echo "⚠️ API连接测试失败，但服务可能仍在启动中"
    fi
    
    # 显示配置信息
    show_config $frontend_port
    
    echo "🎉 启动完成！"
    echo "💡 如需修改端口配置，请编辑 backend/.env.develop 文件"
}

# 运行主函数
main "$@"
