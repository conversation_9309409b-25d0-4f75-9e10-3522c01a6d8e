import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import {
  Row, Col, Tabs, Form, Input, Button, message, Table, Select,
  Switch, Popconfirm, Typography, Tag, Space, Tooltip, Divider
} from 'antd';
import {
  AlipayOutlined, EditOutlined, DeleteOutlined,
  CheckCircleOutlined, CloseCircleOutlined, QuestionCircleOutlined,
} from '@ant-design/icons';
import {
  PageContainer,
  PageTitle,
  PageDescription,
  Card,
  StatusTag
} from '../styles/AdminTheme';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

// 支付配置接口
interface PaymentConfig {
  id: number;
  userId: number;
  alipayAppId: string;
  alipayPrivateKey: string;
  alipayPublicKey: string;
  isActive: boolean;
  description: string;
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    username: string;
    name: string;
  };
}

// 代理用户接口
interface Agent {
  id: number;
  username: string;
  name: string;
  paymentConfig?: {
    id: number;
    isActive: boolean;
  };
}

const PaymentManagement: React.FC = () => {
  const { token } = useAuth();
  const [activeTab, setActiveTab] = useState('1');
  
  // 系统配置状态
  const [systemForm] = Form.useForm();
  const [systemLoading, setSystemLoading] = useState(false);
  const [systemSaving, setSystemSaving] = useState(false);
  
  // 代理支付配置状态
  const [agentConfigs, setAgentConfigs] = useState<PaymentConfig[]>([]);
  const [agents, setAgents] = useState<Agent[]>([]);
  const [agentConfigsLoading, setAgentConfigsLoading] = useState(true);
  
  // 编辑代理配置状态
  const [editForm] = Form.useForm();
  const [editingAgentId, setEditingAgentId] = useState<number | null>(null);
  const [selectedAgent, setSelectedAgent] = useState<number | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editLoading, setEditLoading] = useState(false);
  
  // 获取系统支付配置
  const fetchSystemConfig = async () => {
    try {
      setSystemLoading(true);
      
      const response = await axios.get(`${config.apiUrl}/api/payment-config/system`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      const data = response.data.data;
      
      // 设置表单初始值
      systemForm.setFieldsValue({
        alipayAppId: data.alipayAppId,
        alipayPrivateKey: data.alipayPrivateKey,
        alipayPublicKey: data.alipayPublicKey,
        alipayNotifyUrl: data.alipayNotifyUrl,
        alipayReturnUrl: data.alipayReturnUrl
      });
      
    } catch (error) {
      console.error('获取系统支付配置失败', error);
      message.error('获取系统支付配置失败');
    } finally {
      setSystemLoading(false);
    }
  };
  
  // 获取代理支付配置
  const fetchAgentConfigs = async () => {
    try {
      setAgentConfigsLoading(true);
      
      const response = await axios.get(`${config.apiUrl}/api/payment-config/agents`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('获取到的代理配置列表原始数据:', response.data.data);
      
      // 过滤出已经有支付配置的代理
      const configuredAgents = response.data.data
        .filter((agent: any) => agent.paymentConfig)
        .map((agent: any) => {
          console.log('处理代理数据:', agent);
          console.log('代理的支付配置:', agent.paymentConfig);
          
          // 检查支付宝APPID是否存在
          if (!agent.paymentConfig.alipayAppId) {
            console.warn('代理的支付宝APPID为空:', agent.id, agent.username);
          }
          
          // 检查创建时间是否存在
          if (!agent.paymentConfig.createdAt) {
            console.warn('代理的创建时间为空:', agent.id, agent.username);
          }
          
          const configData = {
            id: agent.paymentConfig.id,
            userId: agent.id,
            username: agent.username,
            name: agent.name,
            alipayAppId: agent.paymentConfig.alipayAppId || '',
            isActive: agent.paymentConfig.isActive,
            description: agent.paymentConfig.description || '',
            createdAt: agent.paymentConfig.createdAt
          };
          
          console.log('处理后的代理配置:', configData);
          return configData;
        });
      
      console.log('处理后的代理配置列表:', configuredAgents);
      setAgentConfigs(configuredAgents);
      
    } catch (error) {
      console.error('获取代理支付配置失败', error);
      message.error('获取代理支付配置失败');
    } finally {
      setAgentConfigsLoading(false);
    }
  };
  
  // 获取一级代理列表
  const fetchAgents = async () => {
    try {
      const response = await axios.get(`${config.apiUrl}/api/payment-config/agents`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('获取到的代理列表:', response.data.data);
      
      if (response.data.success && Array.isArray(response.data.data)) {
        // 设置所有代理
        setAgents(response.data.data);
      } else {
        setAgents([]);
        console.error('获取到的代理列表格式不正确:', response.data);
      }
      
    } catch (error) {
      console.error('获取代理列表失败', error);
      message.error('获取代理列表失败');
      setAgents([]);
    }
  };  
  // 初始化获取数据
  useEffect(() => {
    fetchSystemConfig();
    fetchAgentConfigs();
    fetchAgents();
  }, [token]);
  
  // 保存系统支付配置
  const handleSystemSave = async (values: any) => {
    try {
      setSystemSaving(true);
      
      await axios.put(`${config.apiUrl}/api/payment-config/system`, values, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      message.success('系统支付配置已更新');
      
    } catch (error) {
      console.error('更新系统支付配置失败', error);
      message.error('更新系统支付配置失败');
    } finally {
      setSystemSaving(false);
    }
  };
  
  // 切换代理支付状态
  const handleToggleAgentConfig = async (userId: number, isActive: boolean) => {
    try {
      await axios.patch(
        `${config.apiUrl}/api/payment-config/agent/${userId}/toggle`,
        { isActive },
        { headers: { Authorization: `Bearer ${token}` }}
      );
      
      message.success(`已${isActive ? '启用' : '禁用'}代理支付配置`);
      fetchAgentConfigs();
      
    } catch (error) {
      console.error('切换代理支付状态失败', error);
      message.error('操作失败');
    }
  };
  
  // 删除代理支付配置
  const handleDeleteAgentConfig = async (userId: number) => {
    try {
      await axios.delete(`${config.apiUrl}/api/payment-config/agent/${userId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      message.success('代理支付配置已删除');
      fetchAgentConfigs();
      fetchAgents();
      
    } catch (error) {
      console.error('删除代理支付配置失败', error);
      message.error('删除代理支付配置失败');
    }
  };
  
  // 新增/编辑代理支付配置
  const handleSaveAgentConfig = async (values: any) => {
    try {
      setEditLoading(true);
      
      const agentId = editingAgentId || values.agentId;
      
      if (!agentId) {
        message.error('请选择代理');
        setEditLoading(false);
        return;
      }
      
      const submitData = {
        alipayAppId: values.alipayAppId || '',
        alipayPrivateKey: values.alipayPrivateKey || '',
        alipayPublicKey: values.alipayPublicKey || '',
        isActive: values.isActive === undefined ? true : values.isActive,
        description: values.description || ''
      };
      
      console.log('提交的表单数据:', { ...submitData, agentId });
      
      const response = await axios.put(
        `${config.apiUrl}/api/payment-config/agent/${agentId}`,
        submitData,
        { headers: { Authorization: `Bearer ${token}` }}
      );
      
      console.log('服务器响应:', response.data);
      
      if (response.data.success) {
        message.success(editingAgentId ? '代理支付配置已更新' : '代理支付配置已创建');
        
        // 重置表单和状态
        editForm.resetFields();
        setEditingAgentId(null);
        setSelectedAgent(null);
        setEditModalVisible(false);
        
        // 手动添加新配置到列表中
        if (response.data.data) {
          const newConfig = response.data.data;
          const agent = agents.find(a => a.id === agentId);
          
          if (agent) {
            const configData = {
              id: newConfig.id,
              userId: agent.id,
              username: agent.username,
              name: agent.name,
              alipayAppId: newConfig.alipayAppId || '',
              isActive: newConfig.isActive,
              description: newConfig.description || '',
              createdAt: newConfig.createdAt
            };
            
            console.log('添加新配置到列表:', configData);
            
            // 更新列表
            setAgentConfigs(prev => {
              const exists = prev.find(c => c.userId === agent.id);
              if (exists) {
                return prev.map(c => c.userId === agent.id ? configData : c);
              } else {
                return [...prev, configData];
              }
            });
          }
        }
        
        // 刷新数据
        fetchAgentConfigs();
        fetchAgents();
      } else {
        message.error(response.data.message || '操作失败');
      }
    } catch (error) {
      console.error('保存代理支付配置失败', error);
      message.error('保存代理支付配置失败');
    } finally {
      setEditLoading(false);
    }
  };
  
  // 编辑代理配置
  const handleEditAgentConfig = async (userId: number) => {
    try {
      const response = await axios.get(`${config.apiUrl}/api/payment-config/agent/${userId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log('获取到的代理配置:', response.data);
      
      const agentConfig = response.data.data;
      
      if (!agentConfig) {
        message.error('未找到该代理的支付配置');
        return;
      }
      
      // 设置表单初始值
      editForm.setFieldsValue({
        alipayAppId: agentConfig.alipayAppId || '',
        alipayPrivateKey: agentConfig.alipayPrivateKey || '',
        alipayPublicKey: agentConfig.alipayPublicKey || '',
        isActive: agentConfig.isActive,
        description: agentConfig.description || ''
      });
      
      setEditingAgentId(userId);
      setEditModalVisible(true);
      
    } catch (error) {
      console.error('获取代理支付配置失败', error);
      message.error('获取代理支付配置失败');
    }
  };
  
  // 添加新代理配置
  const handleAddAgentConfig = () => {
    editForm.resetFields();
    editForm.setFieldsValue({
      isActive: true
    });
    setEditingAgentId(null);
    setEditModalVisible(true);
  };
  
  // 代理支付配置表格列
  const agentConfigColumns = [
    {
      title: '代理ID',
      dataIndex: 'userId',
      key: 'userId',
    },
    {
      title: '代理名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <span>{text} ({record.username})</span>
      )
    },
    {
      title: '支付宝APPID',
      dataIndex: 'alipayAppId',
      key: 'alipayAppId',
      ellipsis: true,
      render: (text: string, record: any) => {
        console.log('渲染支付宝APPID:', text, record);
        return <span>{text || '-'}</span>;
      }
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        isActive ? 
        <StatusTag color="success" icon={<CheckCircleOutlined />}>启用</StatusTag> : 
        <StatusTag color="error" icon={<CloseCircleOutlined />}>禁用</StatusTag>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (createdAt: string, record: any) => {
        console.log('渲染创建时间:', createdAt, record);
        return createdAt ? new Date(createdAt).toLocaleString() : '-';
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (text: string, record: any) => (
        <Space>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            onClick={() => handleEditAgentConfig(record.userId)}
          >
            编辑
          </Button>
          <Switch 
            checked={record.isActive} 
            onChange={(checked) => handleToggleAgentConfig(record.userId, checked)} 
            checkedChildren="启用" 
            unCheckedChildren="禁用"
          />
          <Popconfirm
            title="确定要删除该代理的支付配置吗？"
            onConfirm={() => handleDeleteAgentConfig(record.userId)}
            okText="确定"
            cancelText="取消"
            icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
          >
            <Button type="text" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];
  
  return (
    <PageContainer>
      <PageTitle>支付管理</PageTitle>
      <PageDescription>
        配置系统支付和代理个人支付信息，一级代理可以使用个人支付，其下二级代理将使用一级代理的支付配置。
      </PageDescription>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="系统支付配置" key="1">
          <Card>
            <Form
              form={systemForm}
              layout="vertical"
              onFinish={handleSystemSave}
              initialValues={{
                alipayAppId: '',
                alipayPrivateKey: '',
                alipayPublicKey: '',
                alipayNotifyUrl: '',
                alipayReturnUrl: ''
              }}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="alipayAppId"
                    label="支付宝应用ID (APPID)"
                    rules={[{ required: true, message: '请输入支付宝应用ID' }]}
                  >
                    <Input placeholder="请输入支付宝应用ID" />
                  </Form.Item>
                </Col>
                
                <Col span={12}>
                  <Form.Item
                    name="alipayNotifyUrl"
                    label="支付宝异步回调地址"
                    rules={[{ required: true, message: '请输入支付宝异步回调地址' }]}
                  >
                    <Input placeholder="例如: https://yourdomain.com/payment/alipay/notify" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="alipayReturnUrl"
                    label="支付宝同步回调地址"
                    rules={[{ required: true, message: '请输入支付宝同步回调地址' }]}
                  >
                    <Input placeholder="例如: https://yourdomain.com/payment/alipay/return" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="alipayPrivateKey"
                label="支付宝应用私钥 (PRIVATE_KEY)"
                rules={[{ required: true, message: '请输入支付宝应用私钥' }]}
              >
                <TextArea
                  placeholder="请输入支付宝应用私钥"
                  autoSize={{ minRows: 3, maxRows: 6 }}
                />
              </Form.Item>
              
              <Form.Item
                name="alipayPublicKey"
                label="支付宝公钥 (PUBLIC_KEY)"
                rules={[{ required: true, message: '请输入支付宝公钥' }]}
              >
                <TextArea
                  placeholder="请输入支付宝公钥"
                  autoSize={{ minRows: 3, maxRows: 6 }}
                />
              </Form.Item>
              
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={systemSaving}
                  icon={<AlipayOutlined />}
                >
                  保存系统支付配置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
        
        <TabPane tab="代理支付配置" key="2">
          <Card>
            <Row justify="end" style={{ marginBottom: 16 }}>
              <Button
                type="primary"
                icon={<AlipayOutlined />}
                onClick={handleAddAgentConfig}
              >
                添加代理支付配置
              </Button>
            </Row>
            
            <Table
              rowKey="id"
              dataSource={agentConfigs}
              columns={agentConfigColumns}
              loading={agentConfigsLoading}
              pagination={{ pageSize: 10 }}
              onRow={(record) => {
                console.log('渲染行数据:', record);
                return {};
              }}
            />
          </Card>
          
          {/* 编辑/新增代理支付配置 模态框 */}
          {editModalVisible && (
            <Card
              title={editingAgentId ? "编辑代理支付配置" : "添加代理支付配置"}
              style={{ marginTop: 24 }}
              extra={
                <Button
                  type="link"
                  onClick={() => {
                    setEditModalVisible(false);
                    setEditingAgentId(null);
                    setSelectedAgent(null);
                    editForm.resetFields();
                  }}
                >
                  关闭
                </Button>
              }
            >
              <Form
                form={editForm}
                layout="vertical"
                onFinish={handleSaveAgentConfig}
                initialValues={{
                  alipayAppId: '',
                  alipayPrivateKey: '',
                  alipayPublicKey: '',
                  isActive: true,
                  description: ''
                }}
              >
                {!editingAgentId && (
                  <Form.Item
                    label="选择代理"
                    name="agentId"
                    rules={[{ required: true, message: '请选择代理' }]}
                  >
                    <Select 
                      placeholder="请选择代理"
                      onChange={(value) => setSelectedAgent(value)}
                      loading={agentConfigsLoading}
                      disabled={!!editingAgentId}
                      showSearch
                      optionFilterProp="children"
                    >
                      {agents.map((agent) => (
                        <Option key={agent.id} value={agent.id}>
                          {agent.name} ({agent.username})
                          {agent.paymentConfig && (
                            <Tag color={agent.paymentConfig.isActive ? 'green' : 'red'} style={{ marginLeft: 8 }}>
                              {agent.paymentConfig.isActive ? '已配置-启用' : '已配置-禁用'}
                            </Tag>
                          )}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                )}
                
                <Row gutter={24}>
                  <Col span={18}>
                    <Form.Item
                      name="alipayAppId"
                      label="支付宝应用ID (APPID)"
                      rules={[{ required: true, message: '请输入支付宝应用ID' }]}
                    >
                      <Input placeholder="请输入支付宝应用ID" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="isActive"
                      label="状态"
                      valuePropName="checked"
                    >
                      <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                    </Form.Item>
                  </Col>
                </Row>
                
                <Form.Item
                  name="alipayPrivateKey"
                  label="支付宝应用私钥 (PRIVATE_KEY)"
                  rules={[{ required: true, message: '请输入支付宝应用私钥' }]}
                >
                  <TextArea
                    placeholder="请输入支付宝应用私钥"
                    autoSize={{ minRows: 3, maxRows: 6 }}
                  />
                </Form.Item>
                
                <Form.Item
                  name="alipayPublicKey"
                  label="支付宝公钥 (PUBLIC_KEY)"
                  rules={[{ required: true, message: '请输入支付宝公钥' }]}
                >
                  <TextArea
                    placeholder="请输入支付宝公钥"
                    autoSize={{ minRows: 3, maxRows: 6 }}
                  />
                </Form.Item>
                
                <Form.Item
                  name="description"
                  label="备注说明"
                >
                  <Input placeholder="备注说明（可选）" />
                </Form.Item>
                
                <Form.Item>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={editLoading}
                    icon={<AlipayOutlined />}
                  >
                    {editingAgentId ? '更新代理支付配置' : '添加代理支付配置'}
                  </Button>
                </Form.Item>
              </Form>
            </Card>
          )}
        </TabPane>
      </Tabs>
    </PageContainer>
  );
};

export default PaymentManagement;
