// 密码重置脚本
import { User } from '../src/models/index.js';
import sequelize from '../src/config/database.js';
import bcrypt from 'bcryptjs';

async function resetPassword(username, newPassword) {
  try {
    console.log(`尝试为用户 ${username} 重置密码...`);
    
    // 查找用户
    const user = await User.findOne({ where: { username } });
    if (!user) {
      console.error('用户不存在');
      process.exit(1);
    }
    
    console.log(`找到用户: ${user.username}, ID: ${user.id}, 姓名: ${user.name}`);
    
    // 更新密码
    await user.update({ password: newPassword });
    console.log('密码重置成功！');
    console.log('提示：请通知用户使用新密码登录，并建议他们尽快更改密码。');
  } catch (error) {
    console.error('密码重置失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    process.exit(0);
  }
}

// 检查命令行参数
const args = process.argv.slice(2);
if (args.length !== 2) {
  console.log('使用方法: node reset-password.js <用户名> <新密码>');
  console.log('例如: node reset-password.js admin123 newPassword123');
  process.exit(1);
}

const [username, newPassword] = args;

// 执行密码重置
resetPassword(username, newPassword);
