import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET;

// 根据环境获取正确的前端域名
function getFrontendDomain() {
  // 强制返回生产环境域名，确保邀请链接始终指向正式网站
  return 'https://ye.bzcy.xyz';
  
  /* 以下代码暂时不使用
  const env = process.env.NODE_ENV || 'development';
  
  // 生产环境使用正式域名
  if (env === 'production') {
    return 'https://ye.bzcy.xyz';
  }
  
  // 开发环境使用本地地址
  return 'http://localhost:3000';
  */
}

/**
 * 生成邀请链接
 * @param {number} inviterId - 邀请人ID
 * @param {string} inviterRole - 邀请人角色 ('admin' 或 'agent')
 * @param {string} baseUrl - 可选的自定义基础URL
 * @returns {string} 邀请链接
 */
export function generateInviteLink(inviterId, inviterRole, baseUrl) {
  // 使用传入的baseUrl或根据环境自动选择
  const finalBaseUrl = baseUrl || getFrontendDomain();
  
  console.log('生成邀请链接:', { 
    inviterId, 
    inviterRole, 
    baseUrl: finalBaseUrl,
    env: process.env.NODE_ENV
  });
  
  // 生成包含邀请人信息的token
  const token = jwt.sign(
    { 
      inviterId: inviterId,
      inviterRole: inviterRole,
      type: 'invite'
    },
    JWT_SECRET,
    { expiresIn: '7d' } // 链接7天内有效
  );

  console.log('生成的token:', token);
  
  // 返回完整的邀请链接
  return `${finalBaseUrl}/register?invite=${token}`;
}

/**
 * 验证邀请链接的token
 * @param {string} token - 邀请token
 * @returns {Object|null} 解析后的token数据，验证失败返回null
 */
export function verifyInviteToken(token) {
  try {
    console.log('开始验证邀请token:', token);
    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('token解码结果:', decoded);
    
    if (decoded.type !== 'invite') {
      console.log('token类型不正确:', decoded.type);
      return null;
    }

    if (!decoded.inviterId || !decoded.inviterRole) {
      console.log('token缺少必要信息:', decoded);
      return null;
    }

    return {
      inviterId: decoded.inviterId,
      inviterRole: decoded.inviterRole
    };
  } catch (error) {
    console.error('验证邀请token失败:', error);
    return null;
  }
}
