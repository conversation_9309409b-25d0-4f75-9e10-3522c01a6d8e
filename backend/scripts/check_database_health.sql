-- 数据库健康检查脚本
USE commission_platform4;

-- 1. 检查外键约束数量
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    COUNT(*) as constraint_count
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'commission_platform4' 
AND REFERENCED_TABLE_NAME IS NOT NULL
GROUP BY TABLE_NAME, COLUMN_NAME
HAVING COUNT(*) > 1
ORDER BY constraint_count DESC;

-- 2. 检查表名大小写一致性
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    CASE 
        WHEN REFERENCED_TABLE_NAME REGEXP '^[a-z]' THEN '小写'
        WHEN REFERENCED_TABLE_NAME REGEXP '^[A-Z]' THEN '大写'
        ELSE '混合'
    END as table_name_case
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'commission_platform4' 
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY REFERENCED_TABLE_NAME;

-- 3. 检查数据完整性
-- 检查孤立的订单记录
SELECT 'Orders with invalid productId' as issue, COUNT(*) as count
FROM Orders o 
LEFT JOIN Products p ON o.productId = p.id 
WHERE p.id IS NULL
UNION ALL
SELECT 'Orders with invalid agentId' as issue, COUNT(*) as count
FROM Orders o 
LEFT JOIN Users u ON o.agentId = u.id 
WHERE o.agentId IS NOT NULL AND u.id IS NULL
UNION ALL
SELECT 'Commissions with invalid orderId' as issue, COUNT(*) as count
FROM Commissions c 
LEFT JOIN Orders o ON c.orderId = o.id 
WHERE o.id IS NULL
UNION ALL
SELECT 'Commissions with invalid agentId' as issue, COUNT(*) as count
FROM Commissions c 
LEFT JOIN Users u ON c.agentId = u.id 
WHERE u.id IS NULL;

-- 4. 检查重复数据
SELECT 'Duplicate ProductLinks' as issue, COUNT(*) as count
FROM (
    SELECT agentId, productId, COUNT(*) as cnt
    FROM ProductLinks 
    GROUP BY agentId, productId 
    HAVING COUNT(*) > 1
) as duplicates;

-- 5. 检查字段约束
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_TYPE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = 'commission_platform4'
AND TABLE_NAME IN ('Orders', 'Users', 'Products', 'Commissions')
AND COLUMN_NAME IN ('agentId', 'productId', 'orderId')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 6. 检查枚举值
SELECT 'Orders.status' as field, status as value, COUNT(*) as count
FROM Orders GROUP BY status
UNION ALL
SELECT 'Orders.orderType' as field, orderType as value, COUNT(*) as count
FROM Orders GROUP BY orderType
UNION ALL
SELECT 'Products.type' as field, type as value, COUNT(*) as count
FROM Products GROUP BY type
UNION ALL
SELECT 'Users.role' as field, role as value, COUNT(*) as count
FROM Users GROUP BY role
ORDER BY field, value;

-- 7. 检查索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = 'commission_platform4'
AND TABLE_NAME IN ('Orders', 'Users', 'Products', 'Commissions')
ORDER BY TABLE_NAME, INDEX_NAME;

-- 8. 统计表大小和记录数
SELECT 
    TABLE_NAME,
    TABLE_ROWS,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'commission_platform4'
ORDER BY TABLE_ROWS DESC;

-- 9. 检查最近的数据活动
SELECT 'Recent Orders' as activity, COUNT(*) as count
FROM Orders 
WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
UNION ALL
SELECT 'Recent Users' as activity, COUNT(*) as count
FROM Users 
WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY)
UNION ALL
SELECT 'Recent Commissions' as activity, COUNT(*) as count
FROM Commissions 
WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 10. 显示健康检查完成
SELECT 'Database health check completed' as status, NOW() as timestamp;
