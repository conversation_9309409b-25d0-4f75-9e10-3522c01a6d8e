import { useState } from 'react';
import { SectionCard } from './section-card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Sparkles, Star, Gift, ArrowRight, CheckCircle } from 'lucide-react';
import { Alert, AlertDescription } from '../ui/alert';
import { config } from '../../config';
import '../../styles/BaziReportEnhanced.css';

interface NamingServiceOfferProps {
  customerName?: string;
  nameScore?: number;
  baziReportId?: string;
}

export function NamingServiceOffer({ 
  customerName, 
  nameScore, 
  baziReportId 
}: NamingServiceOfferProps) {
  const [isLoading, setIsLoading] = useState(false);

  // 根据姓名分数判断是否需要改名
  const needsRename = nameScore && nameScore < 80;
  
  const handleOrderNaming = async () => {
    setIsLoading(true);
    try {
      console.log('开始创建起名业务订单，参数:', { customerName, baziReportId });

      // 创建起名业务订单 - 使用完整的API URL
      const response = await fetch(`${config.apiUrl}/api/orders/naming`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerName,
          baziReportId,
          serviceType: 'naming',
        }),
      });

      console.log('API响应状态:', response.status);

      // 获取完整的响应数据
      const responseData = await response.json();
      console.log('API响应数据:', responseData);

      if (response.ok) {
        // 正确处理后端返回的数据结构
        if (responseData.success && responseData.data && responseData.data.paymentUrl) {
          console.log('获取到支付链接:', responseData.data.paymentUrl);
          // 跳转到支付页面
          window.location.href = responseData.data.paymentUrl;
        } else {
          throw new Error('响应数据格式不正确');
        }
      } else {
        throw new Error(responseData.message || '创建订单失败');
      }
    } catch (error) {
      console.error('创建起名订单失败:', error);
      alert('创建订单失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SectionCard 
      title="专业起名服务" 
      icon={Sparkles}
      contentClassName="space-y-6"
    >
      {/* 根据姓名分数显示不同的提示 */}
      {needsRename && (
        <Alert className="border-amber-200 bg-gradient-to-r from-amber-50 to-orange-50 shadow-lg">
          <div className="p-1 bg-gradient-to-r from-amber-400 to-orange-400 rounded-full">
            <Star className="h-3 w-3 text-white" />
          </div>
          <AlertDescription className="text-amber-800 font-medium">
            根据您的八字分析，当前姓名分数为 <strong className="text-orange-600">{nameScore}分</strong>，
            建议考虑改名以提升运势，助力人生发展。
          </AlertDescription>
        </Alert>
      )}

      {/* 服务介绍 */}
      <div className="space-y-6">
        <div className="text-center space-y-3">
          <h3 className="enhanced-title text-xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
            八字专业起名 · 改运助力
          </h3>
          <p className="text-muted-foreground text-base">
            基于您的八字命盘，为您量身定制最适合的姓名
          </p>
        </div>

        {/* 服务特色 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="enhanced-card flex items-start space-x-3 p-5 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl border border-blue-200 shadow-sm hover-lift">
            <div className="p-1.5 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg shadow-sm">
              <CheckCircle className="h-4 w-4 text-white shrink-0" />
            </div>
            <div>
              <h4 className="font-semibold text-blue-900 mb-1">八字匹配</h4>
              <p className="text-sm text-blue-700 leading-relaxed">
                根据您的生辰八字，精准匹配五行属性
              </p>
            </div>
          </div>

          <div className="enhanced-card flex items-start space-x-3 p-5 bg-gradient-to-br from-green-50 to-green-100 rounded-xl border border-green-200 shadow-sm hover-lift">
            <div className="p-1.5 bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-sm">
              <CheckCircle className="h-4 w-4 text-white shrink-0" />
            </div>
            <div>
              <h4 className="font-semibold text-green-900 mb-1">寓意美好</h4>
              <p className="text-sm text-green-700 leading-relaxed">
                精选寓意吉祥的汉字，助力人生发展
              </p>
            </div>
          </div>

          <div className="enhanced-card flex items-start space-x-3 p-5 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border border-purple-200 shadow-sm hover-lift">
            <div className="p-1.5 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg shadow-sm">
              <CheckCircle className="h-4 w-4 text-white shrink-0" />
            </div>
            <div>
              <h4 className="font-semibold text-purple-900 mb-1">专业解读</h4>
              <p className="text-sm text-purple-700 leading-relaxed">
                提供详细的姓名解析和运势分析
              </p>
            </div>
          </div>

          <div className="enhanced-card flex items-start space-x-3 p-5 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl border border-orange-200 shadow-sm hover-lift">
            <div className="p-1.5 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg shadow-sm">
              <CheckCircle className="h-4 w-4 text-white shrink-0" />
            </div>
            <div>
              <h4 className="font-semibold text-orange-900 mb-1">多个选择</h4>
              <p className="text-sm text-orange-700 leading-relaxed">
                提供3-5个精选姓名方案供您选择
              </p>
            </div>
          </div>
        </div>

        {/* 价格信息 */}
        <div className="enhanced-card text-center space-y-4 p-6 bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 rounded-xl border border-amber-200 shadow-lg">
          <div className="flex items-center justify-center space-x-3">
            <div className="p-2 bg-gradient-to-r from-amber-400 to-orange-400 rounded-lg shadow-sm">
              <Gift className="h-5 w-5 text-white" />
            </div>
            <span className="text-lg font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
              专业起名服务
            </span>
          </div>

          <div className="flex items-center justify-center space-x-3">
            <span className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
              ¥99-299
            </span>
            <Badge variant="secondary" className="bg-gradient-to-r from-amber-100 to-orange-100 text-amber-700 border-amber-200">
              根据需求定价
            </Badge>
          </div>

          <p className="text-sm text-amber-700 font-medium">
            包含：姓名方案 + 详细解析 + 运势分析 + 专业建议
          </p>
        </div>

        {/* 服务流程 */}
        <div className="space-y-4">
          <h4 className="font-semibold text-center text-amber-700">服务流程</h4>
          <div className="flex items-center justify-between text-sm">
            <div className="flex flex-col items-center space-y-2">
              <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-orange-400 text-white rounded-full flex items-center justify-center font-bold shadow-lg">
                1
              </div>
              <span className="text-center font-medium text-amber-700">下单支付</span>
            </div>
            <ArrowRight className="h-5 w-5 text-amber-400" />
            <div className="flex flex-col items-center space-y-2">
              <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-orange-400 text-white rounded-full flex items-center justify-center font-bold shadow-lg">
                2
              </div>
              <span className="text-center font-medium text-amber-700">专家分析</span>
            </div>
            <ArrowRight className="h-5 w-5 text-amber-400" />
            <div className="flex flex-col items-center space-y-2">
              <div className="w-10 h-10 bg-gradient-to-r from-amber-400 to-orange-400 text-white rounded-full flex items-center justify-center font-bold shadow-lg">
                3
              </div>
              <span className="text-center font-medium text-amber-700">方案交付</span>
            </div>
          </div>
        </div>

        {/* 行动按钮 */}
        <div className="text-center space-y-4">
          <Button
            onClick={handleOrderNaming}
            disabled={isLoading}
            size="lg"
            className="enhanced-button glow-effect w-full sm:w-auto bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white font-bold py-3 px-8 rounded-xl shadow-lg hover-lift border-0"
          >
            {isLoading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                处理中...
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-5 w-5" />
                立即获取专业起名方案
              </>
            )}
          </Button>

          <p className="text-sm text-amber-600 font-medium">
            支付成功后，专业起名师将通过微信与您联系
          </p>
        </div>
      </div>
    </SectionCard>
  );
}
