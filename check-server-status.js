#!/usr/bin/env node

/**
 * 检查服务器状态脚本
 * 用于诊断登录API 404错误问题
 */

import { exec } from 'child_process';
import util from 'util';
const execAsync = util.promisify(exec);

// 服务器配置
const SERVER_CONFIG = {
  host: '************',
  user: 'root',
  port: 3005,
  domain: 'ye.bzcy.xyz'
};

/**
 * 执行远程命令
 */
async function executeRemoteCommand(command, description) {
  console.log(`\n🔍 ${description}`);
  console.log(`执行命令: ${command}`);
  
  try {
    const { stdout, stderr } = await execAsync(command);
    if (stdout) {
      console.log('✅ 输出:');
      console.log(stdout);
    }
    if (stderr) {
      console.log('⚠️ 错误信息:');
      console.log(stderr);
    }
    return { success: true, stdout, stderr };
  } catch (error) {
    console.log('❌ 执行失败:');
    console.log(error.message);
    return { success: false, error: error.message };
  }
}

/**
 * 检查PM2进程状态
 */
async function checkPM2Status() {
  const command = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "pm2 status"`;
  return await executeRemoteCommand(command, '检查PM2进程状态');
}

/**
 * 检查端口监听状态
 */
async function checkPortStatus() {
  const command = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "netstat -tlnp | grep :${SERVER_CONFIG.port}"`;
  return await executeRemoteCommand(command, `检查端口${SERVER_CONFIG.port}监听状态`);
}

/**
 * 检查PM2日志
 */
async function checkPM2Logs() {
  const command = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "pm2 logs commission-platform-api --lines 20"`;
  return await executeRemoteCommand(command, '检查PM2应用日志');
}

/**
 * 检查Nginx状态
 */
async function checkNginxStatus() {
  const command = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "systemctl status nginx | head -10"`;
  return await executeRemoteCommand(command, '检查Nginx服务状态');
}

/**
 * 检查Nginx错误日志
 */
async function checkNginxLogs() {
  const command = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "tail -20 /var/log/nginx/commission-platform.error.log"`;
  return await executeRemoteCommand(command, '检查Nginx错误日志');
}

/**
 * 测试API连接
 */
async function testAPIConnection() {
  console.log('\n🔍 测试API连接');
  
  // 测试内部连接
  const internalCommand = `ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${SERVER_CONFIG.user}@${SERVER_CONFIG.host} "curl -s -o /dev/null -w '%{http_code}' http://localhost:${SERVER_CONFIG.port}/api/auth/login"`;
  const internalResult = await executeRemoteCommand(internalCommand, '测试内部API连接 (localhost:3005)');
  
  // 测试外部连接
  const externalCommand = `curl -s -o /dev/null -w '%{http_code}' https://${SERVER_CONFIG.domain}/api/auth/login`;
  const externalResult = await executeRemoteCommand(externalCommand, '测试外部API连接');
  
  return { internal: internalResult, external: externalResult };
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始检查服务器状态...');
  console.log(`服务器: ${SERVER_CONFIG.host}`);
  console.log(`域名: ${SERVER_CONFIG.domain}`);
  console.log(`后端端口: ${SERVER_CONFIG.port}`);
  
  const checks = [
    { name: 'PM2状态', func: checkPM2Status },
    { name: '端口监听', func: checkPortStatus },
    { name: 'PM2日志', func: checkPM2Logs },
    { name: 'Nginx状态', func: checkNginxStatus },
    { name: 'Nginx日志', func: checkNginxLogs },
    { name: 'API连接测试', func: testAPIConnection }
  ];
  
  const results = {};
  
  for (const check of checks) {
    try {
      results[check.name] = await check.func();
    } catch (error) {
      console.log(`❌ ${check.name} 检查失败:`, error.message);
      results[check.name] = { success: false, error: error.message };
    }
  }
  
  // 总结报告
  console.log('\n📊 检查结果总结:');
  console.log('=' .repeat(50));
  
  for (const [checkName, result] of Object.entries(results)) {
    const status = result.success ? '✅ 正常' : '❌ 异常';
    console.log(`${checkName}: ${status}`);
  }
  
  // 问题诊断建议
  console.log('\n🔧 问题诊断建议:');
  console.log('=' .repeat(50));
  
  if (!results['PM2状态']?.success) {
    console.log('1. PM2服务可能未运行，请检查PM2进程');
    console.log('   建议: ssh到服务器执行 pm2 restart commission-platform-api');
  }
  
  if (!results['端口监听']?.success) {
    console.log('2. 后端服务未在3005端口监听');
    console.log('   建议: 检查后端服务是否正常启动');
  }
  
  if (!results['Nginx状态']?.success) {
    console.log('3. Nginx服务异常');
    console.log('   建议: 重启Nginx服务');
  }
  
  console.log('\n注意: 如果需要输入SSH密码，请手动执行相关命令');
}

// 运行检查
main().catch(console.error);