import React, { useState, useEffect } from 'react';
import { Modal } from './ui/Modal';

interface PickerColumnProps {
  options: Array<{ label: string; value: number }>;
  value: number;
  onChange: (value: number) => void;
  className?: string;
}

const PickerColumn: React.FC<PickerColumnProps> = ({ options, value, onChange, className = '' }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [scrollTop, setScrollTop] = useState(0);

  const itemHeight = 44;
  const visibleItems = 5;
  const containerHeight = itemHeight * visibleItems;

  // 计算当前选中项的索引
  const selectedIndex = options.findIndex(option => option.value === value);
  const currentScrollTop = selectedIndex * itemHeight - (containerHeight - itemHeight) / 2;

  useEffect(() => {
    setScrollTop(Math.max(0, currentScrollTop));
  }, [currentScrollTop]);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setStartY(e.clientY);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return;

    const deltaY = e.clientY - startY;
    const newScrollTop = Math.max(0, Math.min(scrollTop - deltaY, (options.length - 1) * itemHeight));
    setScrollTop(newScrollTop);
  };

  const handleMouseUp = () => {
    if (!isDragging) return;

    setIsDragging(false);

    // 计算最接近的项目
    const nearestIndex = Math.round(scrollTop / itemHeight);
    const clampedIndex = Math.max(0, Math.min(nearestIndex, options.length - 1));

    if (options[clampedIndex]) {
      onChange(options[clampedIndex].value);
    }
  };

  const handleItemClick = (option: { label: string; value: number }) => {
    onChange(option.value);
  };

  return (
    <div className={`van-picker-column ${className}`} style={{ flex: 1 }}>
      <div
        className="van-picker-column__wrapper"
        style={{
          height: `${containerHeight}px`,
          overflow: 'hidden',
          position: 'relative',
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        {/* 选中区域指示器 */}
        <div
          style={{
            position: 'absolute',
            top: `${(containerHeight - itemHeight) / 2}px`,
            left: 0,
            right: 0,
            height: `${itemHeight}px`,
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
            borderTop: '1px solid #ebedf0',
            borderBottom: '1px solid #ebedf0',
            pointerEvents: 'none',
            zIndex: 1
          }}
        />

        {/* 选项列表 */}
        <div
          style={{
            transform: `translateY(-${scrollTop}px)`,
            transition: isDragging ? 'none' : 'transform 0.3s ease',
            paddingTop: `${(containerHeight - itemHeight) / 2}px`,
            paddingBottom: `${(containerHeight - itemHeight) / 2}px`
          }}
        >
          {options.map((option, index) => {
            const isSelected = option.value === value;
            const distance = Math.abs(index * itemHeight - scrollTop - (containerHeight - itemHeight) / 2);
            const opacity = Math.max(0.3, 1 - distance / (itemHeight * 2));

            return (
              <div
                key={option.value}
                className={`van-picker-column__item ${isSelected ? 'van-picker-column__item--selected' : ''}`}
                style={{
                  height: `${itemHeight}px`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '16px',
                  color: isSelected ? '#323233' : '#969799',
                  fontWeight: isSelected ? '500' : 'normal',
                  cursor: 'pointer',
                  opacity,
                  transition: isDragging ? 'none' : 'opacity 0.3s ease'
                }}
                onClick={() => handleItemClick(option)}
              >
                {option.label}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

interface DateTimePickerProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: {
    yearType: number;
    currentDate: number[];
    currentTime: number[];
  }) => void;
  yearType: number;
}

export const DateTimePicker: React.FC<DateTimePickerProps> = ({
  isOpen,
  onClose,
  onConfirm,
  yearType,
}) => {
  const [selectedDate, setSelectedDate] = useState<number[]>([]);
  const [selectedTime, setSelectedTime] = useState<number[]>([]);

  // 生成年份选项（1900-当前年份）
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;
  const currentDay = new Date().getDate();
  const currentHour = new Date().getHours();
  const currentMinute = new Date().getMinutes();

  const years = Array.from({ length: currentYear - 1900 + 1 }, (_, i) => 1900 + i).reverse();
  
  // 生成月份选项
  const months = Array.from({ length: 12 }, (_, i) => i + 1);
  
  // 生成日期选项
  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month, 0).getDate();
  };
  
  // 生成小时选项
  const hours = Array.from({ length: 24 }, (_, i) => i);
  
  // 生成分钟选项
  const minutes = Array.from({ length: 60 }, (_, i) => i);

  useEffect(() => {
    // 初始化默认值
    const now = new Date();
    setSelectedDate([now.getFullYear(), now.getMonth() + 1, now.getDate()]);
    setSelectedTime([now.getHours(), now.getMinutes()]);
  }, []);

  const handleConfirm = () => {
    onConfirm({
      yearType,
      currentDate: selectedDate,
      currentTime: selectedTime,
    });
  };

  const updateDate = (index: number, value: number) => {
    const newDate = [...selectedDate];
    newDate[index] = value;
    
    // 如果修改了年份或月份，需要检查日期是否有效
    if (index <= 1) {
      const [year, month] = newDate;
      const daysInMonth = getDaysInMonth(year, month);
      if (newDate[2] > daysInMonth) {
        newDate[2] = daysInMonth;
      }
    }
    
    setSelectedDate(newDate);
  };

  const updateTime = (index: number, value: number) => {
    const newTime = [...selectedTime];
    newTime[index] = value;
    setSelectedTime(newTime);
  };

  const days = selectedDate.length >= 2 
    ? Array.from({ length: getDaysInMonth(selectedDate[0], selectedDate[1]) }, (_, i) => i + 1)
    : [];

  return (
    <Modal isOpen={isOpen} onClose={onClose} position="bottom">
      <div className="van-popup" style={{ backgroundColor: 'white', borderRadius: '16px 16px 0 0' }}>
        <div className="van-picker__toolbar">
          <button
            className="van-picker__cancel"
            onClick={onClose}
          >
            取消
          </button>
          <div className="van-picker__title">
            {yearType === 1 ? '已选择公历' : '已选择农历'}
          </div>
          <button
            className="van-picker__confirm"
            onClick={handleConfirm}
          >
            确认
          </button>
        </div>

        {/* 日期时间选择器 */}
        <div className="van-picker__columns" style={{ display: 'flex', height: '216px' }}>
          {/* 年份 */}
          <PickerColumn
            options={years.map(year => ({ label: `${year}年`, value: year }))}
            value={selectedDate[0] || currentYear}
            onChange={(value) => updateDate(0, value)}
          />

          {/* 月份 */}
          <PickerColumn
            options={months.map(month => ({ label: `${month}月`, value: month }))}
            value={selectedDate[1] || currentMonth}
            onChange={(value) => updateDate(1, value)}
          />

          {/* 日期 */}
          <PickerColumn
            options={days.map(day => ({ label: `${day}日`, value: day }))}
            value={selectedDate[2] || currentDay}
            onChange={(value) => updateDate(2, value)}
          />

          {/* 小时 */}
          <PickerColumn
            options={hours.map(hour => ({ label: `${hour.toString().padStart(2, '0')}时`, value: hour }))}
            value={selectedTime[0] || currentHour}
            onChange={(value) => updateTime(0, value)}
          />

          {/* 分钟 */}
          <PickerColumn
            options={minutes.map(minute => ({ label: `${minute.toString().padStart(2, '0')}分`, value: minute }))}
            value={selectedTime[1] || currentMinute}
            onChange={(value) => updateTime(1, value)}
          />
        </div>
      </div>
    </Modal>
  );
};
