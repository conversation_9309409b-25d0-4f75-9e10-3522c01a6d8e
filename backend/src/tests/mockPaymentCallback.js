/**
 * 模拟支付宝回调测试脚本
 * 用于测试支付成功后的回调处理逻辑
 */
import fetch from 'node-fetch';
import { Order, ProductLink, User, Product } from '../models/index.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 测试配置
const config = {
  apiUrl: 'http://localhost:3005/api',
  testOrderId: `TEST${Date.now()}`
};

/**
 * 创建测试数据
 */
async function createTestData() {
  console.log('=== 创建测试数据 ===');
  
  // 1. 查找一个 Deepseek 产品
  const product = await Product.findOne({
    where: {
      title: {
        [sequelize.Op.like]: '%Deepseek%'
      }
    }
  });
  
  if (!product) {
    throw new Error('找不到 Deepseek 产品，请先添加产品');
  }
  
  console.log(`找到 Deepseek 产品: ${product.title}, ID: ${product.id}`);
  
  // 2. 查找一个二级代理
  const agent = await User.findOne({
    where: {
      level: 2
    }
  });
  
  if (!agent) {
    throw new Error('找不到二级代理用户');
  }
  
  console.log(`找到二级代理: ${agent.username}, ID: ${agent.id}`);
  
  // 3. 创建产品推广链接
  const productLink = await ProductLink.create({
    productId: product.id,
    agentId: agent.id,
    code: `test_${Date.now()}`,
    sales: 0,
    totalCommission: 0
  });
  
  console.log(`创建产品推广链接成功: ${productLink.code}`);
  
  // 4. 创建订单
  const order = await Order.create({
    id: config.testOrderId,
    productLinkId: productLink.id,
    productId: product.id,
    amount: product.price,
    status: 'pending',
    agentId: agent.id,
    buyerName: '测试买家',
    buyerPhone: '13800001234',
    buyerEmail: '<EMAIL>'
  });
  
  console.log(`创建订单成功: ${order.id}`);
  
  return { product, agent, productLink, order };
}

/**
 * 测试同步回调
 */
async function testSyncCallback(orderId) {
  console.log('\n=== 测试同步回调 ===');
  
  const params = {
    out_trade_no: orderId,
    trade_no: `test_payment_${Date.now()}`,
    total_amount: '299.00',
    seller_id: 'test_seller',
    app_id: 'test_app',
    sign: 'test_sign',
    sign_type: 'RSA2'
  };
  
  // 构建查询字符串
  const queryString = Object.entries(params)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');
  
  // 发送同步回调请求
  const response = await fetch(`${config.apiUrl}/payment/alipay/return?${queryString}`, {
    method: 'GET',
    redirect: 'manual' // 不自动跟随重定向
  });
  
  // 检查重定向URL
  const redirectUrl = response.headers.get('location');
  
  if (!redirectUrl) {
    throw new Error('没有获取到重定向URL');
  }
  
  console.log(`重定向URL: ${redirectUrl}`);
  
  // 检查是否重定向到 PAID_RETURN_URL2
  if (!redirectUrl.includes(process.env.PAID_RETURN_URL2)) {
    throw new Error(`重定向URL不正确，应该包含 ${process.env.PAID_RETURN_URL2}`);
  }
  
  console.log('同步回调测试通过，成功重定向到 PAID_RETURN_URL2');
  
  // 检查订单状态
  const order = await Order.findByPk(orderId);
  
  if (order.status !== 'paid') {
    throw new Error(`订单状态不正确: ${order.status}`);
  }
  
  console.log('订单状态已更新为 paid');
  
  return order;
}

/**
 * 测试异步通知
 */
async function testAsyncNotify(orderId) {
  console.log('\n=== 测试异步通知 ===');
  
  // 先将订单重置为 pending 状态
  const order = await Order.findByPk(orderId);
  await order.update({ status: 'pending', paidAt: null, paymentId: null });
  
  console.log(`订单状态已重置为 pending: ${order.id}`);
  
  const params = {
    out_trade_no: orderId,
    trade_no: `test_payment_${Date.now()}`,
    total_amount: '299.00',
    seller_id: 'test_seller',
    app_id: 'test_app',
    trade_status: 'TRADE_SUCCESS',
    sign: 'test_sign',
    sign_type: 'RSA2'
  };
  
  // 发送异步通知请求
  const response = await fetch(`${config.apiUrl}/payment/alipay/notify`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    body: new URLSearchParams(params)
  });
  
  const responseText = await response.text();
  
  if (responseText !== 'success') {
    throw new Error(`异步通知响应不正确: ${responseText}`);
  }
  
  console.log('异步通知测试通过，响应为 success');
  
  // 检查订单状态
  const updatedOrder = await Order.findByPk(orderId);
  
  if (updatedOrder.status !== 'paid') {
    throw new Error(`订单状态不正确: ${updatedOrder.status}`);
  }
  
  console.log('订单状态已更新为 paid');
  
  return updatedOrder;
}

/**
 * 清理测试数据
 */
async function cleanupTestData(testData) {
  console.log('\n=== 清理测试数据 ===');
  
  if (testData.order) {
    await testData.order.destroy();
    console.log(`删除测试订单: ${testData.order.id}`);
  }
  
  if (testData.productLink) {
    await testData.productLink.destroy();
    console.log(`删除测试产品推广链接: ${testData.productLink.code}`);
  }
  
  console.log('测试数据清理完成');
}

/**
 * 运行测试
 */
async function runTests() {
  let testData = null;
  
  try {
    // 创建测试数据
    testData = await createTestData();
    
    // 测试同步回调
    await testSyncCallback(testData.order.id);
    
    // 测试异步通知
    await testAsyncNotify(testData.order.id);
    
    console.log('\n=== 所有测试通过 ===');
    
  } catch (error) {
    console.error('测试失败:', error);
  } finally {
    // 清理测试数据
    if (testData) {
      await cleanupTestData(testData);
    }
    
    process.exit(0);
  }
}

// 导入 sequelize 用于 Op 操作符
import sequelize from '../config/database.js';

// 运行测试
runTests();
