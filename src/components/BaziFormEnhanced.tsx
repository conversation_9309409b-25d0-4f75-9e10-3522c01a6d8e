import React, { useEffect } from 'react';
import { useBaziStore } from '../store/baziStore';
import { NameField, GenderField, BirthdayField, AddressField, PhoneField } from './FormFields';
import { message } from 'antd';
import axios from 'axios';
import { config } from '../config';
import { format } from 'date-fns';
import '../styles/BaziReportEnhanced.css';

interface BaziFormEnhancedProps {
  productCode?: string;
  onSubmit?: (formData: any) => void;
  showExampleReport?: boolean;
  showCustomerService?: boolean;
}

export const BaziFormEnhanced: React.FC<BaziFormEnhancedProps> = ({
  productCode,
  onSubmit,
  showExampleReport = true,
  showCustomerService = true
}) => {
  const { 
    formData, 
    submitting, 
    setSubmitting, 
    setProductCode,
    resetForm
  } = useBaziStore();

  // 设置产品代码
  useEffect(() => {
    if (productCode) {
      setProductCode(productCode);
    }
  }, [productCode, setProductCode]);

  // 验证表单数据
  const validateForm = () => {
    if (!formData.name.trim()) {
      message.error('请输入姓名');
      return false;
    }
    
    if (!formData.birthday) {
      message.error('请选择生日');
      return false;
    }
    
    if (!formData.address) {
      message.error('请选择地区');
      return false;
    }
    
    return true;
  };

  // 处理表单提交
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    // 如果有自定义提交处理函数，使用它
    if (onSubmit) {
      const submitData = {
        name: formData.name,
        gender: formData.sex,
        calendarType: formData.yearType,
        birthDate: formData.dateTime ? new Date(
          formData.dateTime.year,
          formData.dateTime.month - 1,
          formData.dateTime.day,
          formData.dateTime.hour,
          formData.dateTime.minute
        ) : new Date(),
        birthHour: formData.dateTime?.hour || 0,
        birthMinute: formData.dateTime?.minute || 0,
        birthProvince: formData.area?.province || '',
        birthCity: formData.area?.city || '',
        phone: formData.phone || ''
      };
      onSubmit(submitData);
      return;
    }

    // 如果没有产品代码，显示错误
    if (!productCode) {
      message.error('缺少产品代码');
      return;
    }

    setSubmitting(true);
    
    try {
      // 构建订单数据
      const requestData = {
        // 八字查询相关数据
        name: formData.name,
        gender: formData.sex,
        calendarType: formData.yearType,
        birthYear: formData.dateTime?.year.toString() || '',
        birthMonth: formData.dateTime?.month.toString().padStart(2, '0') || '',
        birthDay: formData.dateTime?.day.toString().padStart(2, '0') || '',
        birthHour: formData.dateTime?.hour || 0,
        birthMinute: formData.dateTime?.minute || 0,
        birthProvince: formData.area?.province || '',
        birthCity: formData.area?.city || '',
        // 通用订单数据
        customerPhone: formData.phone || '',
        deviceId: 'web-' + Date.now(),
        clientIp: '127.0.0.1'
      };

      console.log('创建八字订单，请求数据:', requestData);

      // 调用通用购买API创建订单并获取支付链接
      const response = await axios.post(`${config.apiUrl}/api/orders/purchase/${productCode}`, requestData);

      console.log('订单创建成功:', response.data);

      if (response.data.paymentUrl) {
        message.success('订单创建成功，正在跳转到支付页面...');
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          window.location.href = response.data.paymentUrl;
        }, 1000);
      } else {
        throw new Error('未获取到支付链接');
      }
      
    } catch (error) {
      console.error('创建订单失败:', error);
      
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || '创建订单失败';
        message.error(errorMessage);
      } else {
        message.error('创建订单失败，请重试');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 查看示例报告
  const handleViewExample = () => {
    window.open('/bazi-report-demo', '_blank');
  };

  // 联系客服
  const handleContactService = () => {
    window.open('https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd', '_blank');
  };

  return (
    <div className="main-form">
      <div className="bg-title"></div>
      <div className="inner">
        {/* 表单字段 */}
        <NameField />
        <GenderField />
        <BirthdayField />
        <AddressField />
        {productCode && <PhoneField />}

        {/* 提交按钮 */}
        <button 
          className="submit-button" 
          onClick={handleSubmit}
          disabled={submitting}
        >
          {submitting ? '正在处理中...' : (productCode ? '立即支付并查询' : '立即测算')}
        </button>

        {/* 辅助按钮 */}
        {(showExampleReport || showCustomerService) && (
          <div style={{ 
            display: 'flex', 
            gap: '12px', 
            marginTop: '16px',
            justifyContent: 'center'
          }}>
            {showExampleReport && (
              <button
                onClick={handleViewExample}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: 'linear-gradient(135deg, #f59e0b, #d97706)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(245, 158, 11, 0.3)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                📖 查看示例报告
              </button>
            )}
            
            {showCustomerService && (
              <button
                onClick={handleContactService}
                style={{
                  flex: 1,
                  padding: '12px',
                  background: 'linear-gradient(135deg, #10b981, #059669)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '8px',
                  fontSize: '14px',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(16, 185, 129, 0.3)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                💬 联系客服
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
