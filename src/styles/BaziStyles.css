/* 八字查询页面专用样式 */

/* 滚动动画 */
@keyframes scroll {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

/* 浮动动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bazi-landing-title {
    font-size: 2.5rem !important;
  }
  
  .bazi-landing-subtitle {
    font-size: 1rem !important;
  }
  
  .bazi-card {
    margin: 1rem !important;
    border-radius: 15px !important;
  }
  
  .bazi-form-input {
    font-size: 16px !important; /* 防止iOS缩放 */
  }
  
  .bazi-button-primary {
    height: 50px !important;
    font-size: 16px !important;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.bazi-fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.bazi-pulse {
  animation: pulse 2s infinite;
}

.bazi-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 自定义滚动条 */
.bazi-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.bazi-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.bazi-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
}

.bazi-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

/* 表单样式增强 */
.bazi-form-item .ant-form-item-label > label {
  font-weight: 500;
  color: #333;
}

.bazi-form-item .ant-input,
.bazi-form-item .ant-select-selector,
.bazi-form-item .ant-picker {
  border-radius: 10px;
  border: 2px solid #e1e5e9;
  transition: all 0.3s ease;
}

.bazi-form-item .ant-input:focus,
.bazi-form-item .ant-select-focused .ant-select-selector,
.bazi-form-item .ant-picker-focused {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.bazi-form-item .ant-input:hover,
.bazi-form-item .ant-select:hover .ant-select-selector,
.bazi-form-item .ant-picker:hover {
  border-color: #667eea;
}

/* 按钮样式增强 */
.bazi-button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 15px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.bazi-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.bazi-button-primary:active {
  transform: translateY(0);
}

/* 卡片样式增强 */
.bazi-card-enhanced {
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.bazi-card-enhanced:hover {
  transform: translateY(-5px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

/* 渐变背景 */
.bazi-gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.bazi-gradient-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
}

/* 文字渐变效果 */
.bazi-text-gradient {
  background: linear-gradient(45deg, #FFD700, #FFA500);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: bold;
}

/* 加载动画 */
.bazi-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bazi-loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 提示信息样式 */
.bazi-tip {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 0.85rem;
  color: #666;
  margin-top: 8px;
}

.bazi-tip::before {
  content: '💡 ';
  margin-right: 4px;
}

/* 安全提示样式 */
.bazi-security-notice {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 10px;
  padding: 1rem;
  text-align: center;
  margin-top: 1.5rem;
}

.bazi-security-notice::before {
  content: '🔒 ';
  font-size: 1.2rem;
  margin-right: 8px;
}

/* 评价卡片样式 */
.bazi-testimonial {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 1rem;
  max-width: 200px;
  transition: all 0.3s ease;
}

.bazi-testimonial:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* 统计数字样式 */
.bazi-stat-number {
  color: #FFD700;
  font-weight: bold;
  font-size: 1.2em;
}

/* 特色标签样式 */
.bazi-feature-tag {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
  border-radius: 25px;
  padding: 0.8rem 1.5rem;
  font-size: 0.9rem;
  display: inline-block;
}

/* 价格显示样式 */
.bazi-price {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 25px;
  padding: 1rem 2rem;
  font-size: 1.5rem;
  font-weight: bold;
  display: inline-block;
}

/* 无缝滚动动画 */
@keyframes scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.bazi-scroll-text {
  animation: scroll 20s linear infinite;
  white-space: nowrap;
}

/* 打字机效果 */
@keyframes typing {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

.bazi-typewriter {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: currentColor;
  }
}
