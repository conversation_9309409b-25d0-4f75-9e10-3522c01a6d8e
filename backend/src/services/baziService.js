import crypto from 'crypto';
import fetch from 'node-fetch';
import chengguService from './chengguService.js';

class BaziService {
  constructor() {
    this.source = "market";
  }

  getCredentials() {
    const secretId = process.env.TENCENT_API_SECRET_ID;
    const secretKey = process.env.TENCENT_API_SECRET_KEY;
    const baseUrl = process.env.TENCENT_API_BASE_URL;

    if (!secretId || !secretKey || !baseUrl) {
      console.warn('Bazi API credentials not configured. Please set TENCENT_API_SECRET_ID, TENCENT_API_SECRET_KEY, and TENCENT_API_BASE_URL in environment variables.');
      console.log('Current environment variables:', {
        TENCENT_API_SECRET_ID: secretId ? '已设置' : '未设置',
        TENCENT_API_SECRET_KEY: secretKey ? '已设置' : '未设置',
        TENCENT_API_BASE_URL: baseUrl ? '已设置' : '未设置'
      });
      return null;
    }

    return { secretId, secretKey, baseUrl };
  }

  /**
   * 生成API签名
   * @param {string} datetime - GMT时间字符串
   * @param {Object} credentials - API凭据
   * @returns {string} 签名字符串
   */
  generateSignature(datetime, credentials) {
    const signStr = `x-date: ${datetime}\nx-source: ${this.source}`;
    const sign = crypto
      .createHmac('sha1', credentials.secretKey)
      .update(signStr)
      .digest('base64');
    
    return `hmac id="${credentials.secretId}", algorithm="hmac-sha1", headers="x-date x-source", signature="${sign}"`;
  }

  /**
   * 调用八字API
   * @param {Object} params - API参数
   * @param {string} params.xing - 姓
   * @param {string} params.ming - 名
   * @param {string} params.sex - 性别 (1:男, 2:女)
   * @param {string} params.yearType - 历法类型 (1:公历, 2:农历)
   * @param {string} params.year - 年
   * @param {string} params.month - 月
   * @param {string} params.day - 日
   * @param {string} params.hour - 时
   * @param {string} params.minute - 分
   * @param {string} params.address - 地址
   * @returns {Promise<Object>} API响应数据
   */
  async fetchBaziReport(params) {
    const credentials = this.getCredentials();
    if (!credentials) {
      throw new Error('Bazi API credentials are not configured');
    }

    // 验证必需参数
    const requiredParams = ['xing', 'ming', 'sex', 'yearType', 'year', 'month', 'day', 'hour', 'minute'];
    for (const param of requiredParams) {
      if (!params[param]) {
        throw new Error(`Missing required parameter: ${param}`);
      }
    }

    const datetime = new Date().toUTCString();
    const auth = this.generateSignature(datetime, credentials);

    const headers = {
      "X-Source": this.source,
      "X-Date": datetime,
      "Authorization": auth,
      "Content-Type": "application/json"
    };

    // 构建查询参数
    const queryParams = new URLSearchParams({
      xing: params.xing,
      ming: params.ming,
      sex: params.sex,
      yearType: params.yearType,
      year: params.year,
      month: params.month,
      day: params.day,
      hour: params.hour,
      minute: params.minute,
      address: params.address || ""
    });

    const url = `${credentials.baseUrl}/bazi?${queryParams.toString()}`;

    try {
      console.log('Calling Bazi API:', { url: credentials.baseUrl, params });
      
      const response = await fetch(url, {
        method: "GET",
        headers: headers,
      });

      if (!response.ok) {
        const errorBody = await response.text();
        console.error('Bazi API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          body: errorBody
        });
        throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Bazi API Response:', { code: result.code, msg: result.msg });

      if (result.code !== 0) {
        throw new Error(`API returned an error: ${result.msg} (code: ${result.code})`);
      }

      return result.data;

    } catch (error) {
      console.error('Error calling Bazi API:', error);
      throw new Error(`Failed to fetch Bazi report: ${error.message}`);
    }
  }

  /**
   * 转换前端表单数据为API参数
   * @param {Object} formData - 前端表单数据
   * @returns {Object} API参数
   */
  convertFormDataToApiParams(formData) {
    const xing = formData.name.substring(0, 1);
    const ming = formData.name.substring(1);

    return {
      xing: xing,
      ming: ming,
      sex: formData.gender === 'male' ? '1' : '2',
      yearType: formData.calendarType === 'gregorian' ? '1' : '2',
      year: formData.birthYear,
      month: formData.birthMonth,
      day: formData.birthDay,
      hour: formData.birthHour,
      minute: formData.birthMinute,
      address: `${formData.birthProvince} ${formData.birthCity}`,
    };
  }

  /**
   * 生成八字报告
   * @param {Object} customerInfo - 客户信息
   * @returns {Promise<Object>} 八字报告数据
   */
  async generateBaziReport(customerInfo) {
    try {
      const apiParams = this.convertFormDataToApiParams(customerInfo);
      const reportData = await this.fetchBaziReport(apiParams);

      // 补充称骨算命信息
      if (reportData && reportData['命主八字称骨信息']) {
        const chengguResult = chengguService.calculateChenggu(
          customerInfo.birthYear,
          customerInfo.birthMonth.padStart(2, '0'),
          customerInfo.birthDay.padStart(2, '0'),
          customerInfo.birthHour.padStart(2, '0')
        );

        if (chengguResult.success) {
          console.log('称骨算命计算成功:', chengguResult.data);
          // 用本地计算的称骨信息替换API返回的空值
          reportData['命主八字称骨信息'] = {
            ...reportData['命主八字称骨信息'],
            ...chengguResult.data
          };
        } else {
          console.warn('称骨算命计算失败:', chengguResult.error);
        }
      }

      return {
        success: true,
        data: reportData,
        apiParams: apiParams
      };
    } catch (error) {
      console.error('Error generating Bazi report:', error);
      return {
        success: false,
        error: error.message,
        apiParams: this.convertFormDataToApiParams(customerInfo)
      };
    }
  }
}

export default new BaziService(); 