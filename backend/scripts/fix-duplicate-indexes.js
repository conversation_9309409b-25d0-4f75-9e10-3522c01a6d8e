import mysql from 'mysql2/promise';

const connection = await mysql.createConnection({
  host: 'localhost',
  user: 'root',
  password: 'Noending5',
  database: 'commission_platform4'
});

console.log('开始清理所有表的重复索引...');

// 需要清理的表和字段
const tablesToClean = [
  { table: 'Users', field: 'username' },
  { table: 'ProductLinks', field: 'code' },
  { table: 'Orders', field: 'orderNumber' },
  { table: 'BaziOrders', field: 'orderId' },
  { table: 'Commissions', field: 'orderId' },
  { table: 'Products', field: 'title' },
  // 可以添加更多表
];

try {
  for (const { table, field } of tablesToClean) {
    console.log(`\n清理表 ${table} 的 ${field} 索引...`);

    // 获取所有相关的索引
    const [indexes] = await connection.execute(`
      SHOW INDEX FROM ${table} WHERE Key_name LIKE '${field}%'
    `);

    console.log(`找到 ${indexes.length} 个${field}索引`);

    if (indexes.length <= 1) {
      console.log(`✅ ${table}.${field} 索引正常，无需清理`);
      continue;
    }

    // 保留第一个，删除其他的
    for (let i = 1; i < indexes.length; i++) {
      const indexName = indexes[i].Key_name;
      try {
        console.log(`删除索引: ${table}.${indexName}`);
        await connection.execute(`ALTER TABLE ${table} DROP INDEX \`${indexName}\``);
        console.log(`✅ 成功删除索引: ${table}.${indexName}`);
      } catch (error) {
        console.log(`❌ 删除索引失败 ${table}.${indexName}:`, error.message);
      }
    }

    // 检查剩余的索引
    const [remainingIndexes] = await connection.execute(`
      SHOW INDEX FROM ${table} WHERE Key_name LIKE '${field}%'
    `);

    console.log(`${table} 清理完成，剩余 ${remainingIndexes.length} 个${field}索引`);
    remainingIndexes.forEach(index => {
      console.log(`- ${index.Key_name}`);
    });
  }

  console.log('\n🎉 所有表的重复索引清理完成！');

} catch (error) {
  console.error('清理索引时出错:', error);
} finally {
  await connection.end();
}
