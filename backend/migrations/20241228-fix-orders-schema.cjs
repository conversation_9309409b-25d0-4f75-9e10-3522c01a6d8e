'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 修改 agentId 和 parentAgentId 的类型
    await queryInterface.changeColumn('Orders', 'agentId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    });

    await queryInterface.changeColumn('Orders', 'parentAgentId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    });

    // 修改 status 枚举值
    await queryInterface.changeColumn('Orders', 'status', {
      type: Sequelize.ENUM('pending', 'paid', 'cancelled'),
      defaultValue: 'pending'
    });
  },

  async down(queryInterface, Sequelize) {
    // 恢复 agentId 和 parentAgentId 的类型
    await queryInterface.changeColumn('Orders', 'agentId', {
      type: Sequelize.STRING(7),
      allowNull: false
    });

    await queryInterface.changeColumn('Orders', 'parentAgentId', {
      type: Sequelize.STRING(7),
      allowNull: true
    });

    // 恢复 status 枚举值
    await queryInterface.changeColumn('Orders', 'status', {
      type: Sequelize.ENUM('pending', 'completed', 'cancelled'),
      defaultValue: 'pending'
    });
  }
};
