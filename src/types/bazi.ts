// 八字查询相关类型定义

// 八字查询表单数据类型
export interface BaZiQueryFormValues {
  name: string;
  gender: 'male' | 'female';
  calendarType: 'gregorian' | 'lunar';
  birthDate: Date;
  birthHour: string;
  birthMinute: string;
  birthProvince: string;
  birthCity: string;
  phone?: string; // 可选的手机号码字段
}

// 八字API请求参数
export interface BaZiApiParams {
  address: string;
  day: string;
  hour: string;
  ming: string;
  minute: string;
  month: string;
  sex: '1' | '2';
  xing: string;
  year: string;
  yearType: '1' | '2';
}

// 命主基本信息
export interface MingZhuInfo {
  姓名: string;
  性别: string;
  公历生日: string;
  农历生日: string;
  生肖: string;
  星座: string;
  出生地: string;
}

// 八字称骨信息
export interface ChengGuInfo {
  称骨歌: string;
  总骨重: string;
  年骨重: string;
  月骨重: string;
  日骨重: string;
  时骨重: string;
}

// 姓名数理信息
export interface XingMingShuLiInfo {
  天格: {
    数字: number;
    吉凶: string;
    含义: string;
  };
  人格: {
    数字: number;
    吉凶: string;
    含义: string;
  };
  地格: {
    数字: number;
    吉凶: string;
    含义: string;
  };
  总格: {
    数字: number;
    吉凶: string;
    含义: string;
  };
  外格: {
    数字: number;
    吉凶: string;
    含义: string;
  };
}

// 八字命盘信息
export interface MingPanInfo {
  八字信息: {
    年柱: string;
    月柱: string;
    日柱: string;
    时柱: string;
    命理综述: string;
  };
  十神信息: {
    [key: string]: string;
  };
  纳音信息: {
    [key: string]: string;
  };
}

// 五行喜神信息
export interface WuXingXiShenInfo {
  五行统计: {
    [key: string]: number;
  };
  喜用神: string;
  忌神: string;
  五行缺失: string[];
  建议: string;
}

// 日干论命信息
export interface RiGanLunMingInfo {
  日干: string;
  性格特点: string;
  事业运势: string;
  财运分析: string;
  感情运势: string;
  健康状况: string;
  总体建议: string;
}

// 完整的八字报告数据
export interface BaZiReportData {
  命主信息: MingZhuInfo;
  命主八字称骨信息: ChengGuInfo;
  命主姓名数理信息: XingMingShuLiInfo;
  命主八字命盘信息: MingPanInfo;
  命主五行和喜神信息: WuXingXiShenInfo;
  命主日干论命信息: RiGanLunMingInfo;
}

// API响应数据结构
export interface FullBaZiData {
  code: number;
  msg: string;
  data: BaZiReportData;
}

// 八字查询订单
export interface BaZiOrder {
  id: number;
  orderId: number;
  name: string;
  gender: 'male' | 'female';
  calendarType: 'gregorian' | 'lunar';
  birthYear: string;
  birthMonth: string;
  birthDay: string;
  birthHour: string;
  birthMinute: string;
  birthProvince: string;
  birthCity: string;
  reportData?: BaZiReportData;
  reportStatus: 'pending' | 'generated' | 'failed';
  createdAt: Date;
  updatedAt: Date;
}

// 省市数据结构
export interface LocationData {
  [province: string]: string[];
}

// ========== 八字命理详细类型补充 ========== //

// Basic type for strings that might contain \r\n or <BR> for formatting
export type FormattedText = string;

export interface XingYunMiJue {
  幸运数字: string;
  大凶数字: string;
  吉祥颜色: string;
  忌讳颜色: string;
  幸运花: string;
  幸运物品: string;
}

export interface ErShiBaXiu {
  所属星宿: string;
  星宿爱情: FormattedText;
  真爱方位: string;
  星宿吉祥物: string;
}

export interface MingZhuXinXi {
  姓名: string;
  姓氏: string;
  名字: string;
  性别: string;
  公历生日: string;
  农历生日: string;
  出生地区: string;
  真太阳时: string;
  岁数: number;
  星座: string;
  属相: string;
  幸运秘诀: XingYunMiJue;
  本命佛: string;
  太岁: string;
  命主福元: string;
  二十八宿: ErShiBaXiu;
  文昌位: string;
}

export interface MingZhuBaZiChengGuXinXi {
  称骨重量: string;
  称骨重量_命数: string;
  称骨重量_歌诀: FormattedText;
  称骨重量_歌诀释义: FormattedText;
  称骨重量_命运详解: FormattedText; // Contains <BR>
}

export interface XingMingWuXing {
  简体: string;
  繁体: string;
  拼音: string;
  繁体五行: string;
  繁体笔画: number;
}

export interface ShuLiDetail {
  主运: string;
  数理: string;
  数理暗示: FormattedText;
  象词: string;
  签语: FormattedText;
  吉凶: string;
  含义: FormattedText;
  五行: string;
}

export interface SanCaiPeiZhi {
  重点: string;
  三才解析: FormattedText;
  三才吉凶: string;
  基础运: string;
  成功运: string;
  社交运: string;
}

export interface XingMingZongFen {
  分数: number;
  说明: FormattedText;
}

export interface MingZhuXingMingShuLiXinXi {
  姓名五行: XingMingWuXing[];
  天格: ShuLiDetail;
  人格: ShuLiDetail;
  地格: ShuLiDetail;
  外格: ShuLiDetail;
  总格: ShuLiDetail;
  三才配置: SanCaiPeiZhi;
  姓名总分: XingMingZongFen;
}

export interface BaZiDateInfo {
  年: string;
  月: string;
  日: string;
  时: string;
}

export interface BaZiXinXi {
  公历: BaZiDateInfo;
  农历: BaZiDateInfo;
  节气: string[];
  起运: string;
  交运: string;
  胎元: string;
  命宫: string;
  天干留意: string;
  地支留意: string;
  三命通会: FormattedText[];
  月柱命理: FormattedText;
  日柱命理: FormattedText;
  时柱命理: FormattedText[];
  命理综述: string;
}

export interface BaZiJingPiXiangJieSubSection {
  [key: string]: FormattedText; // e.g., 优点, 缺点 or 流年财运, 本命财运
}

export interface BaZiJingPiXiangJie {
  您的性格分析: {
    优点: FormattedText;
    缺点: FormattedText;
  };
  您的财运分析: {
    流年财运: FormattedText;
    本命财运: FormattedText;
    注意事项: FormattedText;
  };
  您的爱情恋爱建议: {
    爱情分析: FormattedText;
  };
  您的健康建议: {
    中医五行: FormattedText;
    健康分析: FormattedText;
    易患疾病: FormattedText;
    易发症状: FormattedText;
    养生要点: FormattedText;
    生活起居: FormattedText;
    饮食调节: FormattedText;
    保健膳食: FormattedText;
  };
  您的事业成就: {
    喜用神: FormattedText;
    事业发展建议: FormattedText;
    三合贵人: FormattedText;
  };
  未来一年运程: WeiLaiYiNianYunCheng[];
}

export interface WeiLaiYiNianYunCheng {
  日期: string;
  运势: FormattedText;
  十神: FormattedText;
}

export interface BaZiPillar {
  十神: string;
  乾造: string;
  藏干: string[][];
  空亡: string;
  纳音: string;
  星运: string;
  自坐: string;
  神煞: string[];
}

export interface LiuNianJiXiong {
  流年: string;
  十神: string;
  星运: string;
  自坐: string;
  纳音: string;
  空亡: string;
  藏干: string[][];
  吉凶: string | null;
  神煞: string[];
  天干留意: string;
  地支留意: string;
}

export interface BaZiDaYun {
  虚岁: string;
  年份: string;
  纳音: string;
  空亡: string;
  星运: string;
  自坐: string;
  大运: string[];
  藏干: string[][];
  神煞: string[];
  流年: string[]; // This seems to be just a list of years, actual data is in 流年吉凶
  流年吉凶: LiuNianJiXiong[];
}

export interface MingZhuBaZiMingPanXinXi {
  八字信息: BaZiXinXi;
  八字精批详解: BaZiJingPiXiangJie;
  八字_年柱: BaZiPillar;
  八字_月柱: BaZiPillar;
  八字_日柱: BaZiPillar;
  八字_时柱: BaZiPillar;
  八字_大运: BaZiDaYun[];
}

export interface WuXingFenShu {
  五行: string;
  分数: string;
}

export interface TongLeiYiLeiDeFen {
  同类五行?: string; //异类 has this optional structure
  异类五行?: string;
  同类得分?: number;
  异类得分?: number;
}

export interface WangShuaiDeFen {
  得分: string;
  评级: string;
}

export interface MingZhuWuXingHeXiShenXinXi {
  五行分数: WuXingFenShu[];
  同类得分: TongLeiYiLeiDeFen;
  异类得分: TongLeiYiLeiDeFen;
  旺衰得分: WangShuaiDeFen;
  喜用神: string;
  八字喜神: FormattedText;
  八字论命: FormattedText;
}

export interface MingZhuRiGanLunMingXinXi {
  生辰八字: string[];
  八字五行: string[];
  五行纳音: string[];
  五行个数: string;
  同类异类: string;
  五行旺衰: FormattedText;
  四季用神: string;
  日干心性: FormattedText;
  日干层次: FormattedText;
  日干支分析: FormattedText;
  性格分析: FormattedText;
  爱情分析: FormattedText;
  事业分析: FormattedText;
  财运分析: FormattedText;
  健康分析: FormattedText;
} 