import React from 'react';
import { AlertCircle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import { Button } from './button';
import { cn } from '../../lib/utils';
import '../../styles/BaziReportEnhanced.css';

interface EnhancedErrorProps {
  title?: string;
  message?: string;
  className?: string;
  showRetry?: boolean;
  showHome?: boolean;
  showBack?: boolean;
  onRetry?: () => void;
  onHome?: () => void;
  onBack?: () => void;
}

export function EnhancedError({ 
  title = "出现了一些问题",
  message = "抱歉，系统遇到了一些问题。请稍后重试或联系客服。",
  className,
  showRetry = true,
  showHome = true,
  showBack = false,
  onRetry,
  onHome,
  onBack
}: EnhancedErrorProps) {
  return (
    <div className={cn(
      "min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 flex items-center justify-center",
      className
    )}>
      <div className="enhanced-card max-w-lg w-full mx-4 text-center space-y-6 p-8">
        {/* 错误图标 */}
        <div className="relative">
          <div className="relative bg-white rounded-full p-6 shadow-xl mx-auto w-fit">
            <AlertCircle className="h-16 w-16 text-red-500 mx-auto" />
          </div>
          
          {/* 装饰性元素 */}
          <div className="absolute inset-0 animate-pulse">
            <div className="w-3 h-3 bg-red-300 rounded-full absolute top-4 left-1/4 animate-bounce" style={{ animationDelay: '0s' }} />
            <div className="w-2 h-2 bg-orange-300 rounded-full absolute top-1/3 right-4 animate-bounce" style={{ animationDelay: '0.5s' }} />
            <div className="w-3 h-3 bg-yellow-300 rounded-full absolute bottom-4 right-1/3 animate-bounce" style={{ animationDelay: '1s' }} />
            <div className="w-2 h-2 bg-red-200 rounded-full absolute bottom-1/3 left-4 animate-bounce" style={{ animationDelay: '1.5s' }} />
          </div>
        </div>

        {/* 标题和消息 */}
        <div className="space-y-3">
          <h2 className="text-2xl font-bold bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
            {title}
          </h2>
          
          <p className="text-red-700 leading-relaxed">
            {message}
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {showRetry && onRetry && (
            <Button
              onClick={onRetry}
              className="enhanced-button bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white font-semibold py-2 px-6 rounded-lg shadow-lg hover-lift border-0"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              重试
            </Button>
          )}
          
          {showHome && onHome && (
            <Button
              onClick={onHome}
              variant="outline"
              className="border-amber-300 text-amber-700 hover:bg-amber-50 font-semibold py-2 px-6 rounded-lg hover-lift"
            >
              <Home className="mr-2 h-4 w-4" />
              返回首页
            </Button>
          )}
          
          {showBack && onBack && (
            <Button
              onClick={onBack}
              variant="outline"
              className="border-gray-300 text-gray-700 hover:bg-gray-50 font-semibold py-2 px-6 rounded-lg hover-lift"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回上页
            </Button>
          )}
        </div>

        {/* 底部提示 */}
        <div className="text-sm text-red-600 bg-red-50 p-3 rounded-lg border border-red-200">
          如果问题持续存在，请联系客服获取帮助
        </div>
      </div>
    </div>
  );
}

// 简化版错误组件
export function SimpleError({ 
  message = "加载失败", 
  onRetry,
  className 
}: { 
  message?: string; 
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <div className={cn("flex flex-col items-center justify-center p-6 text-center", className)}>
      <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
      <p className="text-red-700 mb-4">{message}</p>
      {onRetry && (
        <Button
          onClick={onRetry}
          variant="outline"
          className="border-red-300 text-red-700 hover:bg-red-50"
        >
          <RefreshCw className="mr-2 h-4 w-4" />
          重试
        </Button>
      )}
    </div>
  );
}

// 内联错误组件
export function InlineError({ 
  message = "出现错误",
  className 
}: { 
  message?: string;
  className?: string;
}) {
  return (
    <div className={cn("flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg border border-red-200", className)}>
      <AlertCircle className="h-4 w-4 shrink-0" />
      <span className="text-sm font-medium">{message}</span>
    </div>
  );
}
