import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { config } from '../config';
import { 
  Row, Col, Typography, Spin, Empty, Button, Input, Select, 
  Pagination, Tooltip, Image, Space, Modal, Form, message, Tag
} from 'antd';
import { 
  AppstoreOutlined, SearchOutlined, PlusOutlined, 
  DollarOutlined, PercentageOutlined,
  EditOutlined, DeleteOutlined, ReloadOutlined,
  EyeOutlined, CopyOutlined
} from '@ant-design/icons';

// 导入我们的主题组件
import {
  PageContainer,
  PageTitle,
  PageDescription,
  Card,
  FilterContainer,
  colors
} from '../styles/AdminTheme';
import styled from 'styled-components';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 添加产品卡片相关样式
const ProductCard = styled(Card)`
  height: 100%;
  
  &.product-card-body .ant-card-body {
    padding: 16px;
  }
  
  .product-image-container {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    height: 200px;
    margin-bottom: 12px;
  }
  
  .product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .product-status-overlay {
    position: absolute;
    top: 0;
    right: 0;
    padding: 4px;
  }
  
  .product-content {
    padding: 0;
  }
  
  .product-title {
    margin-bottom: 8px;
  }
  
  .product-description {
    color: rgba(0, 0, 0, 0.45);
    margin-bottom: 12px;
  }
  
  .product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
  
  .price-commission {
    display: flex;
    flex-direction: column;
  }
  
  .product-price {
    font-weight: bold;
  }
  
  .product-commission {
    color: ${colors.warning};
  }
  
  .product-actions {
    display: flex;
    justify-content: flex-end;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    padding-top: 12px;
  }
`;

interface Product {
  id: number;
  title: string;
  description: string;
  price: number;
  commissionRate: number; // 个人佣金率
  baseCommissionRate: number; // 基础佣金率
  imageUrl: string;
  status: 'active' | 'inactive';
  productCode?: string; // 添加用户专属产品code
  createdAt: string;
  updatedAt: string;
  images?: ProductImage[]; // 添加产品图片数组
}

interface ProductImage {
  id: number;
  productId: number;
  url: string;
  isPrimary: boolean;
  order: number;
}

// 暂时未使用的接口，后续可能会用到
// interface Category {
//   id: number;
//   name: string;
// }

const Products: React.FC = () => {
  const { isAuthenticated, token, user } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [totalProducts, setTotalProducts] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 12; // 固定每页显示12个产品
  const [filter, setFilter] = useState({
    search: '',
    status: '',
  });
  
  // 编辑产品相关状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [form] = Form.useForm();
  const [confirmLoading, setConfirmLoading] = useState(false);

  // 用户专属产品链接
  const [productLinks, setProductLinks] = useState<Array<{productId: number, status: string, code: string}>>([]);
  // 产品图片
  const [productImages, setProductImages] = useState<{[key: number]: ProductImage[]}>({});

  useEffect(() => {
    console.log('Products页面 - useEffect触发，当前页面:', currentPage, '筛选条件:', filter);
    if (token) {
      console.log('Products页面 - 开始获取数据，token存在');
      fetchProducts();
      
      // 获取产品链接
      if (user) {
        fetchProductLinks(user.id);
      }
    } else {
      console.log('Products页面 - token不存在');
    }
  }, [token, currentPage, pageSize, filter]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      console.log('开始获取产品数据');

      let url = `${config.apiUrl}/api/products`;
      
      if (filter.search) {
        url += `?search=${encodeURIComponent(filter.search)}`;
      }
      
      if (filter.status) {
        url += url.includes('?') ? `&status=${filter.status}` : `?status=${filter.status}`;
      }
      
      console.log('请求URL:', url);
      
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('获取产品列表失败');
      }

      const data = await response.json();
      console.log('获取到的产品数据:', data);
      
      // 处理产品数据，确保包含baseCommissionRate字段
      const processedData = data.map((product: any) => {
        console.log(`产品 ${product.id} 的基础佣金率:`, product.baseCommissionRate);
        return {
          ...product,
          // 确保基础佣金率字段存在，如果不存在则使用默认值
          baseCommissionRate: product.baseCommissionRate !== undefined ? Number(product.baseCommissionRate) : 0,
          // 如果没有commissionRate，则使用baseCommissionRate
          commissionRate: product.commissionRate !== undefined ? Number(product.commissionRate) : Number(product.baseCommissionRate || 0)
        };
      });
      
      console.log('处理后的产品数据:', processedData);
      
      setProducts(processedData);
      setTotalProducts(processedData.length);
      
      // 获取每个产品的图片
      data.forEach((product: Product) => {
        fetchProductImages(product.id);
      });
      
      setLoading(false);
    } catch (err) {
      console.error('获取产品失败:', err);
      setError('获取产品列表失败，请稍后重试');
      setLoading(false);
    }
  };

  // 获取用户专属产品链接
  const fetchProductLinks = async (userId: number) => {
    try {
      console.log('Products页面 - 开始获取用户专属产品链接');
      const response = await fetch(`${config.apiUrl}/api/product-links?userId=${userId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('获取产品链接失败');
      }

      const data = await response.json();
      console.log('Products页面 - 获取到产品链接数据:', data);
      
      // 直接使用API返回的数组数据
      if (Array.isArray(data)) {
        setProductLinks(data);
      } else {
        setProductLinks([]);
      }
    } catch (error) {
      console.error('获取产品链接失败:', error);
    }
  };

  // 获取产品图片
  const fetchProductImages = async (productId: number) => {
    try {
      // 检查是否已经获取过该产品的图片，避免重复请求
      if (productImages[productId]) {
        return;
      }

      const response = await fetch(`${config.apiUrl}/api/product-images/product/${productId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`获取产品图片失败: ${errorText}`);
      }

      const images = await response.json();
      
      if (Array.isArray(images) && images.length > 0) {
        // 保存图片数据
        setProductImages(prev => ({
          ...prev,
          [productId]: images
        }));
        
        // 同时更新产品的imageUrl字段，使用主图片或第一张图片的URL
        const primaryImage = images.find((img: ProductImage) => img.isPrimary);
        const firstImage = images[0];
        const imageUrl = primaryImage?.url || firstImage?.url;
        
        // 更新产品数据中的imageUrl
        setProducts(prevProducts => 
          prevProducts.map(p => 
            p.id === productId ? { ...p, imageUrl: imageUrl } : p
          )
        );
      } else {
        // 即使没有图片，也设置一个空数组，避免重复请求
        setProductImages(prev => ({
          ...prev,
          [productId]: []
        }));
      }
    } catch (err) {
      console.error(`获取产品 ${productId} 的图片失败:`, err instanceof Error ? err.message : '未知错误');
      // 设置一个空数组，避免重复请求
      setProductImages(prev => ({
        ...prev,
        [productId]: []
      }));
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    console.log('筛选条件变更:', key, value);
    setFilter(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1); // 重置到第一页
  };

  const handleResetFilters = () => {
    console.log('重置所有筛选条件');
    setFilter({
      search: '',
      status: '',
    });
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    console.log('页面变更:', page);
    setCurrentPage(page);
  };

  // 管理员编辑产品相关函数
  const showEditModal = (product: Product) => {
    console.log('编辑产品数据:', product);
    setEditingProduct(product);
    
    // 获取产品的主图URL
    let imageUrl = product.imageUrl || '';
    
    // 如果产品有图片数据，尝试获取主图或第一张图片的URL
    if (productImages[product.id] && productImages[product.id].length > 0) {
      const primaryImage = productImages[product.id].find((img: ProductImage) => img.isPrimary);
      const firstImage = productImages[product.id][0];
      imageUrl = primaryImage?.url || firstImage?.url || imageUrl;
    }
    
    // 设置表单字段值
    form.setFieldsValue({
      title: product.title,
      description: product.description,
      price: product.price,
      commissionRate: product.commissionRate || 0, // 确保佣金比例有默认值
      imageUrl: imageUrl,
      status: product.status
    });
    
    setIsModalVisible(true);
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    setEditingProduct(null);
    form.resetFields();
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setConfirmLoading(true);
      
      if (!editingProduct) return;
      
      console.log('保存产品数据:', values);
      
      // 保存产品基本信息
      const response = await fetch(`${config.apiUrl}/api/products/${editingProduct.id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          title: values.title,
          description: values.description,
          price: values.price,
          commissionRate: values.commissionRate,
          status: values.status,
          imageUrl: values.imageUrl // 确保imageUrl也被保存到产品基本信息中
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`更新产品失败: ${errorText}`);
      }

      // 如果有图片URL，保存产品图片
      if (values.imageUrl) {
        try {
          // 先验证图片URL是否有效
          const isValidUrl = /^(http|https):\/\/[^ "]+$/.test(values.imageUrl);
          if (!isValidUrl) {
            message.warning('请输入有效的图片URL地址，以http://或https://开头');
            setConfirmLoading(false);
            return;
          }

          // 检查是否已有主图片
          const existingImages = productImages[editingProduct.id] || [];
          const isPrimary = existingImages.length === 0 || !existingImages.some(img => img.isPrimary);
          
          message.loading('正在保存产品图片...', 0.5);
          console.log('保存产品图片:', values.imageUrl, '设为主图:', isPrimary);
          
          // 保存产品图片
          const imageResponse = await fetch(`${config.apiUrl}/api/products/${editingProduct.id}/images`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              url: values.imageUrl,
              isPrimary: isPrimary,
              order: existingImages.length + 1
            })
          });
          
          if (!imageResponse.ok) {
            const imageErrorText = await imageResponse.text();
            console.warn('保存产品图片失败，但产品信息已更新:', imageErrorText);
            message.warning('产品信息已更新，但图片保存失败，请检查图片URL是否有效');
          } else {
            console.log('产品图片保存成功');
            message.success('产品图片保存成功');
            // 更新本地图片缓存
            const newImage = await imageResponse.json();
            if (newImage) {
              const updatedImages = [...(existingImages || []), newImage];
              setProductImages(prev => ({
                ...prev,
                [editingProduct.id]: updatedImages
              }));
            }
          }
        } catch (imageError) {
          console.error('保存产品图片失败:', imageError);
          message.error('保存产品图片失败，请检查网络连接或联系管理员');
          // 不影响主流程，只记录错误
        }
      }

      message.success('产品更新成功！');
      fetchProducts(); // 刷新产品列表
      setIsModalVisible(false);
    } catch (error) {
      console.error('保存产品失败:', error);
      message.error('保存产品失败，请重试');
    } finally {
      setConfirmLoading(false);
    }
  };

  const handleDeleteProduct = async (productId: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '您确定要删除这个产品吗？此操作不可恢复。',
      okText: '确认',
      cancelText: '取消',
      okButtonProps: { danger: true },
      onOk: async () => {
        try {
          const response = await fetch(`${config.apiUrl}/api/products/${productId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error('删除产品失败');
          }

          message.success('产品删除成功！');
          fetchProducts(); // 刷新产品列表
        } catch (error) {
          console.error('删除产品失败:', error);
          message.error('删除产品失败，请重试');
        }
      }
    });
  };

  // 复制产品推广链接
  const copyProductLink = (productId: number) => {
    // 查找当前产品的专属推广链接
    const productLink = productLinks.find((link: {productId: number, status: string, code: string}) => 
      link.productId === productId && link.status === 'active'
    );
    
    if (productLink && productLink.code) {
      // 使用专属产品code
      const link = `${window.location.origin}/product/${productLink.code}`;
      navigator.clipboard.writeText(link)
        .then(() => {
          message.success('推广链接已复制到剪贴板！');
        })
        .catch(() => {
          message.error('复制失败，请手动复制');
        });
    } else {
      // 没有找到产品专属code，需要先创建
      createProductLink(productId);
    }
  };
  
  // 创建产品推广链接
  const createProductLink = async (productId: number) => {
    try {
      message.loading('正在生成推广链接...', 1);
      
      const response = await fetch(`${config.apiUrl}/api/product-links`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ productId })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || '创建推广链接失败');
      }
      
      const data = await response.json();
      console.log('创建产品链接成功:', data);
      
      if (data.link && data.link.code) {
        // 更新本地链接数组
        setProductLinks(prev => [
          ...prev,
          {
            productId: productId,
            status: 'active',
            code: data.link.code
          }
        ]);
        
        // 复制新创建的链接
        const link = `${window.location.origin}/product/${data.link.code}`;
        navigator.clipboard.writeText(link)
          .then(() => {
            message.success('推广链接已创建并复制到剪贴板！');
          })
          .catch(() => {
            message.error('链接创建成功，但复制失败，请手动复制');
          });
      }
    } catch (error) {
      console.error('创建产品链接失败:', error);
      message.error(error instanceof Error ? error.message : '创建推广链接失败，请重试');
    }
  };

  // 未登录时显示的内容
  if (!isAuthenticated) {
    return (
      <PageContainer>
        <div className="text-center py-12">
          <PageTitle level={2}>请先登录</PageTitle>
          <PageDescription>
            您需要登录后才能查看产品列表
          </PageDescription>
          <div className="mt-6">
            <Link to="/login">
              <Button type="primary" size="large">
                前往登录
              </Button>
            </Link>
          </div>
        </div>
      </PageContainer>
    );
  }

  console.log('Products页面 - 渲染状态:', { 
    isAuthenticated, 
    loading, 
    error, 
    productsCount: products.length 
  });

  return (
    <PageContainer>
      <div className="page-header">
        <PageTitle level={2}>
          <AppstoreOutlined className="icon" />
          产品管理
        </PageTitle>
        <PageDescription>
          浏览和管理所有产品信息，生成推广链接获取佣金
        </PageDescription>
      </div>

      {/* 增加临时测试按钮，用于手动刷新数据 */}
      <div style={{ marginBottom: '16px' }}>
        <Button 
          type="primary" 
          onClick={() => fetchProducts()} 
          icon={<ReloadOutlined />}
        >
          手动刷新产品数据
        </Button>
      </div>

      <Card className="mb-6">
        <FilterContainer>
          <div className="filter-item" style={{ flex: 1 }}>
            <Input
              placeholder="搜索产品名称或描述"
              prefix={<SearchOutlined />}
              value={filter.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              allowClear
            />
          </div>
          <div className="filter-item">
            <Select
              placeholder="产品状态"
              style={{ width: '100%' }}
              allowClear
              value={filter.status || undefined}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="active">已上架</Option>
              <Option value="inactive">已下架</Option>
            </Select>
          </div>
          <div className="filter-actions">
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleResetFilters}
            >
              重置筛选
            </Button>
            {user?.role === 'admin' && (
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingProduct(null);
                  form.resetFields();
                  setIsModalVisible(true);
                }}
              >
                添加产品
              </Button>
            )}
          </div>
        </FilterContainer>

        {loading ? (
          <div className="py-20 text-center">
            <Spin size="large">
              <div className="content">
                <div style={{ marginTop: 16 }}>加载产品数据...</div>
              </div>
            </Spin>
          </div>
        ) : error ? (
          <div className="py-10 text-center">
            <Empty 
              description={<span style={{ color: colors.error }}>{error}</span>}
              image={Empty.PRESENTED_IMAGE_SIMPLE} 
            />
          </div>
        ) : products.length === 0 ? (
          <div className="py-10 text-center">
            <Empty description="暂无产品数据" />
          </div>
        ) : (
          <>
            <Row gutter={[16, 16]} className="products-grid">
              {products.map(product => (
                <Col xs={24} sm={12} md={8} lg={6} key={product.id}>
                  <ProductCard 
                    hoverable 
                    className="product-card product-card-body"
                  >
                    <div className="product-image-container">
                      <Image
                        src={
                          // 确保productImages[product.id]是数组再使用find方法
                          Array.isArray(productImages[product.id]) && productImages[product.id].length > 0 ?
                            (() => {
                              const imgUrl = productImages[product.id].find((img: ProductImage) => img.isPrimary)?.url || 
                                           productImages[product.id][0]?.url;
                              // 判断是否是完整URL
                              return imgUrl?.startsWith('http') ? imgUrl : `${config.apiUrl}${imgUrl}`;
                            })() :
                            'https://img.freepik.com/free-vector/hand-drawn-children-s-day-background_23-2149375747.jpg'
                        }
                        alt="儿童纪录片"
                        className="product-image"
                        fallback="https://img.freepik.com/free-vector/hand-drawn-children-s-day-background_23-2149375747.jpg"
                        preview={false}
                      />
                      {product.status === 'inactive' && (
                        <div className="product-status-overlay">
                          <Tag color="red">已下架</Tag>
                        </div>
                      )}
                    </div>
                    
                    <div className="product-content">
                      <Title level={5} className="product-title" ellipsis={{ rows: 1 }} title={product.title}>
                        {product.title}
                      </Title>
                      
                      <Paragraph className="product-description" ellipsis={{ rows: 2 }} title={product.description}>
                        {product.description}
                      </Paragraph>
                      
                      <div className="product-meta">
                        <div className="price-commission">
                          <div className="product-price">
                            <DollarOutlined /> <Text strong>¥{product.price.toFixed(2)}</Text>
                          </div>
                          <div className="product-commission">
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <Text>基础佣金率：</Text> <Text type="warning" strong style={{ marginLeft: '4px' }}>{product.baseCommissionRate}%</Text>
                            </div>
                            <div style={{ fontSize: '12px', color: '#999', marginTop: '2px', marginLeft: '16px' }}>
                              具体个人佣金率为主
                            </div>
                          </div>
                        </div>
                        
                        <div className="product-actions">
                          <Space>
                            <Tooltip title="查看详情">
                              <Button 
                                type="text" 
                                icon={<EyeOutlined />} 
                                size="small"
                                onClick={() => {
                                  // 查找产品的默认推广链接
                                  const defaultLink = productLinks.find((link: {productId: number, status: string, code: string}) => 
                                    link.productId === product.id && link.status === 'active'
                                  );
                                  
                                  if (defaultLink) {
                                    window.open(`/product/${defaultLink.code}`, '_blank');
                                  } else {
                                    message.warning('该产品暂无可用的推广链接');
                                  }
                                }}
                              />
                            </Tooltip>
                            
                            <Tooltip title="复制推广链接">
                              <Button 
                                type="text" 
                                icon={<CopyOutlined />} 
                                size="small" 
                                onClick={() => copyProductLink(product.id)}
                              />
                            </Tooltip>
                            
                            {user?.role === 'admin' && (
                              <>
                                <Tooltip title="编辑产品">
                                  <Button 
                                    type="text" 
                                    icon={<EditOutlined />} 
                                    size="small" 
                                    onClick={() => showEditModal(product)}
                                  />
                                </Tooltip>
                                
                                <Tooltip title="删除产品">
                                  <Button 
                                    type="text" 
                                    danger
                                    icon={<DeleteOutlined />} 
                                    size="small" 
                                    onClick={() => handleDeleteProduct(product.id)}
                                  />
                                </Tooltip>
                              </>
                            )}
                          </Space>
                        </div>
                      </div>
                    </div>
                  </ProductCard>
                </Col>
              ))}
            </Row>
            
            <div style={{ textAlign: 'right', marginTop: '20px' }}>
              <Pagination
                current={currentPage}
                pageSize={pageSize}
                total={totalProducts}
                onChange={handlePageChange}
                showSizeChanger={false}
                showQuickJumper
                showTotal={(total) => `共 ${total} 件产品`}
              />
            </div>
          </>
        )}
      </Card>

      {/* 编辑产品的弹窗 */}
      <Modal
        title={editingProduct ? '编辑产品' : '添加产品'}
        open={isModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={confirmLoading} onClick={handleSave}>
            保存
          </Button>,
        ]}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="产品名称"
            rules={[{ required: true, message: '请输入产品名称' }]}
          >
            <Input placeholder="请输入产品名称" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="产品描述"
            rules={[{ required: true, message: '请输入产品描述' }]}
          >
            <TextArea rows={4} placeholder="请输入产品描述" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <Input type="number" min={0} step={0.01} placeholder="请输入价格" prefix="¥" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="commissionRate"
                label="佣金比例"
                rules={[{ required: true, message: '请输入佣金比例' }]}
              >
                <Input type="number" min={0} max={100} placeholder="请输入佣金比例" suffix="%" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="imageUrl"
            label="产品图片URL"
            rules={[
              { required: true, message: '请输入产品图片URL' },
              { pattern: /^(http|https):\/\/[^ "]+$/, message: '请输入有效的URL地址，以http://或https://开头' }
            ]}
            extra="输入图片URL后将自动保存为产品图片，并设置为产品主图。URL必须以http://或https://开头"
          >
            <Input 
              placeholder="请输入产品图片URL" 
              id="imageUrl"
              addonAfter={
                <Tooltip title="预览图片">
                  <EyeOutlined onClick={() => {
                    const url = form.getFieldValue('imageUrl');
                    if (url) {
                      if (!/^(http|https):\/\/[^ "]+$/.test(url)) {
                        message.warning('请输入有效的图片URL地址，以http://或https://开头');
                        return;
                      }
                      Modal.info({
                        title: '图片预览',
                        content: (
                          <div style={{ textAlign: 'center', marginTop: 16 }}>
                            <Image src={url} alt="产品图片预览" fallback="https://img.freepik.com/free-vector/hand-drawn-children-s-day-background_23-2149375747.jpg" />
                          </div>
                        ),
                        width: 500,
                      });
                    } else {
                      message.warning('请先输入图片URL');
                    }
                  }} />
                </Tooltip>
              }
            />
          </Form.Item>
          
          <Form.Item
            name="status"
            label="产品状态"
            rules={[{ required: true, message: '请选择产品状态' }]}
          >
            <Select placeholder="请选择产品状态">
              <Option value="active">上架</Option>
              <Option value="inactive">下架</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </PageContainer>
  );
};

export default Products;