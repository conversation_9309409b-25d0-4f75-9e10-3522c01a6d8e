import React from 'react';

interface MarqueeProps {
  productName?: string;
  productPrice?: number;
  productDescription?: string;
}

export const Marquee: React.FC<MarqueeProps> = ({
  productName,
  productPrice,
  productDescription
}) => {
  const defaultText = "本网页数据安全由阿里云提供，严格按照法律法规和用户协议对您的信息进行全方位保护，请放心使用，测试仅供娱乐！";

  const displayText = productName && productPrice
    ? `🎉 ${productName} - ¥${productPrice} | ${productDescription || ''} 🎉`
    : defaultText;

  return (
    <div style={{
      color: 'white',
      fontSize: '13px',
      padding: '6px',
      overflow: 'hidden',
      whiteSpace: 'nowrap',
      animation: 'marquee 20s linear infinite',
      background: 'linear-gradient(90deg, #ff6b35, #f7931e)',
    }}>
      {displayText}
    </div>
  );
};
