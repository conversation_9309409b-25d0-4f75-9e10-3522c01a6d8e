import { Product } from './src/models/index.js';
import sequelize from './src/config/database.js';

async function fixBaziProductType() {
  try {
    console.log('🔧 开始修复八字产品类型设置...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('✅ 数据库连接成功');

    // 查找所有八字相关产品
    const baziProducts = await Product.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.or]: [
            { [sequelize.Sequelize.Op.like]: '%八字%' },
            { [sequelize.Sequelize.Op.like]: '%命理%' },
            { [sequelize.Sequelize.Op.like]: '%命盘%' },
            { [sequelize.Sequelize.Op.like]: '%算命%' }
          ]
        }
      }
    });

    console.log(`🔍 找到 ${baziProducts.length} 个八字相关产品:`);
    
    for (const product of baziProducts) {
      console.log(`📋 产品ID: ${product.id}, 标题: ${product.title}, 当前类型: ${product.type}`);
      
      // 如果产品类型不是service，则修复为service
      if (product.type !== 'service') {
        await product.update({ type: 'service' });
        console.log(`✅ 已修复产品ID ${product.id} 的类型: ${product.type} -> service`);
      } else {
        console.log(`✅ 产品ID ${product.id} 类型已正确设置为 service`);
      }
    }

    console.log('\n🎉 八字产品类型修复完成！');
    console.log('\n📊 修复结果总结:');
    
    // 再次查询验证结果
    const updatedProducts = await Product.findAll({
      where: {
        title: {
          [sequelize.Sequelize.Op.or]: [
            { [sequelize.Sequelize.Op.like]: '%八字%' },
            { [sequelize.Sequelize.Op.like]: '%命理%' },
            { [sequelize.Sequelize.Op.like]: '%命盘%' },
            { [sequelize.Sequelize.Op.like]: '%算命%' }
          ]
        }
      }
    });
    
    updatedProducts.forEach(product => {
      console.log(`   - ID: ${product.id}, 标题: ${product.title}, 类型: ${product.type}`);
    });
    
  } catch (error) {
    console.error('❌ 修复八字产品类型失败:', error);
  } finally {
    await sequelize.close();
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  fixBaziProductType();
}

export default fixBaziProductType;