import jwt from 'jsonwebtoken';
import { User } from '../models/index.js';

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-2024';  // 优先使用环境变量

export const auth = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');
    console.log('验证token:', token);
    
    if (!token) {
      return res.status(401).json({ message: '请先登录' });
    }

    const decoded = jwt.verify(token, JWT_SECRET);
    console.log('token解码结果:', decoded);
    
    const user = await User.findOne({ where: { id: decoded.userId } });
    console.log('查找到的用户:', user ? { id: user.id, role: user.role } : null);

    if (!user) {
      return res.status(401).json({ message: '用户不存在' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('认证失败:', error);
    res.status(401).json({ message: '认证失败', error: error.message });
  }
};

export const adminAuth = async (req, res, next) => {
  try {
    await auth(req, res, () => {
      if (req.user.role !== 'admin') {
        return res.status(403).json({ message: '需要管理员权限' });
      }
      next();
    });
  } catch (error) {
    console.error('管理员认证失败:', error);
    res.status(403).json({ message: '需要管理员权限' });
  }
};