/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#b87c3d',
        'primary-light': '#ffa648',
        'bg-light': '#FEF4E0',
        'color-border': '#E1B467',
        'color-text': '#3f2410',
        'color-light': '#b87c3d',
        'color-light1': '#FEF1C3',
        'color-red': '#fb5757',
        'color-warning': '#ffa648',
        'color-desc': 'rgba(63,36,16,0.7)',
        'color-highlight': '#fe8d59',
        'bg-gray': '#EDEDED',
        'bg-color': '#AD7E47',
        'bg-body': '#ecedee',
      },
      backgroundImage: {
        'gradient-main': 'linear-gradient(141deg,#e87947 6%,#eb8833 52%,#e9543f 100%)',
      },
      fontFamily: {
        sans: ['PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Arial', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
