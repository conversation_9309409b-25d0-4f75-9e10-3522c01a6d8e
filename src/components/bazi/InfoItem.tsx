import type { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface InfoItemProps {
  label: string;
  value?: ReactNode;
  className?: string;
  labelClassName?: string;
  valueClassName?: string;
  direction?: 'row' | 'col'; // 'row' is horizontal on sm+, 'col' is always vertical
}

export function InfoItem({ label, value, className, labelClassName, valueClassName, direction = 'row' }: InfoItemProps) {
  if (value === undefined || value === null || value === '') {
    return null;
  }
  
  const containerClasses = cn(
    "py-2 sm:py-2.5", // Increased padding for better vertical spacing
    direction === 'row' ? "flex flex-col sm:flex-row sm:items-start sm:justify-between" : "flex flex-col",
    className
  );

  const labelClasses = cn(
    "font-semibold text-primary text-sm", // Consistent label size, was text-xs sm:text-sm
    direction === 'row' ? "sm:w-1/3 mb-0.5 sm:mb-0 shrink-0" : "mb-1", // Ensure label doesn't shrink excessively, added mb-1 for col direction
    labelClassName
  );

  const valueClasses = cn(
    "text-foreground text-sm sm:text-base", 
     direction === 'row' ? "sm:w-2/3 sm:text-left" : "",
    "break-words", // Allow long words to break
    valueClassName
  );

  return (
    <div className={containerClasses}>
      <dt className={labelClasses}>{label}:</dt>
      <dd className={valueClasses}>{value}</dd>
    </div>
  );
}

export function ParagraphText({ text }: { text: string | undefined | null }) {
  if (!text) return null;
  // Handles both \r\n and <BR> style line breaks
  const paragraphs = text.split(/\r\n|<br\/?>|\n/gi).filter(p => p.trim() !== "");
  return (
    <>
      {paragraphs.map((p, index) => (
        <p key={index} className="mb-2 sm:mb-2.5 last:mb-0 text-sm sm:text-base" style={{ whiteSpace: 'pre-line' }}>
          {/* Increased base text size and margin bottom */}
          {p.trim()}
        </p>
      ))}
    </>
  );
} 