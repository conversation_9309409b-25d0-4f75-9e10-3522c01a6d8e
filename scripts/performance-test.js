#!/usr/bin/env node

/**
 * 性能测试脚本 - 产品级性能监控
 * 
 * 功能：
 * - 页面加载性能测试
 * - API响应时间测试
 * - 内存使用监控
 * - 并发用户测试
 * - 生成性能报告
 */

const puppeteer = require('puppeteer');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  baseUrl: process.env.TEST_URL || 'http://localhost:3000',
  apiUrl: process.env.API_URL || 'http://localhost:3005',
  concurrentUsers: parseInt(process.env.CONCURRENT_USERS) || 10,
  testDuration: parseInt(process.env.TEST_DURATION) || 60000, // 60秒
  outputDir: './performance-reports',
  headless: process.env.HEADLESS !== 'false'
};

// 确保输出目录存在
if (!fs.existsSync(CONFIG.outputDir)) {
  fs.mkdirSync(CONFIG.outputDir, { recursive: true });
}

class PerformanceTest {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      config: CONFIG,
      pageLoad: [],
      apiResponse: [],
      memory: [],
      errors: []
    };
  }

  // 页面加载性能测试
  async testPageLoad() {
    console.log('🚀 开始页面加载性能测试...');
    
    const browser = await puppeteer.launch({ 
      headless: CONFIG.headless,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
      const pages = [
        '/',
        '/bazi-demo',
        '/login',
        '/register'
      ];

      for (const pagePath of pages) {
        const page = await browser.newPage();
        
        // 启用性能监控
        await page.setCacheEnabled(false);
        
        const startTime = Date.now();
        
        try {
          // 导航到页面
          const response = await page.goto(`${CONFIG.baseUrl}${pagePath}`, {
            waitUntil: 'networkidle2',
            timeout: 30000
          });

          const loadTime = Date.now() - startTime;

          // 获取性能指标
          const metrics = await page.metrics();
          
          // 获取Web Vitals
          const webVitals = await page.evaluate(() => {
            return new Promise((resolve) => {
              if ('PerformanceObserver' in window) {
                const vitals = {};
                
                // LCP
                new PerformanceObserver((list) => {
                  const entries = list.getEntries();
                  vitals.lcp = entries[entries.length - 1]?.startTime;
                }).observe({ entryTypes: ['largest-contentful-paint'] });
                
                // FID
                new PerformanceObserver((list) => {
                  const entries = list.getEntries();
                  vitals.fid = entries[0]?.processingStart - entries[0]?.startTime;
                }).observe({ entryTypes: ['first-input'] });
                
                // CLS
                let clsValue = 0;
                new PerformanceObserver((list) => {
                  const entries = list.getEntries();
                  entries.forEach((entry) => {
                    if (!entry.hadRecentInput) {
                      clsValue += entry.value;
                    }
                  });
                  vitals.cls = clsValue;
                }).observe({ entryTypes: ['layout-shift'] });
                
                setTimeout(() => resolve(vitals), 2000);
              } else {
                resolve({});
              }
            });
          });

          this.results.pageLoad.push({
            path: pagePath,
            loadTime,
            status: response.status(),
            metrics: {
              ...metrics,
              ...webVitals
            },
            timestamp: new Date().toISOString()
          });

          console.log(`✅ ${pagePath}: ${loadTime}ms`);
          
        } catch (error) {
          this.results.errors.push({
            type: 'page_load',
            path: pagePath,
            error: error.message,
            timestamp: new Date().toISOString()
          });
          console.error(`❌ ${pagePath}: ${error.message}`);
        }
        
        await page.close();
      }
    } finally {
      await browser.close();
    }
  }

  // API响应时间测试
  async testApiResponse() {
    console.log('🔌 开始API响应时间测试...');
    
    const endpoints = [
      { method: 'GET', path: '/api/health' },
      { method: 'GET', path: '/api/products' },
      { method: 'GET', path: '/api/users/profile' },
      { method: 'POST', path: '/api/auth/login', data: { email: '<EMAIL>', password: 'password' } }
    ];

    for (const endpoint of endpoints) {
      const startTime = Date.now();
      
      try {
        const config = {
          method: endpoint.method,
          url: `${CONFIG.apiUrl}${endpoint.path}`,
          timeout: 10000,
          ...(endpoint.data && { data: endpoint.data })
        };

        const response = await axios(config);
        const responseTime = Date.now() - startTime;

        this.results.apiResponse.push({
          method: endpoint.method,
          path: endpoint.path,
          responseTime,
          status: response.status,
          dataSize: JSON.stringify(response.data).length,
          timestamp: new Date().toISOString()
        });

        console.log(`✅ ${endpoint.method} ${endpoint.path}: ${responseTime}ms`);
        
      } catch (error) {
        const responseTime = Date.now() - startTime;
        
        this.results.errors.push({
          type: 'api_response',
          method: endpoint.method,
          path: endpoint.path,
          error: error.message,
          responseTime,
          timestamp: new Date().toISOString()
        });
        
        console.error(`❌ ${endpoint.method} ${endpoint.path}: ${error.message}`);
      }
    }
  }

  // 内存使用监控
  async testMemoryUsage() {
    console.log('💾 开始内存使用监控...');
    
    const browser = await puppeteer.launch({ 
      headless: CONFIG.headless,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
      const page = await browser.newPage();
      await page.goto(`${CONFIG.baseUrl}/bazi-demo`);
      
      // 监控内存使用
      const monitorDuration = 30000; // 30秒
      const interval = 1000; // 每秒检查一次
      const startTime = Date.now();
      
      while (Date.now() - startTime < monitorDuration) {
        const metrics = await page.metrics();
        
        this.results.memory.push({
          jsHeapUsedSize: metrics.JSHeapUsedSize,
          jsHeapTotalSize: metrics.JSHeapTotalSize,
          timestamp: new Date().toISOString()
        });
        
        await new Promise(resolve => setTimeout(resolve, interval));
      }
      
      await page.close();
    } finally {
      await browser.close();
    }
  }

  // 并发用户测试
  async testConcurrentUsers() {
    console.log(`👥 开始并发用户测试 (${CONFIG.concurrentUsers} 用户)...`);
    
    const promises = [];
    
    for (let i = 0; i < CONFIG.concurrentUsers; i++) {
      promises.push(this.simulateUser(i));
    }
    
    await Promise.all(promises);
  }

  // 模拟单个用户行为
  async simulateUser(userId) {
    const browser = await puppeteer.launch({ 
      headless: CONFIG.headless,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    try {
      const page = await browser.newPage();
      const startTime = Date.now();
      
      // 用户行为序列
      const actions = [
        () => page.goto(`${CONFIG.baseUrl}/`),
        () => page.goto(`${CONFIG.baseUrl}/bazi-demo`),
        () => page.click('button[type="button"]').catch(() => {}), // 可能不存在的按钮
        () => page.type('input[placeholder*="姓名"]', `测试用户${userId}`).catch(() => {}),
        () => page.waitForTimeout(2000)
      ];
      
      for (const action of actions) {
        try {
          await action();
        } catch (error) {
          // 忽略单个动作的错误，继续执行
        }
      }
      
      const duration = Date.now() - startTime;
      console.log(`✅ 用户 ${userId} 完成测试: ${duration}ms`);
      
      await page.close();
    } catch (error) {
      this.results.errors.push({
        type: 'concurrent_user',
        userId,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      await browser.close();
    }
  }

  // 生成性能报告
  generateReport() {
    console.log('📊 生成性能报告...');
    
    const report = {
      ...this.results,
      summary: {
        pageLoad: {
          average: this.calculateAverage(this.results.pageLoad, 'loadTime'),
          min: Math.min(...this.results.pageLoad.map(p => p.loadTime)),
          max: Math.max(...this.results.pageLoad.map(p => p.loadTime))
        },
        apiResponse: {
          average: this.calculateAverage(this.results.apiResponse, 'responseTime'),
          min: Math.min(...this.results.apiResponse.map(a => a.responseTime)),
          max: Math.max(...this.results.apiResponse.map(a => a.responseTime))
        },
        memory: {
          peak: Math.max(...this.results.memory.map(m => m.jsHeapUsedSize)),
          average: this.calculateAverage(this.results.memory, 'jsHeapUsedSize')
        },
        errors: this.results.errors.length
      }
    };
    
    // 保存报告
    const filename = `performance-report-${Date.now()}.json`;
    const filepath = path.join(CONFIG.outputDir, filename);
    fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
    
    // 生成HTML报告
    this.generateHtmlReport(report, filepath.replace('.json', '.html'));
    
    console.log(`📄 报告已保存: ${filepath}`);
    
    return report;
  }

  // 生成HTML报告
  generateHtmlReport(report, filepath) {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; }
        .metric { margin: 10px 0; }
        .error { color: red; }
        .success { color: green; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>性能测试报告</h1>
    <p>测试时间: ${report.timestamp}</p>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <div class="metric">页面加载平均时间: ${report.summary.pageLoad.average.toFixed(2)}ms</div>
        <div class="metric">API响应平均时间: ${report.summary.apiResponse.average.toFixed(2)}ms</div>
        <div class="metric">内存峰值使用: ${(report.summary.memory.peak / 1024 / 1024).toFixed(2)}MB</div>
        <div class="metric ${report.summary.errors > 0 ? 'error' : 'success'}">错误数量: ${report.summary.errors}</div>
    </div>
    
    <h2>详细数据</h2>
    <pre>${JSON.stringify(report, null, 2)}</pre>
</body>
</html>`;
    
    fs.writeFileSync(filepath, html);
  }

  // 计算平均值
  calculateAverage(array, property) {
    if (array.length === 0) return 0;
    const sum = array.reduce((acc, item) => acc + item[property], 0);
    return sum / array.length;
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🎯 开始性能测试...\n');
    
    try {
      await this.testPageLoad();
      await this.testApiResponse();
      await this.testMemoryUsage();
      await this.testConcurrentUsers();
      
      const report = this.generateReport();
      
      console.log('\n✅ 性能测试完成!');
      console.log(`📊 摘要:`);
      console.log(`   页面加载: ${report.summary.pageLoad.average.toFixed(2)}ms (平均)`);
      console.log(`   API响应: ${report.summary.apiResponse.average.toFixed(2)}ms (平均)`);
      console.log(`   内存峰值: ${(report.summary.memory.peak / 1024 / 1024).toFixed(2)}MB`);
      console.log(`   错误数量: ${report.summary.errors}`);
      
    } catch (error) {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    }
  }
}

// 运行测试
if (require.main === module) {
  const test = new PerformanceTest();
  test.runAllTests().catch(console.error);
}

module.exports = PerformanceTest;
