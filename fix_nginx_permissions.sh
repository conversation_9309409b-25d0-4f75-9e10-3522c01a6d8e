#!/bin/bash

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m'

# 定义变量
SERVER_USER="root"
SERVER_IP="************"
APP_NAME="commission-platform"

# 输入服务器密码
echo -e "${GREEN}请输入服务器密码: ${NC}"
read -s SERVER_PASSWORD
echo

# 远程执行命令函数
remote_exec() {
    echo -e "${GREEN}执行: $1${NC}"
    sshpass -p "$SERVER_PASSWORD" ssh -o StrictHostKeyChecking=no $SERVER_USER@$SERVER_IP "$1"
}

# 远程复制文件函数
remote_copy() {
    echo -e "${GREEN}复制: $1 到 $2${NC}"
    if command -v rsync &> /dev/null; then
        sshpass -p "$SERVER_PASSWORD" rsync -avz --progress -e "ssh -o StrictHostKeyChecking=no" "$1" "$SERVER_USER@$SERVER_IP:$2"
    else
        sshpass -p "$SERVER_PASSWORD" scp -o StrictHostKeyChecking=no -r "$1" "$SERVER_USER@$SERVER_IP:$2"
    fi
}

echo -e "${GREEN}开始修复Nginx配置和权限...${NC}"

# 1. 确保目录存在并设置正确权限
echo -e "${YELLOW}创建必要目录...${NC}"
remote_exec "mkdir -p /data/commission-platform"
remote_exec "mkdir -p /var/log/nginx"

# 2. 设置目录权限
echo -e "${YELLOW}设置目录权限...${NC}"
remote_exec "chown -R $SERVER_USER:$SERVER_USER /data/commission-platform"
remote_exec "chmod -R 755 /data/commission-platform"
remote_exec "chown -R $SERVER_USER:$SERVER_USER /var/log/nginx"
remote_exec "chmod -R 755 /var/log/nginx"

# 3. 上传新的Nginx配置
echo -e "${YELLOW}上传Nginx配置...${NC}"
remote_copy "nginx/commission-platform.conf" "/etc/nginx/conf.d/commission-platform.conf"
remote_exec "chown $SERVER_USER:$SERVER_USER /etc/nginx/conf.d/commission-platform.conf"
remote_exec "chmod 644 /etc/nginx/conf.d/commission-platform.conf"

# 4. 检查Nginx配置
echo -e "${YELLOW}检查Nginx配置...${NC}"
if ! remote_exec "nginx -t"; then
    echo -e "${RED}Nginx配置测试失败${NC}"
    exit 1
fi

# 5. 重启Nginx
echo -e "${YELLOW}重启Nginx...${NC}"
remote_exec "systemctl restart nginx"

# 6. 检查Nginx状态
echo -e "${YELLOW}检查Nginx状态...${NC}"
remote_exec "systemctl status nginx | grep active"

echo -e "${GREEN}修复完成！${NC}"
echo -e "${GREEN}请访问 https://ye.bzcy.xyz 检查是否正常${NC}"
