{"timestamp": "2025-03-13T14:03:03.132Z", "summary": {"total": 7, "passed": 3, "failed": 4, "skipped": 0}, "details": [{"name": "用户登录 - 管理员", "result": "passed"}, {"name": "用户登录 - 普通用户", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "Invalid credentials"}}, {"name": "获取个人资料", "result": "failed", "error": "Request failed with status code 500", "details": {"message": "获取用户资料失败", "error": "sequelize is not defined"}}, {"name": "浏览产品列表", "result": "failed", "error": "产品列表格式错误"}, {"name": "查询佣金记录", "result": "passed"}, {"name": "查询提现记录", "result": "passed"}, {"name": "API健康检查", "result": "failed", "error": "API健康检查失败: Request failed with status code 401"}], "config": {"baseURL": "https://ye.bzcy.xyz", "includeAdmin": false}}