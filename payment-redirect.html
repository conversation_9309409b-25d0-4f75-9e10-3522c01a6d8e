<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>支付完成 - 正在跳转...</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: #333;
    }
    .container {
      text-align: center;
      background-color: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 90%;
    }
    h1 {
      font-size: 24px;
      margin-bottom: 20px;
      color: #2c3e50;
    }
    p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    .loading {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="loading"></div>
    <h1>支付成功，正在跳转...</h1>
    <p id="message">正在为您准备资源，请稍候...</p>
  </div>

  <script>
    // 获取URL参数的函数
    function getQueryParam(param) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(param);
    }

    // 处理跳转逻辑
    window.onload = function() {
      const messageElement = document.getElementById('message');
      
      // 获取产品类型和订单ID
      const productType = getQueryParam('product_type');
      const orderId = getQueryParam('order_id');
      
      // 根据产品类型确定跳转地址
      let redirectUrl = '';
      if (productType === 'deepseek') {
        // Deepseek AI课程链接
        redirectUrl = 'https://pan.quark.cn/s/59c6d1f5973e';
        messageElement.textContent = '正在为您跳转到Deepseek AI课程资源页...';
      } else {
        // 儿童纪录片链接
        redirectUrl = 'https://pan.baidu.com/s/1l8Q8zYJUtYUC1fKG32FM0A?pwd=qbci';
        messageElement.textContent = '正在为您跳转到儿童纪录片资源页...';
      }
      
      // 记录跳转信息
      console.log(`订单${orderId}支付完成，产品类型: ${productType}，即将跳转到: ${redirectUrl}`);
      
      // 延迟2秒后跳转，让用户有时间看到状态
      setTimeout(function() {
        window.location.href = redirectUrl;
      }, 2000);
    };
  </script>
</body>
</html>
