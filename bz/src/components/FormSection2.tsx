import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { NameField, GenderField, BirthdayField, AddressField } from '@/components/FormFields';
import { useAppStore } from '@/store';

export const FormSection2: React.FC = () => {
  const { formData, setFormData, createTrade, loading } = useAppStore();

  const handleSubmit = async () => {
    if (loading) return;

    // 基本验证
    if (!formData.name || formData.name.length < 2) {
      alert('请输入您的姓名');
      return;
    }

    if (!formData.sex) {
      alert('请选择您的性别');
      return;
    }

    if (!formData.birthday) {
      alert('请选择您的出生时间');
      return;
    }

    if (!formData.address || formData.address.length < 2) {
      alert('请选择您的出生地址');
      return;
    }

    // 确认对话框
    const confirmed = window.confirm(`
请确认生日是否正确！

姓名：${formData.name}
性别：${formData.sex === '1' ? '男' : '女'}
生日：${formData.birthday}
出生地址：${formData.address}
    `);

    if (confirmed) {
      await createTrade();
    }
  };

  return (
    <div className="main-form">
      {/* 表单内容 */}
      <div className="inner">
        <NameField />
        <GenderField />
        <BirthdayField />
        <AddressField />
      </div>

      {/* 提交按钮 */}
      <motion.div
        className="submit"
        onClick={handleSubmit}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
      >
        {loading && (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
          </div>
        )}
      </motion.div>
    </div>
  );
};
