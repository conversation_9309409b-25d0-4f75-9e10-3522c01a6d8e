import sequelize from '../config/database.js';
import { DataTypes } from 'sequelize';

// 创建 PaymentConfig 表
const createPaymentConfigTable = async () => {
  try {
    console.log('开始创建 PaymentConfig 表...');

    await sequelize.getQueryInterface().createTable('PaymentConfigs', {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      alipayAppId: {
        type: DataTypes.STRING,
        allowNull: false
      },
      alipayPrivateKey: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      alipayPublicKey: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false
      }
    });

    console.log('PaymentConfig 表创建成功!');
  } catch (error) {
    console.error('创建 PaymentConfig 表失败:', error);
    
    // 检查表是否已存在
    if (error.name === 'SequelizeUniqueConstraintError' || error.message.includes('already exists')) {
      console.log('PaymentConfig 表已存在，跳过创建');
    } else {
      throw error;
    }
  }
};

// 执行迁移
createPaymentConfigTable()
  .then(() => {
    console.log('迁移完成');
    process.exit(0);
  })
  .catch(error => {
    console.error('迁移失败:', error);
    process.exit(1);
  });
