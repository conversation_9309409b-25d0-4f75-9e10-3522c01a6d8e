import express from 'express';
import { auth } from '../middleware/auth.js';
import { ProductLink, Product, User } from '../models/index.js';
import { nanoid } from 'nanoid/non-secure';

const router = express.Router();

// 生成推广链接
router.post('/', auth, async (req, res) => {
  try {
    const { productId } = req.body;
    const agentId = req.user.id;

    // 验证产品是否存在且状态为active
    const product = await Product.findOne({
      where: { id: productId, status: 'active' }
    });

    if (!product) {
      return res.status(404).json({ message: '产品不存在或已下架' });
    }

    // 验证代理用户状态
    const agent = await User.findOne({
      where: { id: agentId, status: 'approved' }
    });

    if (!agent) {
      return res.status(403).json({ message: '您的账号未获得审核通过' });
    }

    // 检查是否已经存在该产品的推广链接
    const existingLink = await ProductLink.findOne({
      where: { productId, agentId }
    });

    if (existingLink) {
      return res.json({
        message: '推广链接已存在',
        link: existingLink
      });
    }

    // 生成唯一的推广码
    const code = nanoid(10);

    // 创建推广链接
    const productLink = await ProductLink.create({
      code,
      productId,
      agentId,
      commissionRate: 80 // 默认分润比例80%
    });

    res.status(201).json({
      message: '推广链接生成成功',
      link: productLink
    });
  } catch (error) {
    console.error('生成推广链接失败:', error);
    res.status(500).json({ message: '生成推广链接失败，请重试' });
  }
});

// 获取代理的所有推广链接
router.get('/', auth, async (req, res) => {
  try {
    const links = await ProductLink.findAll({
      where: { agentId: req.user.id },
      include: [{
        model: Product,
        as: 'product',
        attributes: ['title', 'price', 'description']
      }],
      order: [['createdAt', 'DESC']]
    });

    res.json(links);
  } catch (error) {
    console.error('获取推广链接失败:', error);
    res.status(500).json({ message: '获取推广链接失败，请重试' });
  }
});

// 通过推广码获取产品信息（公开访问）
router.get('/:code', async (req, res) => {
  try {
    const { code } = req.params;
    console.log('🔍 查询推广码:', code);

    const productLink = await ProductLink.findOne({
      where: { code },
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'title', 'description', 'price', 'status']
      }, {
        model: User,
        as: 'linkAgent',
        attributes: ['name']
      }]
    });

    console.log('📦 查询结果:', productLink ? {
      id: productLink.id,
      code: productLink.code,
      status: productLink.status,
      productId: productLink.productId,
      agentId: productLink.agentId,
      product: productLink.product ? {
        title: productLink.product.title,
        status: productLink.product.status
      } : null
    } : 'null');

    if (!productLink) {
      console.log('❌ 推广链接不存在');
      return res.status(404).json({ message: '推广链接不存在' });
    }

    if (productLink.status !== 'active') {
      return res.status(404).json({ message: '推广链接已失效' });
    }

    if (!productLink.product || productLink.product.status !== 'active') {
      return res.status(404).json({ message: '产品已下架' });
    }

    // 增加点击次数
    await productLink.increment('clicks');

    // 格式化返回数据
    const formattedData = {
      id: productLink.id,
      code: productLink.code,
      status: productLink.status,
      product: {
        id: productLink.product.id,
        title: productLink.product.title,
        description: productLink.product.description,
        price: Number(productLink.product.price)
      },
      linkAgent: productLink.linkAgent ? {
        name: productLink.linkAgent.name
      } : null
    };

    console.log('返回数据:', formattedData);
    res.json(formattedData);
  } catch (error) {
    console.error('获取产品信息失败:', error);
    res.status(500).json({ 
      message: '获取产品信息失败，请重试',
      error: error.message 
    });
  }
});

export default router;
