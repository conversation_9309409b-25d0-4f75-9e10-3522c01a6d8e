// 增强的八字表单状态管理Hook

import { useReducer, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import { BaziFormData, FormState, ErrorCode } from '../types/bazi-enhanced';
import { ValidationUtils, StorageUtils, ErrorUtils } from '../utils/bazi-utils';

// 状态动作类型
type FormAction = 
  | { type: 'SET_FIELD'; field: keyof BaziFormData; value: any }
  | { type: 'SET_MULTIPLE_FIELDS'; fields: Partial<BaziFormData> }
  | { type: 'SET_LOADING'; loading: boolean }
  | { type: 'SET_SUBMITTING'; submitting: boolean }
  | { type: 'SET_ERRORS'; errors: Record<string, string> }
  | { type: 'SET_TOUCHED'; field: string; touched: boolean }
  | { type: 'VALIDATE_FORM' }
  | { type: 'RESET_FORM' }
  | { type: 'LOAD_FROM_STORAGE' };

// 初始状态
const initialState: FormState = {
  data: {
    calendarType: 'gregorian',
    isAccurate: true
  },
  loading: false,
  submitting: false,
  errors: {},
  touched: {},
  isValid: false
};

// 状态reducer
function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'SET_FIELD':
      const newData = { ...state.data, [action.field]: action.value };
      const errors = ValidationUtils.validateFormData(newData);
      return {
        ...state,
        data: newData,
        errors,
        isValid: Object.keys(errors).length === 0
      };

    case 'SET_MULTIPLE_FIELDS':
      const updatedData = { ...state.data, ...action.fields };
      const validationErrors = ValidationUtils.validateFormData(updatedData);
      return {
        ...state,
        data: updatedData,
        errors: validationErrors,
        isValid: Object.keys(validationErrors).length === 0
      };

    case 'SET_LOADING':
      return { ...state, loading: action.loading };

    case 'SET_SUBMITTING':
      return { ...state, submitting: action.submitting };

    case 'SET_ERRORS':
      return { 
        ...state, 
        errors: action.errors,
        isValid: Object.keys(action.errors).length === 0
      };

    case 'SET_TOUCHED':
      return {
        ...state,
        touched: { ...state.touched, [action.field]: action.touched }
      };

    case 'VALIDATE_FORM':
      const formErrors = ValidationUtils.validateFormData(state.data);
      return {
        ...state,
        errors: formErrors,
        isValid: Object.keys(formErrors).length === 0
      };

    case 'RESET_FORM':
      return {
        ...initialState,
        data: { ...initialState.data }
      };

    case 'LOAD_FROM_STORAGE':
      const savedData = StorageUtils.loadFormData();
      if (savedData) {
        const loadErrors = ValidationUtils.validateFormData(savedData);
        return {
          ...state,
          data: { ...state.data, ...savedData },
          errors: loadErrors,
          isValid: Object.keys(loadErrors).length === 0
        };
      }
      return state;

    default:
      return state;
  }
}

// 自定义Hook
export function useBaziForm(options?: {
  onSubmit?: (data: BaziFormData) => Promise<void>;
  autoSave?: boolean;
  loadFromStorage?: boolean;
}) {
  const [state, dispatch] = useReducer(formReducer, initialState);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();
  const submitAttemptRef = useRef(0);

  // 初始化：从本地存储加载数据
  useEffect(() => {
    if (options?.loadFromStorage !== false) {
      dispatch({ type: 'LOAD_FROM_STORAGE' });
    }
  }, [options?.loadFromStorage]);

  // 自动保存
  useEffect(() => {
    if (options?.autoSave !== false && Object.keys(state.data).length > 1) {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      
      autoSaveTimeoutRef.current = setTimeout(() => {
        StorageUtils.saveFormData(state.data);
      }, 1000); // 1秒后保存
    }

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [state.data, options?.autoSave]);

  // 设置字段值
  const setField = useCallback((field: keyof BaziFormData, value: any) => {
    dispatch({ type: 'SET_FIELD', field, value });
    dispatch({ type: 'SET_TOUCHED', field: field as string, touched: true });
  }, []);

  // 设置多个字段
  const setFields = useCallback((fields: Partial<BaziFormData>) => {
    dispatch({ type: 'SET_MULTIPLE_FIELDS', fields });
    Object.keys(fields).forEach(field => {
      dispatch({ type: 'SET_TOUCHED', field, touched: true });
    });
  }, []);

  // 验证表单
  const validateForm = useCallback(() => {
    dispatch({ type: 'VALIDATE_FORM' });
    return state.isValid;
  }, [state.isValid]);

  // 重置表单
  const resetForm = useCallback(() => {
    dispatch({ type: 'RESET_FORM' });
    StorageUtils.clearFormData();
    message.success('表单已重置');
  }, []);

  // 提交表单 - 支持真实API调用
  const submitForm = useCallback(async () => {
    try {
      dispatch({ type: 'SET_SUBMITTING', submitting: true });
      submitAttemptRef.current += 1;

      // 最终验证
      const errors = ValidationUtils.validateFormData(state.data);
      if (Object.keys(errors).length > 0) {
        dispatch({ type: 'SET_ERRORS', errors });
        message.error('请检查表单信息');
        return false;
      }

      // 确保数据完整性
      if (!state.data.name || !state.data.gender || !state.data.birthDate ||
          !state.data.birthTime || !state.data.location) {
        message.error('请填写完整的出生信息');
        return false;
      }

      // 调用提交回调
      if (options?.onSubmit) {
        await options.onSubmit(state.data as BaziFormData);
      } else {
        // 如果没有提供onSubmit回调，使用默认的API调用
        await defaultApiSubmit(state.data as BaziFormData);
      }

      // 保存到历史记录
      StorageUtils.saveToHistory(state.data as BaziFormData);

      // 清除当前表单数据
      StorageUtils.clearFormData();

      message.success('提交成功！');
      return true;

    } catch (error) {
      console.error('Submit error:', error);

      // 错误处理
      const errorMessage = ErrorUtils.formatError(error);
      message.error(errorMessage);

      // 网络错误重试机制
      if (ErrorUtils.isNetworkError(error) && submitAttemptRef.current < 3) {
        message.info(`网络错误，将在3秒后自动重试 (${submitAttemptRef.current}/3)`);
        setTimeout(() => {
          submitForm();
        }, 3000);
      }

      return false;
    } finally {
      dispatch({ type: 'SET_SUBMITTING', submitting: false });
    }
  }, [state.data, options?.onSubmit]);

  // 默认API提交函数
  const defaultApiSubmit = useCallback(async (formData: BaziFormData) => {
    // 准备API请求数据
    const apiData = {
      productCode: 'default-bazi-product', // 可以通过options传入
      name: formData.name,
      gender: formData.gender,
      calendarType: formData.calendarType || 'gregorian',
      birthYear: parseInt(formData.birthDate.split('-')[0]),
      birthMonth: parseInt(formData.birthDate.split('-')[1]),
      birthDay: parseInt(formData.birthDate.split('-')[2]),
      birthHour: formData.birthTime?.hour || 0,
      birthMinute: formData.birthTime?.minute || 0,
      birthProvince: formData.location?.province || '',
      birthCity: formData.location?.city || ''
    };

    console.log('默认API提交八字数据:', apiData);

    // 调用后端API
    const response = await fetch('/api/bazi/orders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(apiData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || '创建订单失败');
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || '创建订单失败');
    }

    // 处理成功响应
    if (result.data?.paymentUrl) {
      // 跳转到支付页面
      window.location.href = result.data.paymentUrl;
    }

    return result;
  }, []);

  // 获取字段错误信息
  const getFieldError = useCallback((field: string) => {
    return state.touched[field] ? state.errors[field] : undefined;
  }, [state.errors, state.touched]);

  // 检查字段是否有错误
  const hasFieldError = useCallback((field: string) => {
    return !!(state.touched[field] && state.errors[field]);
  }, [state.errors, state.touched]);

  // 获取表单进度
  const getFormProgress = useCallback(() => {
    const requiredFields = ['name', 'gender', 'birthDate', 'birthTime', 'location'];
    const completedFields = requiredFields.filter(field => {
      const value = state.data[field as keyof BaziFormData];
      return value !== undefined && value !== null && value !== '';
    });
    
    return Math.round((completedFields.length / requiredFields.length) * 100);
  }, [state.data]);

  // 获取历史记录
  const getHistory = useCallback(() => {
    return StorageUtils.getHistory();
  }, []);

  // 从历史记录加载
  const loadFromHistory = useCallback((historyItem: any) => {
    const { id, timestamp, ...formData } = historyItem;
    setFields(formData);
    message.success('已加载历史记录');
  }, [setFields]);

  return {
    // 状态
    data: state.data,
    loading: state.loading,
    submitting: state.submitting,
    errors: state.errors,
    touched: state.touched,
    isValid: state.isValid,

    // 操作方法
    setField,
    setFields,
    validateForm,
    resetForm,
    submitForm,

    // 工具方法
    getFieldError,
    hasFieldError,
    getFormProgress,
    getHistory,
    loadFromHistory,

    // 状态控制
    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', loading }),
    setSubmitting: (submitting: boolean) => dispatch({ type: 'SET_SUBMITTING', submitting })
  };
}
