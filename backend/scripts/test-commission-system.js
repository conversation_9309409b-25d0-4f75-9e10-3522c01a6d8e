/**
 * 佣金系统综合测试脚本
 * 
 * 功能：
 * 1. 生成测试订单和佣金数据
 * 2. 测试佣金计算是否正确
 * 3. 测试佣金提现功能
 * 4. 验证数据一致性
 * 
 * 使用方法：
 * node scripts/test-commission-system.js
 */

import '../src/config/dotenv.js';
import sequelize from '../src/config/database.js';
import User from '../src/models/User.js';
import Product from '../src/models/Product.js';
import Order from '../src/models/Order.js';
import Commission from '../src/models/Commission.js';
import Withdrawal from '../src/models/Withdrawal.js';
import { generateOrderNumber } from '../src/utils/orderUtils.js';
import SystemConfig from '../src/models/SystemConfig.js';

// 颜色输出函数
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}[信息]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[成功]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[警告]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[错误]${colors.reset} ${msg}`),
  result: (msg) => console.log(`${colors.magenta}[结果]${colors.reset} ${msg}`),
  test: (msg) => console.log(`${colors.cyan}[测试]${colors.reset} ${msg}`)
};

// 随机工具函数
const random = {
  // 生成订单号
  orderId: () => generateOrderNumber(),
  
  // 生成随机金额
  amount: (min = 100, max = 1000) => parseFloat((Math.random() * (max - min) + min).toFixed(2)),
  
  // 生成随机日期
  date: (daysAgo = 30) => {
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * daysAgo));
    return date;
  },
  
  // 生成随机手机号
  phone: () => `1${Math.floor(Math.random() * 9) + 1}${Array(9).fill(0).map(() => Math.floor(Math.random() * 10)).join('')}`,
  
  // 生成随机客户名
  name: () => {
    const surnames = ['张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴'];
    const names = ['明', '华', '强', '伟', '芳', '娜', '静', '秀', '丽', '军', '杰', '涛', '敏', '燕'];
    return surnames[Math.floor(Math.random() * surnames.length)] + names[Math.floor(Math.random() * names.length)];
  },
  
  // 从数组中随机选择一项
  pick: (array) => array[Math.floor(Math.random() * array.length)]
};

// 清理测试数据
async function cleanupTestData(agentIds) {
  log.info('清理之前的测试数据...');
  
  // 删除这些代理的提现记录
  await Withdrawal.destroy({
    where: {
      agentId: agentIds
    }
  });
  
  // 删除这些代理的佣金记录
  await Commission.destroy({
    where: {
      agentId: agentIds
    }
  });
  
  // 删除这些代理的订单
  await Order.destroy({
    where: {
      agentId: agentIds
    }
  });
  
  log.success('测试数据清理完成');
}

// 生成订单和佣金数据
async function generateOrdersAndCommissions(agents, products) {
  const ordersByAgent = {};
  
  for (const agent of agents) {
    log.info(`为代理 ${agent.username} (ID: ${agent.id}) 生成订单和佣金...`);
    ordersByAgent[agent.id] = [];
    
    // 为每个代理生成5-10个订单
    const orderCount = Math.floor(Math.random() * 6) + 5;
    
    for (let i = 0; i < orderCount; i++) {
      // 随机选择一个产品
      const product = random.pick(products);
      const orderAmount = random.amount();
      
      // 计算佣金
      const agentCommissionRate = agent.commissionRate / 100; // 转换为小数
      const agentCommission = parseFloat((orderAmount * agentCommissionRate).toFixed(2));
      
      let parentAgentCommission = 0;
      let parentAgentId = null;
      
      if (agent.parentAgentId) {
        const parentAgent = await User.findByPk(agent.parentAgentId);
        if (parentAgent) {
          parentAgentId = parentAgent.id;
          // 上级代理获得的是差额佣金
          const parentCommissionRate = (parentAgent.commissionRate - agent.commissionRate) / 100;
          if (parentCommissionRate > 0) {
            parentAgentCommission = parseFloat((orderAmount * parentCommissionRate).toFixed(2));
          }
        }
      }
      
      // 创建订单
      const order = await Order.create({
        id: random.orderId(),
        amount: orderAmount,
        status: 'paid', // 已支付状态
        customerName: random.name(),
        customerPhone: random.phone(),
        productId: product.id,
        agentId: agent.id,
        parentAgentId: parentAgentId,
        agentCommission: agentCommission,
        parentAgentCommission: parentAgentCommission,
        paidAt: random.date()
      });
      
      log.success(`创建订单: ${order.id}, 金额: ¥${orderAmount}, 代理佣金: ¥${agentCommission}`);
      ordersByAgent[agent.id].push(order);
      
      // 创建代理佣金记录
      const commission = await Commission.create({
        amount: agentCommission,
        status: Math.random() > 0.3 ? 'available' : 'pending', // 70%为可提现状态
        orderId: order.id,
        agentId: agent.id
      });
      
      log.success(`创建佣金记录: ID ${commission.id}, 金额: ¥${agentCommission}, 状态: ${commission.status}`);
      
      // 如果有上级代理，也创建上级代理的佣金记录
      if (parentAgentId && parentAgentCommission > 0) {
        const parentCommission = await Commission.create({
          amount: parentAgentCommission,
          status: Math.random() > 0.3 ? 'available' : 'pending',
          orderId: order.id,
          agentId: parentAgentId
        });
        
        log.success(`创建上级代理佣金记录: ID ${parentCommission.id}, 金额: ¥${parentAgentCommission}, 状态: ${parentCommission.status}`);
      }
    }
  }
  
  return ordersByAgent;
}

// 验证佣金计算是否正确
async function verifyCommissionCalculation(agents, ordersByAgent) {
  log.test('验证佣金计算是否正确...');
  
  for (const agent of agents) {
    const orders = ordersByAgent[agent.id] || [];
    
    for (const order of orders) {
      // 重新计算佣金
      const expectedCommission = parseFloat((order.amount * (agent.commissionRate / 100)).toFixed(2));
      
      // 验证订单中记录的佣金是否正确
      if (Math.abs(order.agentCommission - expectedCommission) > 0.01) {
        log.error(`订单 ${order.id} 佣金计算错误: 期望 ¥${expectedCommission}, 实际 ¥${order.agentCommission}`);
      } else {
        log.success(`订单 ${order.id} 佣金计算正确: ¥${order.agentCommission}`);
      }
      
      // 验证佣金记录是否存在
      const commissions = await Commission.findAll({
        where: {
          orderId: order.id,
          agentId: agent.id
        }
      });
      
      if (commissions.length === 0) {
        log.error(`订单 ${order.id} 没有对应的佣金记录`);
      } else if (commissions.length > 1) {
        log.warning(`订单 ${order.id} 有多个佣金记录: ${commissions.length}个`);
      } else {
        const commission = commissions[0];
        if (Math.abs(commission.amount - expectedCommission) > 0.01) {
          log.error(`佣金记录 ${commission.id} 金额错误: 期望 ¥${expectedCommission}, 实际 ¥${commission.amount}`);
        } else {
          log.success(`佣金记录 ${commission.id} 金额正确: ¥${commission.amount}`);
        }
      }
    }
  }
}

// 测试佣金提现功能
async function testWithdrawal(agent) {
  log.test(`测试代理 ${agent.username} 的佣金提现功能...`);
  
  // 获取可提现佣金总额
  const availableCommissions = await Commission.findAll({
    where: {
      agentId: agent.id,
      status: 'available'
    }
  });
  
  const totalAvailable = availableCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
  log.info(`代理 ${agent.username} 可提现佣金总额: ¥${totalAvailable.toFixed(2)}`);
  
  if (totalAvailable <= 0) {
    log.warning(`代理 ${agent.username} 没有可提现佣金，跳过提现测试`);
    return null;
  }
  
  // 测试1: 提现金额超过可提现金额
  try {
    log.test('测试1: 提现金额超过可提现金额');
    const excessAmount = totalAvailable + 100;
    
    const withdrawal1 = await Withdrawal.create({
      amount: excessAmount,
      alipayAccount: `${agent.username}@alipay.com`,
      agentId: agent.id,
      status: 'pending'
    });
    
    // 更新佣金状态
    await updateCommissionStatus(agent.id, withdrawal1.id, excessAmount);
    
    log.error('测试1失败: 系统允许提现金额超过可提现金额');
  } catch (error) {
    log.success('测试1通过: 系统正确拒绝了超额提现');
  }
  
  // 测试2: 提现小额佣金
  try {
    log.test('测试2: 提现小额佣金 (¥0.01)');
    
    const withdrawal2 = await Withdrawal.create({
      amount: 0.01,
      alipayAccount: `${agent.username}@alipay.com`,
      agentId: agent.id,
      status: 'pending'
    });
    
    // 更新佣金状态
    await updateCommissionStatus(agent.id, withdrawal2.id, 0.01);
    
    log.success('测试2通过: 系统允许提现小额佣金');
    
    // 清理测试2创建的提现记录
    await Withdrawal.destroy({ where: { id: withdrawal2.id } });
  } catch (error) {
    log.error(`测试2失败: ${error.message}`);
  }
  
  // 测试3: 正常提现
  try {
    log.test('测试3: 正常提现流程');
    
    // 提现一半的可用佣金
    const withdrawAmount = parseFloat((totalAvailable / 2).toFixed(2));
    
    const withdrawal3 = await Withdrawal.create({
      amount: withdrawAmount,
      alipayAccount: `${agent.username}@alipay.com`,
      agentId: agent.id,
      status: 'pending'
    });
    
    // 更新佣金状态
    await updateCommissionStatus(agent.id, withdrawal3.id, withdrawAmount);
    
    log.success(`测试3通过: 成功创建提现记录，金额: ¥${withdrawAmount}`);
    
    // 验证佣金状态变更
    const withdrawnCommissions = await Commission.findAll({
      where: {
        agentId: agent.id,
        status: 'withdrawn',
        withdrawalId: withdrawal3.id
      }
    });
    
    const totalWithdrawn = withdrawnCommissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
    
    if (Math.abs(totalWithdrawn - withdrawAmount) > 0.01) {
      log.error(`佣金状态更新错误: 期望 ¥${withdrawAmount}, 实际 ¥${totalWithdrawn}`);
    } else {
      log.success(`佣金状态更新正确: ¥${totalWithdrawn}`);
    }
    
    // 测试审核提现
    log.test('测试3.1: 审核提现');
    await withdrawal3.update({
      status: 'approved',
      remarks: '测试审核通过',
      approvedAt: new Date()
    });
    
    log.success('提现审核通过');
    
    // 测试完成提现
    log.test('测试3.2: 完成提现');
    await withdrawal3.update({
      status: 'completed',
      completedAt: new Date()
    });
    
    log.success('提现完成');
    
    return withdrawal3;
  } catch (error) {
    log.error(`测试3失败: ${error.message}`);
    return null;
  }
}

// 更新佣金状态
async function updateCommissionStatus(agentId, withdrawalId, amount) {
  // 获取可提现佣金
  const availableCommissions = await Commission.findAll({
    where: {
      agentId: agentId,
      status: 'available'
    },
    order: [['createdAt', 'ASC']] // 先进先出
  });
  
  let remainingAmount = amount;
  
  for (const commission of availableCommissions) {
    if (remainingAmount <= 0) break;
    
    const commissionAmount = Number(commission.amount);
    
    if (commissionAmount <= remainingAmount) {
      // 如果佣金金额小于等于剩余所需金额，全部提现
      await commission.update({
        status: 'withdrawn',
        withdrawalId: withdrawalId
      });
      remainingAmount -= commissionAmount;
    } else {
      // 如果佣金金额大于剩余所需金额，需要拆分
      await commission.update({
        amount: commissionAmount - remainingAmount
      });
      
      // 创建新的已提现佣金记录
      await Commission.create({
        amount: remainingAmount,
        status: 'withdrawn',
        orderId: commission.orderId,
        agentId: commission.agentId,
        withdrawalId: withdrawalId
      });
      
      remainingAmount = 0;
    }
  }
  
  if (remainingAmount > 0) {
    throw new Error(`可提现金额不足，还需 ¥${remainingAmount}`);
  }
}

// 验证提现后的数据一致性
async function verifyDataConsistency(agent, withdrawal) {
  if (!withdrawal) return;
  
  log.test(`验证代理 ${agent.username} 提现后的数据一致性...`);
  
  // 获取所有佣金记录
  const allCommissions = await Commission.findAll({
    where: { agentId: agent.id }
  });
  
  // 按状态分类
  const commissionsByStatus = {
    pending: allCommissions.filter(c => c.status === 'pending'),
    available: allCommissions.filter(c => c.status === 'available'),
    withdrawn: allCommissions.filter(c => c.status === 'withdrawn')
  };
  
  // 计算各状态佣金总额
  const totalByStatus = {
    pending: commissionsByStatus.pending.reduce((sum, c) => sum + Number(c.amount), 0),
    available: commissionsByStatus.available.reduce((sum, c) => sum + Number(c.amount), 0),
    withdrawn: commissionsByStatus.withdrawn.reduce((sum, c) => sum + Number(c.amount), 0)
  };
  
  // 获取该代理的所有提现记录
  const withdrawals = await Withdrawal.findAll({
    where: { agentId: agent.id }
  });
  
  // 计算提现总额
  const totalWithdrawn = withdrawals.reduce((sum, w) => sum + Number(w.amount), 0);
  
  // 验证提现总额与已提现佣金总额是否一致
  if (Math.abs(totalWithdrawn - totalByStatus.withdrawn) > 0.01) {
    log.error(`数据不一致: 提现总额 ¥${totalWithdrawn} 与已提现佣金总额 ¥${totalByStatus.withdrawn} 不匹配`);
  } else {
    log.success(`数据一致: 提现总额 ¥${totalWithdrawn} 与已提现佣金总额 ¥${totalByStatus.withdrawn} 匹配`);
  }
  
  // 验证特定提现记录关联的佣金总额是否等于提现金额
  const withdrawnCommissions = await Commission.findAll({
    where: {
      withdrawalId: withdrawal.id
    }
  });
  
  const withdrawalCommissionTotal = withdrawnCommissions.reduce((sum, c) => sum + Number(c.amount), 0);
  
  if (Math.abs(withdrawalCommissionTotal - withdrawal.amount) > 0.01) {
    log.error(`提现记录 ${withdrawal.id} 数据不一致: 提现金额 ¥${withdrawal.amount} 与关联佣金总额 ¥${withdrawalCommissionTotal} 不匹配`);
  } else {
    log.success(`提现记录 ${withdrawal.id} 数据一致: 提现金额 ¥${withdrawal.amount} 与关联佣金总额 ¥${withdrawalCommissionTotal} 匹配`);
  }
  
  // 输出最终统计
  log.result(`\n代理 ${agent.username} 佣金统计:`);
  log.result(`- 总佣金: ¥${(totalByStatus.pending + totalByStatus.available + totalByStatus.withdrawn).toFixed(2)}`);
  log.result(`- 待结算佣金: ¥${totalByStatus.pending.toFixed(2)}`);
  log.result(`- 可提现佣金: ¥${totalByStatus.available.toFixed(2)}`);
  log.result(`- 已提现佣金: ¥${totalByStatus.withdrawn.toFixed(2)}`);
  log.result(`- 提现记录数: ${withdrawals.length}`);
}

// 主函数
async function main() {
  try {
    console.log('\n==================== 佣金系统综合测试 ====================\n');
    
    // 1. 查找测试用户
    log.info('查找测试用户...');
    const agent1 = await User.findOne({ where: { username: 'agent1' } });
    const agent2 = await User.findOne({ where: { username: 'agent2' } });
    
    if (!agent1 || !agent2) {
      log.error('找不到agent1或agent2用户，请先创建这些用户');
      
      // 如果找不到用户，创建测试用户
      if (!agent1) {
        log.info('创建测试用户 agent1...');
        const newAgent1 = await User.create({
          username: 'agent1',
          password: 'password123',
          name: '测试代理1',
          phone: '13800138001',
          role: 'user',
          level: 1,
          status: 'approved',
          commissionRate: 80.00
        });
        log.success(`创建成功: ${newAgent1.username} (ID: ${newAgent1.id})`);
      }
      
      if (!agent2) {
        log.info('创建测试用户 agent2...');
        const newAgent2 = await User.create({
          username: 'agent2',
          password: 'password123',
          name: '测试代理2',
          phone: '13800138002',
          role: 'user',
          level: 1,
          status: 'approved',
          commissionRate: 70.00
        });
        log.success(`创建成功: ${newAgent2.username} (ID: ${newAgent2.id})`);
      }
      
      // 重新获取用户
      const updatedAgent1 = await User.findOne({ where: { username: 'agent1' } });
      const updatedAgent2 = await User.findOne({ where: { username: 'agent2' } });
      
      if (!updatedAgent1 || !updatedAgent2) {
        log.error('创建用户后仍然找不到测试用户，测试终止');
        return;
      }
    }
    
    // 重新获取用户确保数据最新
    const testAgents = [
      await User.findOne({ where: { username: 'agent1' } }),
      await User.findOne({ where: { username: 'agent2' } })
    ];
    
    log.success(`找到测试用户: ${testAgents.map(a => a.username).join(', ')}`);
    
    // 2. 查找可用的产品
    log.info('查找可用产品...');
    const products = await Product.findAll({ where: { status: 'active' } });
    
    if (products.length === 0) {
      log.warning('找不到可用的产品，创建测试产品...');
      
      // 创建测试产品
      const testProducts = [
        await Product.create({
          title: '测试产品1',
          description: '这是一个用于测试的产品',
          price: 199.99,
          baseCommissionRate: 0.8,
          status: 'active'
        }),
        await Product.create({
          title: '测试产品2',
          description: '这是另一个用于测试的产品',
          price: 299.99,
          baseCommissionRate: 0.7,
          status: 'active'
        })
      ];
      
      log.success(`创建了 ${testProducts.length} 个测试产品`);
      
      // 重新获取产品
      const updatedProducts = await Product.findAll({ where: { status: 'active' } });
      if (updatedProducts.length === 0) {
        log.error('创建产品后仍然找不到可用产品，测试终止');
        return;
      }
    }
    
    // 重新获取产品确保数据最新
    const testProducts = await Product.findAll({ where: { status: 'active' } });
    log.success(`找到 ${testProducts.length} 个可用产品`);
    
    // 3. 清理之前的测试数据
    await cleanupTestData(testAgents.map(agent => agent.id));
    
    // 4. 生成订单和佣金数据
    const ordersByAgent = await generateOrdersAndCommissions(testAgents, testProducts);
    
    // 5. 验证佣金计算是否正确
    await verifyCommissionCalculation(testAgents, ordersByAgent);
    
    // 6. 测试佣金提现功能
    const withdrawalResults = [];
    for (const agent of testAgents) {
      const withdrawal = await testWithdrawal(agent);
      if (withdrawal) {
        withdrawalResults.push({ agent, withdrawal });
      }
    }
    
    // 7. 验证提现后的数据一致性
    for (const { agent, withdrawal } of withdrawalResults) {
      await verifyDataConsistency(agent, withdrawal);
    }
    
    console.log('\n==================== 测试完成 ====================\n');
    
  } catch (error) {
    log.error(`测试过程中出错: ${error.message}`);
    console.error(error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行主函数
main();
