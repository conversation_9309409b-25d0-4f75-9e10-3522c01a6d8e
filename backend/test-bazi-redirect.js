/**
 * 测试八字产品支付跳转逻辑
 * 验证修复后的环境变量和产品类型配置是否正确
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '.env') });
dotenv.config({ path: path.join(__dirname, '.env.production') });

console.log('=== 八字产品支付跳转逻辑测试 ===\n');

// 1. 检查环境变量配置
console.log('1. 环境变量检查:');
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL || '未设置'}`);
console.log(`PAID_RETURN_URL: ${process.env.PAID_RETURN_URL || '未设置'}`);
console.log(`PAID_RETURN_URL2: ${process.env.PAID_RETURN_URL2 || '未设置'}`);
console.log();

// 2. 模拟八字产品的跳转逻辑
function simulateBaziRedirect(productTitle, productType, extendParams) {
    console.log(`2. 模拟产品跳转逻辑:`);
    console.log(`产品标题: ${productTitle}`);
    console.log(`产品类型: ${productType}`);
    console.log(`扩展参数: ${extendParams}`);
    
    let redirectUrl;
    
    // 模拟 payment.js 中的跳转逻辑
    if (productType === 'service' && (productTitle.includes('八字') || productTitle.includes('命理'))) {
        // 八字服务跳转到报告页面
        redirectUrl = `${process.env.FRONTEND_URL}/bazi-report?orderId=test123`;
        console.log(`✅ 八字产品 - 跳转到报告页面`);
    } else if (extendParams && extendParams.includes('deepseek')) {
        // Deepseek AI课程跳转
        redirectUrl = process.env.PAID_RETURN_URL2;
        console.log(`📚 Deepseek课程 - 跳转到课程页面`);
    } else {
        // 其他产品跳转到默认页面
        redirectUrl = process.env.PAID_RETURN_URL;
        console.log(`📦 其他产品 - 跳转到默认页面`);
    }
    
    console.log(`跳转URL: ${redirectUrl}`);
    console.log();
    
    return redirectUrl;
}

// 3. 测试不同产品的跳转
console.log('3. 测试各种产品跳转:');
console.log();

// 测试八字产品
simulateBaziRedirect('玄易八字命盘', 'service', null);
simulateBaziRedirect('专业八字命理查询服务', 'service', null);

// 测试其他产品
simulateBaziRedirect('儿童纪录片', 'physical', null);
simulateBaziRedirect('Deepseek AI课程', 'service', 'deepseek_course');

// 4. 验证修复状态
console.log('4. 修复状态验证:');
const frontendUrlSet = !!process.env.FRONTEND_URL;
const correctFrontendUrl = process.env.FRONTEND_URL === 'https://ye.bzcy.xyz';

console.log(`✅ FRONTEND_URL 已设置: ${frontendUrlSet}`);
console.log(`✅ FRONTEND_URL 正确: ${correctFrontendUrl}`);

if (frontendUrlSet && correctFrontendUrl) {
    console.log('\n🎉 八字产品支付跳转问题已修复！');
    console.log('八字产品现在会正确跳转到: https://ye.bzcy.xyz/bazi-report?orderId=xxx');
} else {
    console.log('\n❌ 还存在配置问题，需要进一步检查');
}

console.log('\n=== 测试完成 ===');