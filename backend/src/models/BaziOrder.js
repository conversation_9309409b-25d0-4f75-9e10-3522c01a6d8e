import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const BaziOrder = sequelize.define('BaziOrder', {
  orderId: {
    type: DataTypes.STRING(20),
    allowNull: false,
    references: {
      model: 'Orders',
      key: 'id'
    },
    comment: 'Associated order ID'
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Customer name'
  },
  gender: {
    type: DataTypes.ENUM('male', 'female'),
    allowNull: false,
    comment: 'Customer gender'
  },
  calendarType: {
    type: DataTypes.ENUM('gregorian', 'lunar'),
    allowNull: false,
    defaultValue: 'gregorian',
    comment: 'Calendar type: gregorian or lunar'
  },
  birthYear: {
    type: DataTypes.STRING(4),
    allowNull: false,
    comment: 'Birth year'
  },
  birthMonth: {
    type: DataTypes.STRING(2),
    allowNull: false,
    comment: 'Birth month'
  },
  birthDay: {
    type: DataTypes.STRING(2),
    allowNull: false,
    comment: 'Birth day'
  },
  birthHour: {
    type: DataTypes.STRING(2),
    allowNull: false,
    comment: 'Birth hour'
  },
  birthMinute: {
    type: DataTypes.STRING(2),
    allowNull: false,
    comment: 'Birth minute'
  },
  birthProvince: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Birth province'
  },
  birthCity: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Birth city'
  },
  reportData: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Generated bazi report data in JSON format'
  },
  reportStatus: {
    type: DataTypes.ENUM('pending', 'generated', 'failed'),
    defaultValue: 'pending',
    comment: 'Report generation status'
  },
  apiRequestParams: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'API request parameters for debugging'
  },
  apiResponse: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Full API response for debugging'
  },
  errorMessage: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Error message if report generation failed'
  }
}, {
  tableName: 'bazi_orders',
  timestamps: true,
  indexes: [
    {
      fields: ['orderId'],
      unique: true
    },
    {
      fields: ['reportStatus']
    }
  ]
});

export default BaziOrder; 