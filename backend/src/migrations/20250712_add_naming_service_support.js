export const up = async (queryInterface, Sequelize) => {
  // 1. 添加价格范围字段到产品表
  const tableDescription = await queryInterface.describeTable('products');
  
  if (!tableDescription.minPrice) {
    await queryInterface.addColumn('products', 'minPrice', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Minimum price for variable pricing products like naming service'
    });
  }
  
  if (!tableDescription.maxPrice) {
    await queryInterface.addColumn('products', 'maxPrice', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
      comment: 'Maximum price for variable pricing products like naming service'
    });
  }

  // 2. 更新产品类型枚举以包含'naming'
  // 由于MySQL的ENUM修改比较复杂，我们先添加一个临时列
  await queryInterface.addColumn('products', 'type_new', {
    type: Sequelize.ENUM('physical', 'service', 'naming'),
    allowNull: false,
    defaultValue: 'physical',
    comment: 'Product type: physical for goods, service for services like bazi query, naming for naming service'
  });

  // 3. 复制现有数据到新列
  await queryInterface.sequelize.query(`
    UPDATE products SET type_new = type
  `);

  // 4. 删除旧列
  await queryInterface.removeColumn('products', 'type');

  // 5. 重命名新列
  await queryInterface.renameColumn('products', 'type_new', 'type');
};

export const down = async (queryInterface, Sequelize) => {
  // 回滚操作：删除新添加的字段和恢复原始枚举
  await queryInterface.removeColumn('products', 'minPrice');
  await queryInterface.removeColumn('products', 'maxPrice');
  
  // 恢复原始的type枚举
  await queryInterface.addColumn('products', 'type_new', {
    type: Sequelize.ENUM('physical', 'service'),
    allowNull: false,
    defaultValue: 'physical'
  });

  await queryInterface.sequelize.query(`
    UPDATE products SET type_new = CASE 
      WHEN type = 'naming' THEN 'service'
      ELSE type
    END
  `);

  await queryInterface.removeColumn('products', 'type');
  await queryInterface.renameColumn('products', 'type_new', 'type');
};
