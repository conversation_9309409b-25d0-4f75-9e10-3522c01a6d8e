// 错误边界组件 - 产品级错误处理

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Result, Button, Typography, Card, Space, Alert } from 'antd';
import { ExclamationCircleOutlined, ReloadOutlined, HomeOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新 state 使下一次渲染能够显示降级后的 UI
    return {
      hasError: true,
      error,
      errorId: Date.now().toString()
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo
    });

    // 调用错误回调
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 发送错误报告到监控服务
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // 这里可以集成错误监控服务，如 Sentry
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId') || 'anonymous'
      };

      console.error('Error Report:', errorReport);
      
      // 发送到错误监控服务
      // fetch('/api/error-report', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义降级UI，使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认错误UI
      return (
        <div style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          padding: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Card
            style={{
              maxWidth: '500px',
              width: '100%',
              textAlign: 'center',
              borderRadius: '12px',
              boxShadow: '0 8px 32px rgba(0,0,0,0.1)'
            }}
          >
            <Result
              status="error"
              icon={<ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
              title="页面出现了问题"
              subTitle="抱歉，页面遇到了意外错误。我们已经记录了这个问题，请稍后重试。"
              extra={
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                  <Space wrap>
                    <Button 
                      type="primary" 
                      icon={<ReloadOutlined />}
                      onClick={this.handleReload}
                    >
                      刷新页面
                    </Button>
                    <Button 
                      icon={<HomeOutlined />}
                      onClick={this.handleGoHome}
                    >
                      返回首页
                    </Button>
                    <Button 
                      type="dashed"
                      onClick={this.handleReset}
                    >
                      重试
                    </Button>
                  </Space>

                  {/* 错误详情（开发环境显示） */}
                  {process.env.NODE_ENV === 'development' && this.state.error && (
                    <Alert
                      message="错误详情（开发模式）"
                      description={
                        <div style={{ textAlign: 'left' }}>
                          <Paragraph>
                            <Text strong>错误信息:</Text>
                            <br />
                            <Text code>{this.state.error.message}</Text>
                          </Paragraph>
                          
                          <Paragraph>
                            <Text strong>错误ID:</Text>
                            <br />
                            <Text code>{this.state.errorId}</Text>
                          </Paragraph>

                          {this.state.error.stack && (
                            <details>
                              <summary style={{ cursor: 'pointer', marginBottom: '8px' }}>
                                <Text strong>堆栈信息</Text>
                              </summary>
                              <pre style={{
                                background: '#f5f5f5',
                                padding: '8px',
                                borderRadius: '4px',
                                fontSize: '12px',
                                overflow: 'auto',
                                maxHeight: '200px'
                              }}>
                                {this.state.error.stack}
                              </pre>
                            </details>
                          )}
                        </div>
                      }
                      type="warning"
                      showIcon
                      style={{ marginTop: '16px' }}
                    />
                  )}

                  {/* 用户反馈 */}
                  <div style={{
                    background: '#f6f6f6',
                    padding: '12px',
                    borderRadius: '6px',
                    marginTop: '16px'
                  }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      如果问题持续存在，请联系客服：
                      <br />
                      微信：bazi888 | 邮箱：<EMAIL>
                      <br />
                      错误ID：{this.state.errorId}
                    </Text>
                  </div>
                </Space>
              }
            />
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
