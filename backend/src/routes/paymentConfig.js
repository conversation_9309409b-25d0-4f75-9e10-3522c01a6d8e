import express from 'express';
import { PaymentConfig, User, SystemConfig } from '../models/index.js';
import { auth, adminAuth } from '../middleware/auth.js';
import sequelize from '../config/database.js';

const router = express.Router();

/**
 * 获取系统支付配置信息
 * @route GET /api/payment-config/system
 * @access 仅管理员
 */
router.get('/system', auth, adminAuth, async (req, res) => {
  try {
    // 从环境变量获取默认值
    const defaultConfig = {
      alipayAppId: process.env.ALIPAY_APPID || '',
      alipayPrivateKey: process.env.ALIPAY_PRIVATE_KEY || '',
      alipayPublicKey: process.env.ALIPAY_PUBLIC_KEY || '',
      alipayNotifyUrl: process.env.ALIPAY_NOTIFY_URL || '',
      alipayReturnUrl: process.env.ALIPAY_RETURN_URL || ''
    };
    
    // 尝试从数据库获取系统支付配置
    try {
      // 获取系统支付配置
      const systemConfig = {
        alipayAppId: await SystemConfig.getValue('ALIPAY_APPID', defaultConfig.alipayAppId),
        alipayPrivateKey: await SystemConfig.getValue('ALIPAY_PRIVATE_KEY', defaultConfig.alipayPrivateKey),
        alipayPublicKey: await SystemConfig.getValue('ALIPAY_PUBLIC_KEY', defaultConfig.alipayPublicKey),
        alipayNotifyUrl: await SystemConfig.getValue('ALIPAY_NOTIFY_URL', defaultConfig.alipayNotifyUrl),
        alipayReturnUrl: await SystemConfig.getValue('ALIPAY_RETURN_URL', defaultConfig.alipayReturnUrl)
      };
      
      res.json({ success: true, data: systemConfig });
    } catch (dbError) {
      // 如果数据库错误，使用环境变量中的默认值
      console.error('从数据库获取配置失败，使用环境变量默认值:', dbError);
      res.json({ 
        success: true, 
        data: defaultConfig,
        message: 'SystemConfigs表不存在，使用环境变量默认值' 
      });
    }
  } catch (error) {
    console.error('获取系统支付配置失败:', error);
    res.status(500).json({ success: false, message: '获取系统支付配置失败', error: error.message });
  }
});

/**
 * 更新系统支付配置
 * @route PUT /api/payment-config/system
 * @access 仅管理员
 */
router.put('/system', auth, adminAuth, async (req, res) => {
  const { alipayAppId, alipayPrivateKey, alipayPublicKey, alipayNotifyUrl, alipayReturnUrl } = req.body;
  
  try {
    // 更新环境变量
    process.env.ALIPAY_APPID = alipayAppId;
    process.env.ALIPAY_PRIVATE_KEY = alipayPrivateKey;
    process.env.ALIPAY_PUBLIC_KEY = alipayPublicKey;
    process.env.ALIPAY_NOTIFY_URL = alipayNotifyUrl;
    process.env.ALIPAY_RETURN_URL = alipayReturnUrl;
    
    // 尝试更新数据库中的值
    try {
      const transaction = await sequelize.transaction();
      
      // 更新系统配置值
      await SystemConfig.setValue('ALIPAY_APPID', alipayAppId, '支付宝应用ID', 'payment', transaction);
      await SystemConfig.setValue('ALIPAY_PRIVATE_KEY', alipayPrivateKey, '支付宝应用私钥', 'payment', transaction);
      await SystemConfig.setValue('ALIPAY_PUBLIC_KEY', alipayPublicKey, '支付宝公钥', 'payment', transaction);
      await SystemConfig.setValue('ALIPAY_NOTIFY_URL', alipayNotifyUrl, '支付宝异步回调地址', 'payment', transaction);
      await SystemConfig.setValue('ALIPAY_RETURN_URL', alipayReturnUrl, '支付宝同步回调地址', 'payment', transaction);
      
      await transaction.commit();
    } catch (dbError) {
      console.error('更新数据库配置失败，仅更新了环境变量:', dbError);
      // 即使数据库操作失败，我们依然返回成功，因为环境变量已更新
    }
    
    res.json({ success: true, message: '系统支付配置已更新' });
  } catch (error) {
    console.error('更新系统支付配置失败:', error);
    res.status(500).json({ success: false, message: '更新系统支付配置失败', error: error.message });
  }
});

/**
 * 获取所有代理的支付配置
 * @route GET /api/payment-config/agents
 * @access 仅管理员
 */

/**
 * 获取代理列表(供选择配置支付)
 * @route GET /api/payment-config/agent-list
 * @access 仅管理员
 */

/**
 * 获取单个代理的支付配置
 * @route GET /api/payment-config/agent/:userId
 * @access 仅管理员
 */
router.get('/agent/:userId', auth, adminAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    
    const config = await PaymentConfig.findOne({ 
      where: { userId },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'username', 'name']
      }]
    });
    
    if (!config) {
      return res.status(404).json({ success: false, message: '未找到该代理的支付配置' });
    }
    
    res.json({ success: true, data: config });
    
  } catch (error) {
    console.error('获取代理支付配置失败:', error);
    res.status(500).json({ success: false, message: '获取代理支付配置失败', error: error.message });
  }
});

/**
 * 启用/禁用代理的支付配置
 * @route PATCH /api/payment-config/agent/:userId/toggle
 * @access 仅管理员
 */
router.patch('/agent/:userId/toggle', auth, adminAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    const { isActive } = req.body;
    
    const config = await PaymentConfig.findOne({ where: { userId } });
    
    if (!config) {
      return res.status(404).json({ success: false, message: '未找到该代理的支付配置' });
    }
    
    await config.update({ isActive });
    
    res.json({ 
      success: true, 
      message: isActive ? '代理支付配置已启用' : '代理支付配置已禁用'
    });
    
  } catch (error) {
    console.error('切换代理支付配置状态失败:', error);
    res.status(500).json({ success: false, message: '切换代理支付配置状态失败', error: error.message });
  }
});

/**
 * 删除代理的支付配置
 * @route DELETE /api/payment-config/agent/:userId
 * @access 仅管理员
 */
router.delete('/agent/:userId', auth, adminAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    
    const config = await PaymentConfig.findOne({ where: { userId } });
    
    if (!config) {
      return res.status(404).json({ success: false, message: '未找到该代理的支付配置' });
    }
    
    await config.destroy();
    
    res.json({ success: true, message: '代理支付配置已删除' });
    
  } catch (error) {
    console.error('删除代理支付配置失败:', error);
    res.status(500).json({ success: false, message: '删除代理支付配置失败', error: error.message });
  }
});

/**
 * 获取代理列表和支付配置(供选择配置支付)
 * @route GET /api/payment-config/agents
 * @access 仅管理员
 */
router.get('/agents', auth, adminAuth, async (req, res) => {
  try {
    // 获取所有代理用户
    const agents = await User.findAll({
      where: {
        role: 'user',
        status: 'approved'
      },
      attributes: ['id', 'username', 'name', 'level', 'parentAgentId'],
      order: [
        ['level', 'ASC'],
        ['name', 'ASC']
      ]
    });
    
    // 获取所有支付配置
    const paymentConfigs = await PaymentConfig.findAll({
      attributes: ['id', 'userId', 'alipayAppId', 'alipayPrivateKey', 'alipayPublicKey', 'isActive', 'description', 'createdAt', 'updatedAt']
    });
    
    console.log('获取到的支付配置:', paymentConfigs.map(c => c.get({ plain: true })));
    
    // 将支付配置信息添加到代理用户中
    const agentsWithConfig = agents.map(agent => {
      const plainAgent = agent.get({ plain: true });
      const config = paymentConfigs.find(c => c.userId === agent.id);
      plainAgent.paymentConfig = config ? config.get({ plain: true }) : null;
      return plainAgent;
    });
    
    console.log('返回的代理列表:', agentsWithConfig);
    
    res.json({ success: true, data: agentsWithConfig });
  } catch (error) {
    console.error('获取代理列表失败:', error);
    res.status(500).json({ success: false, message: '获取代理列表失败', error: error.message });
  }
});

/**
 * 获取代理列表(供选择配置支付)
 * @route GET /api/payment-config/agent-list
 * @access 仅管理员
 */
router.get('/agent-list', auth, adminAuth, async (req, res) => {
  try {
    // 获取所有代理用户
    const agents = await User.findAll({
      where: {
        role: 'user',
        status: 'approved'
      },
      attributes: ['id', 'username', 'name', 'level', 'parentAgentId'],
      order: [
        ['level', 'ASC'],
        ['name', 'ASC']
      ]
    });
    
    // 获取所有支付配置
    const paymentConfigs = await PaymentConfig.findAll({
      attributes: ['id', 'userId', 'alipayAppId', 'alipayPrivateKey', 'alipayPublicKey', 'isActive', 'description', 'createdAt', 'updatedAt']
    });
    
    console.log('获取到的支付配置:', paymentConfigs.map(c => c.get({ plain: true })));
    
    // 将支付配置信息添加到代理用户中
    const agentsWithConfig = agents.map(agent => {
      const plainAgent = agent.get({ plain: true });
      const config = paymentConfigs.find(c => c.userId === agent.id);
      plainAgent.paymentConfig = config ? config.get({ plain: true }) : null;
      return plainAgent;
    });
    
    console.log('返回的代理列表:', agentsWithConfig);
    
    res.json({ success: true, data: agentsWithConfig });
  } catch (error) {
    console.error('获取代理列表失败:', error);
    res.status(500).json({ success: false, message: '获取代理列表失败', error: error.message });
  }
});

/**
 * 创建或更新代理的支付配置
 * @route PUT /api/payment-config/agent/:userId
 * @access 仅管理员
 */
router.put('/agent/:userId', auth, adminAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    const { alipayAppId, alipayPrivateKey, alipayPublicKey, isActive, description } = req.body;
    
    console.log('接收到的请求数据:', req.body);
    
    // 验证必填字段
    if (!alipayAppId || !alipayPrivateKey || !alipayPublicKey) {
      return res.status(400).json({ success: false, message: '支付宝AppID、私钥和公钥不能为空' });
    }
    
    // 检查用户是否存在且是代理
    const agent = await User.findOne({
      where: {
        id: userId,
        role: 'user'
      }
    });
    
    if (!agent) {
      return res.status(404).json({ success: false, message: '指定的用户不存在或不是一级代理' });
    }
    
    // 创建或更新支付配置
    const [config, created] = await PaymentConfig.findOrCreate({
      where: { userId },
      defaults: {
        alipayAppId,
        alipayPrivateKey,
        alipayPublicKey,
        isActive: isActive !== undefined ? isActive : true,
        description: description || `${agent.name} 的支付配置`
      }
    });
    
    if (!created) {
      await config.update({
        alipayAppId,
        alipayPrivateKey,
        alipayPublicKey,
        isActive: isActive !== undefined ? isActive : config.isActive,
        description: description || config.description
      });
    }
    
    // 重新获取更新后的配置
    const updatedConfig = await PaymentConfig.findOne({
      where: { userId },
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'username', 'name']
      }]
    });
    
    console.log('更新后的配置:', updatedConfig.get({ plain: true }));
    
    res.json({ 
      success: true, 
      message: created ? '代理支付配置已创建' : '代理支付配置已更新',
      data: updatedConfig
    });
    
  } catch (error) {
    console.error('更新代理支付配置失败:', error);
    res.status(500).json({ success: false, message: '更新代理支付配置失败', error: error.message });
  }
});

export default router;
