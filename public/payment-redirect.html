<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>u652fu4ed8u5b8cu6210 - u6b63u5728u8df3u8f6c...</title>
  <style>
    body {
      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      background-color: #f8f9fa;
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: #333;
    }
    .container {
      text-align: center;
      background-color: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 90%;
    }
    h1 {
      font-size: 24px;
      margin-bottom: 20px;
      color: #2c3e50;
    }
    p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    .loading {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="loading"></div>
    <h1>u652fu4ed8u6210u529fuff0cu6b63u5728u8df3u8f6c...</h1>
    <p id="message">u6b63u5728u4e3au60a8u51c6u5907u8d44u6e90uff0cu8bf7u7a0du5019...</p>
  </div>

  <script>
    // u83b7u53d6URLu53c2u6570u7684u51fdu6570
    function getQueryParam(param) {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get(param);
    }

    // u5904u7406u8df3u8f6cu903bu8f91
    window.onload = function() {
      const messageElement = document.getElementById('message');
      
      // u83b7u53d6u4ea7u54c1u7c7bu578bu548cu8ba2u5355ID
      const productType = getQueryParam('product_type');
      const orderId = getQueryParam('order_id');
      
      // u6839u636eu4ea7u54c1u7c7bu578bu786eu5b9au8df3u8f6cu5730u5740
      let redirectUrl = '';
      if (productType === 'deepseek') {
        // Deepseek AIu8bfeu7a0bu94feu63a5
        redirectUrl = 'https://pan.quark.cn/s/59c6d1f5973e';
        messageElement.textContent = 'u6b63u5728u4e3au60a8u8df3u8f6cu5230Deepseek AIu8bfeu7a0bu8d44u6e90u9875...';
      } else {
        // u513fu7ae5u7eaau5f55u7247u94feu63a5
        redirectUrl = 'https://pan.baidu.com/s/1l8Q8zYJUtYUC1fKG32FM0A?pwd=qbci';
        messageElement.textContent = 'u6b63u5728u4e3au60a8u8df3u8f6cu5230u513fu7ae5u7eaau5f55u7247u8d44u6e90u9875...';
      }
      
      // u8bb0u5f55u8df3u8f6cu4fe1u606f
      console.log(`u8ba2u5355${orderId}u652fu4ed8u5b8cu6210uff0cu4ea7u54c1u7c7bu578b: ${productType}uff0cu5373u5c06u8df3u8f6cu5230: ${redirectUrl}`);
      
      // u5ef6u8fdf2u79d2u540eu8df3u8f6cuff0cu8ba9u7528u6237u6709u65f6u95f4u770bu5230u72b6u6001
      setTimeout(function() {
        window.location.href = redirectUrl;
      }, 2000);
    };
  </script>
</body>
</html>
