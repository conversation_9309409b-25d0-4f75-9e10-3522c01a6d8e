'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 确保所有订单ID字段的长度和类型一致
    // 1. 先修改Orders表中的id字段
    await queryInterface.changeColumn('Orders', 'id', {
      type: Sequelize.STRING(20),
      allowNull: false,
      primaryKey: true
    });
    
    // 2. 修改Commission表中的orderId字段
    await queryInterface.changeColumn('Commissions', 'orderId', {
      type: Sequelize.STRING(20),
      allowNull: false,
      references: {
        model: 'Orders',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'NO ACTION'
    });
    
    // 3. 修改佣金状态枚举值，确保一致性
    await queryInterface.changeColumn('Commissions', 'status', {
      type: Sequelize.ENUM('pending', 'available', 'withdrawn'),
      allowNull: false,
      defaultValue: 'pending'
    });
    
    // 4. 确保所有金额字段的精度一致（decimal(10,2)）
    const decimalType = Sequelize.DECIMAL(10, 2);
    
    // Orders表中的金额字段
    await queryInterface.changeColumn('Orders', 'amount', {
      type: decimalType,
      allowNull: false
    });
    
    await queryInterface.changeColumn('Orders', 'agentCommission', {
      type: decimalType,
      allowNull: false,
      defaultValue: 0
    });
    
    await queryInterface.changeColumn('Orders', 'parentAgentCommission', {
      type: decimalType,
      allowNull: false,
      defaultValue: 0
    });
    
    // Commissions表中的金额字段
    await queryInterface.changeColumn('Commissions', 'amount', {
      type: decimalType,
      allowNull: false
    });
    
    // Users表中的佣金字段
    await queryInterface.changeColumn('Users', 'totalCommission', {
      type: decimalType,
      allowNull: false,
      defaultValue: 0
    });
    
    await queryInterface.changeColumn('Users', 'monthlyCommission', {
      type: decimalType,
      allowNull: false,
      defaultValue: 0
    });
    
    // Products表中的价格字段
    await queryInterface.changeColumn('Products', 'price', {
      type: decimalType,
      allowNull: false
    });
    
    // Withdrawals表中的金额字段
    await queryInterface.changeColumn('Withdrawals', 'amount', {
      type: decimalType,
      allowNull: false
    });
  },

  async down(queryInterface, Sequelize) {
    // 回滚操作，这里不做具体实现，因为回滚会比较复杂
    console.log('无法回滚字段一致性修复');
  }
};
