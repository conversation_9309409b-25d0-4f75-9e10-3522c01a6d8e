import sequelize from '../config/database.js';
import { User } from '../models/index.js';

// 获取数据看板统计信息
export const getDashboardStats = async (req, res) => {
  try {
    const { id: userId, role } = req.user;
    console.log('开始获取数据看板统计信息:', {
      userId,
      role,
      currentTime: new Date().toISOString()
    });

    // 根据用户角色构建查询条件
    let agentIds = [userId];
    let isParentAgent = false;

    if (role === 'admin') {
      // 管理员可以查看所有用户的数据
      const allUsers = await User.findAll({
        attributes: ['id']
      });
      agentIds = allUsers.map(user => user.id);
    } else {
      // 检查是否是一级代理（有下级代理的用户）
      const subAgents = await User.findAll({
        where: { parentAgentId: userId },
        attributes: ['id']
      });
      
      if (subAgents.length > 0) {
        isParentAgent = true;
        agentIds = [userId, ...subAgents.map(agent => agent.id)];
      }
    }

    console.log('查询条件:', {
      role,
      userId,
      agentIds,
      isParentAgent
    });

    // 获取今日订单统计
    const todayStats = await sequelize.query(`
      SELECT 
        COUNT(id) as totalOrders,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paidOrders,
        COALESCE(
          SUM(CASE 
            WHEN status = 'paid' THEN 
              CASE 
                WHEN :role = 'admin' THEN adminCommission
                WHEN agentId = :userId THEN agentCommission
                WHEN :isParentAgent AND parentAgentId = :userId THEN parentAgentCommission
                ELSE 0 
              END
            ELSE 0 
          END
        ), 0) as totalCommission
      FROM Orders 
      WHERE (agentId IN (:agentIds) OR (:isParentAgent AND parentAgentId = :userId))
      AND createdAt >= CURDATE()
      AND createdAt < DATE_ADD(CURDATE(), INTERVAL 1 DAY)
    `, {
      replacements: { userId, agentIds, role, isParentAgent },
      type: sequelize.QueryTypes.SELECT
    });

    // 获取昨日订单统计
    const yesterdayStats = await sequelize.query(`
      SELECT 
        COUNT(id) as totalOrders,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paidOrders,
        COALESCE(
          SUM(CASE 
            WHEN status = 'paid' THEN 
              CASE 
                WHEN :role = 'admin' THEN adminCommission
                WHEN agentId = :userId THEN agentCommission
                WHEN :isParentAgent AND parentAgentId = :userId THEN parentAgentCommission
                ELSE 0 
              END
            ELSE 0 
          END
        ), 0) as totalCommission
      FROM Orders 
      WHERE (agentId IN (:agentIds) OR (:isParentAgent AND parentAgentId = :userId))
      AND createdAt >= DATE_SUB(CURDATE(), INTERVAL 1 DAY)
      AND createdAt < CURDATE()
    `, {
      replacements: { userId, agentIds, role, isParentAgent },
      type: sequelize.QueryTypes.SELECT
    });

    // 获取所有时间订单统计
    const allTimeStats = await sequelize.query(`
      SELECT 
        COUNT(id) as totalOrders,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paidOrders,
        COALESCE(
          SUM(CASE 
            WHEN status = 'paid' THEN 
              CASE 
                WHEN :role = 'admin' THEN adminCommission
                WHEN agentId = :userId THEN agentCommission
                WHEN :isParentAgent AND parentAgentId = :userId THEN parentAgentCommission
                ELSE 0 
              END
            ELSE 0 
          END
        ), 0) as totalCommission
      FROM Orders 
      WHERE (agentId IN (:agentIds) OR (:isParentAgent AND parentAgentId = :userId))
    `, {
      replacements: { userId, agentIds, role, isParentAgent },
      type: sequelize.QueryTypes.SELECT
    });

    // 计算转化率
    const calculateConversionRate = (stats) => {
      const { totalOrders, paidOrders } = stats[0];
      if (!totalOrders) return 0;
      return Number(((paidOrders / totalOrders) * 100).toFixed(2));
    };

    // 构造返回数据
    const responseData = {
      today: {
        totalOrders: Number(todayStats[0]?.totalOrders || 0),
        paidOrders: Number(todayStats[0]?.paidOrders || 0),
        totalCommission: Number(todayStats[0]?.totalCommission || 0),
        conversionRate: calculateConversionRate(todayStats)
      },
      yesterday: {
        totalOrders: Number(yesterdayStats[0]?.totalOrders || 0),
        paidOrders: Number(yesterdayStats[0]?.paidOrders || 0),
        totalCommission: Number(yesterdayStats[0]?.totalCommission || 0),
        conversionRate: calculateConversionRate(yesterdayStats)
      },
      allTime: {
        totalOrders: Number(allTimeStats[0]?.totalOrders || 0),
        paidOrders: Number(allTimeStats[0]?.paidOrders || 0),
        totalCommission: Number(allTimeStats[0]?.totalCommission || 0),
        conversionRate: calculateConversionRate(allTimeStats)
      }
    };

    console.log('统计数据:', {
      role,
      userId,
      agentIds,
      isParentAgent,
      data: responseData
    });
    res.json(responseData);

  } catch (error) {
    console.error('获取看板数据失败:', {
      message: error.message,
      stack: error.stack,
      sql: error.sql,
      parameters: error.parameters
    });
    res.status(500).json({ message: '获取统计数据失败' });
  }
};