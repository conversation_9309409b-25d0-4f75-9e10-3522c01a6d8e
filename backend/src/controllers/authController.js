import jwt from 'jsonwebtoken';
import { validationResult } from 'express-validator';
import { User } from '../models/index.js';
import { Op } from 'sequelize';

export const register = async (req, res) => {
  try {
    console.log('收到注册请求:', req.body);

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('验证错误:', errors.array());
      return res.status(400).json({ 
        message: '输入验证失败',
        errors: errors.array() 
      });
    }

    const { username, password, name, phone, inviteToken } = req.body;
    
    // 检查必填字段
    if (!username || !password || !name || !phone) {
      return res.status(400).json({ 
        message: '缺少必填字段',
        errors: [
          !username && { param: 'username', msg: '用户名不能为空' },
          !password && { param: 'password', msg: '密码不能为空' },
          !name && { param: 'name', msg: '姓名不能为空' },
          !phone && { param: 'phone', msg: '手机号不能为空' }
        ].filter(Boolean)
      });
    }

    // 检查用户名是否已存在
    const existingUser = await User.findOne({ 
      where: { username } 
    });

    if (existingUser) {
      console.log('用户名已存在:', username);
      return res.status(400).json({ 
        message: '用户名已被使用',
        errors: [{ param: 'username', msg: '用户名已被使用' }]
      });
    }

    // 初始化代理相关变量
    let parentAgentId = null;
    let level = 1;

    // 如果有邀请token，验证并获取邀请人信息
    if (inviteToken) {
      console.log('验证邀请token');
      try {
        const decoded = jwt.verify(inviteToken, process.env.JWT_SECRET || 'your-super-secret-jwt-key-2024');
        console.log('token解码结果:', decoded);

        if (!decoded || decoded.type !== 'invite' || !decoded.inviterId) {
          console.log('无效的邀请token:', decoded);
          return res.status(400).json({ message: '邀请链接无效或已过期' });
        }

        console.log('准备查询邀请人，使用 inviterId:', decoded.inviterId);  // 添加日志

        // 验证邀请人
        const inviter = await User.findOne({
          where: {
            id: decoded.inviterId,  // 使用正确的字段名
            status: 'approved'
          }
        });

        if (!inviter) {
          console.log('邀请人不存在:', decoded.inviterId);
          return res.status(400).json({ message: '邀请人不存在' });
        }

        console.log('查找到的邀请人:', {
          id: inviter.id,
          role: inviter.role,
          level: inviter.level,
          status: inviter.status
        });

        if (inviter.status !== 'approved') {
          return res.status(400).json({ message: '邀请人账号未获得审核通过' });
        }

        if (inviter.role !== 'admin' && (inviter.role !== 'user' || inviter.level !== 1)) {
          return res.status(400).json({ message: '邀请人无权限生成邀请链接' });
        }

        console.log('邀请人验证成功:', {
          id: inviter.id,
          role: inviter.role,
          level: inviter.level,
          status: inviter.status
        });

        // 如果邀请人是一级代理，设置为二级代理
        if (inviter.role === 'user' && inviter.level === 1) {
          parentAgentId = inviter.id;
          level = 2;
          console.log('设置为二级代理:', { parentAgentId, level });
        } else if (inviter.role === 'admin') {
          level = 1;
          console.log('设置为一级代理:', { level });
        }
      } catch (error) {
        console.error('验证邀请token失败:', error);
        return res.status(400).json({ 
          message: '邀请链接无效或已过期',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
      }
    }

    // 创建新用户
    try {
      console.log('开始创建新用户:', { username, name, level, parentAgentId });
      const user = await User.create({
        username,
        password,  // 密码会在 User 模型的 hooks 中自动加密
        name,
        phone,
        parentAgentId,
        level,
        status: 'pending',
        role: 'user',
        commissionRate: level === 2 ? 20 : 80 // 二级代理默认20%，一级代理默认80%
      });

      console.log('用户创建成功:', { id: user.id, username: user.username });
      res.status(201).json({ 
        message: '注册成功，请等待审核',
        user: {
          username: user.username,
          name: user.name,
          status: user.status
        }
      });
    } catch (error) {
      console.error('创建用户失败:', error);
      res.status(500).json({ 
        message: '注册失败，请稍后重试',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  } catch (error) {
    console.error('注册失败:', error);
    // 检查是否是Sequelize验证错误
    if (error.name === 'SequelizeValidationError') {
      return res.status(400).json({
        message: '输入验证失败',
        errors: error.errors.map(err => ({
          param: err.path,
          msg: err.message
        }))
      });
    }
    // 检查是否是Sequelize唯一约束错误
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({
        message: '用户名已被使用',
        errors: [{ param: 'username', msg: '用户名已被使用' }]
      });
    }
    res.status(500).json({ 
      message: '注册失败，请稍后重试',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

export const login = async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('尝试登录:', { username });  // 添加日志

    const user = await User.findOne({ where: { username } });
    console.log('查找用户结果:', user ? '用户存在' : '用户不存在');  // 添加日志
    
    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const isPasswordValid = await user.comparePassword(password);
    console.log('密码验证结果:', isPasswordValid ? '密码正确' : '密码错误');  // 添加日志

    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    if (user.status !== 'approved' && user.role !== 'admin') {
      console.log('用户状态检查:', { status: user.status, role: user.role });  // 添加日志
      return res.status(403).json({ message: 'Account pending approval' });
    }

    const token = jwt.sign(
      { userId: user.id },
      process.env.JWT_SECRET || 'your-super-secret-jwt-key-2024',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );
    // const token = jwt.sign({ 
    //   inviterId: inviterId,
    //   inviterRole: inviterRole,
    //   type: 'invite'
    // }, JWT_SECRET);

    console.log('登录成功，生成token:', { userId: user.id });
    
    res.json({ 
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        level: user.level,
        status: user.status
      }, 
      token 
    });
  } catch (error) {
    console.error('登录失败:', error);  // 添加日志
    res.status(500).json({ message: error.message });
  }
};

export const approveAgent = async (req, res) => {
  try {
    const { agentId, commissionRate } = req.body;
    
    const agent = await User.findOne({ where: { id: agentId, status: 'pending' } });
    if (!agent) {
      return res.status(404).json({ message: 'Agent not found or already processed' });
    }

    // Validate commission rate
    if (agent.level === 1) {
      if (commissionRate < 0 || commissionRate > 100) {
        return res.status(400).json({ message: 'Invalid commission rate' });
      }
    }

    agent.status = 'approved';
    agent.commissionRate = commissionRate;
    agent.approvedAt = new Date();
    agent.approvedBy = req.user.id;
    
    await agent.save();

    res.json({ message: 'Agent approved successfully', agent });
  } catch (error) {
    console.error('审批代理商失败:', error);
    res.status(500).json({ message: error.message });
  }
};

export const setSubAgentCommission = async (req, res) => {
  try {
    const { commissionRate } = req.body;
    // 支持从URL路径参数或请求体中获取代理ID
    const subAgentId = req.params.id || req.body.subAgentId;
    const parentAgent = req.user;

    if (parentAgent.level !== 1) {
      return res.status(403).json({ message: 'Only level-1 agents can set sub-agent commission rates' });
    }

    const subAgent = await User.findOne({ 
      where: { 
        id: subAgentId, 
        parentAgentId: parentAgent.id,
        level: 2
      }
    });

    if (!subAgent) {
      return res.status(404).json({ message: 'Sub-agent not found' });
    }

    // 检查佣金率是否在合理范围内
    if (commissionRate < 0 || commissionRate > 100) {
      return res.status(400).json({ 
        message: '佣金率必须在0-100之间' 
      });
    }
    // 已取消佣金率不能超过上级代理佣金率的限制

    subAgent.commissionRate = commissionRate;
    await subAgent.save();

    res.json({ message: 'Sub-agent commission rate updated', subAgent });
  } catch (error) {
    console.error('设置二级代理商佣金比例失败:', error);
    res.status(500).json({ message: error.message });
  }
};

export const getProfile = async (req, res) => {
  try {
    const [user] = await sequelize.query(
      `SELECT id, username, name, phone, role, level, status, commissionRate, parentAgentId 
       FROM Users 
       WHERE id = ?`,
      {
        replacements: [req.user.id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    if (!user) {
      return res.status(404).json({ message: '用户不存在' });
    }

    // 如果有上级代理，获取上级代理信息
    let parentAgent = null;
    if (user.parentAgentId) {
      const [parent] = await sequelize.query(
        `SELECT id, name, username 
         FROM Users 
         WHERE id = ?`,
        {
          replacements: [user.parentAgentId],
          type: sequelize.QueryTypes.SELECT
        }
      );
      parentAgent = parent;
    }

    // 如果是一级代理，获取下级代理列表
    let subAgents = [];
    if (user.role === 'user' && user.level === 1) {
      subAgents = await sequelize.query(
        `SELECT id, name, username, status, commissionRate 
         FROM Users 
         WHERE parentAgentId = ?`,
        {
          replacements: [user.id],
          type: sequelize.QueryTypes.SELECT
        }
      );
    }

    res.json({
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        phone: user.phone,
        role: user.role,
        level: user.level,
        status: user.status,
        commissionRate: user.commissionRate,
        parentAgent,
        subAgents
      }
    });
  } catch (error) {
    console.error('获取用户资料失败:', error);
    res.status(500).json({ message: '获取用户资料失败', error: error.message });
  }
};