import express from 'express';
import { auth, adminAuth } from '../middleware/auth.js';
import { Order, User, Product, ProductLink, Commission, BaziOrder } from '../models/index.js';
import { createPaymentUrl } from '../services/alipay.js';
import { generateOrderNumber } from '../utils/orderUtils.js';
import { calculateCommissions } from '../utils/commissionUtils.js';
import { Op } from 'sequelize';
import sequelize from '../config/database.js';

const router = express.Router();

// 获取订单列表
router.get('/', auth, async (req, res) => {
  try {
    // 获取分页参数，默认第一页，每页10条
    const page = parseInt(req.query.page) || 1;
    const pageSize = parseInt(req.query.pageSize) || 10;
    const offset = (page - 1) * pageSize;

    console.log('分页参数:', { page, pageSize, offset });

    // 构建查询条件
    let whereClause = {};
    if (req.user.role !== 'admin') {
      whereClause = {
        [Op.or]: [
          { agentId: req.user.id },
          { parentAgentId: req.user.id }
        ]
      };
    }

    // 处理其他筛选条件
    // 1. 状态筛选
    if (req.query.status) {
      whereClause.status = req.query.status;
    }

    // 2. 日期筛选
    if (req.query.startDate && req.query.endDate) {
      try {
        const startDate = new Date(`${req.query.startDate}T00:00:00.000Z`);
        const endDate = new Date(`${req.query.endDate}T23:59:59.999Z`);
        
        // 验证日期是否有效
        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          console.log('有效的日期区间:', { startDate, endDate });
          whereClause.createdAt = {
            [Op.between]: [startDate, endDate]
          };
        } else {
          console.log('无效的日期格式:', { startDate: req.query.startDate, endDate: req.query.endDate });
        }
      } catch (error) {
        console.error('处理日期参数时出错:', error);
      }
    }

    // 获取总记录数和已支付订单数（包括paid和completed状态）
    const [total, totalPaid] = await Promise.all([
      Order.count({ where: whereClause }),
      Order.count({ where: { ...whereClause, status: { [Op.in]: ['paid', 'completed'] } } })
    ]);

    // 计算总页数
    const totalPages = Math.ceil(total / pageSize);

    console.log('统计数据:', { total, totalPaid, totalPages });

    // 准备包含和搜索的逻辑
    const includeOptions = [
      {
        model: User,
        as: 'orderAgent',
        attributes: ['id', 'username', 'level']
      },
      {
        model: Product,
        as: 'product',
        attributes: ['id', 'title', 'price']
      },
      {
        model: ProductLink,
        as: 'productLink',
        attributes: ['code']
      }
    ];

    // 处理搜索参数
    let finalQueryOptions = {
      where: whereClause,
      include: includeOptions,
      order: [['createdAt', 'DESC']],
      limit: pageSize,
      offset: offset
    };

    // 如果有搜索关键词，添加搜索条件
    if (req.query.search) {
      try {
        const searchTerm = req.query.search.trim();
        console.log('处理搜索关键词:', searchTerm);
        
        if (searchTerm) {
          console.log('开始搜索订单，关键词:', searchTerm);
          
          // 搜索订单ID - 直接比较，不使用LIKE
          const orderIdMatches = await Order.findAll({
            attributes: ['id'],
            where: {
              id: searchTerm
            }
          }).then(orders => orders.map(order => order.id));
          
          console.log('订单ID精确匹配结果:', orderIdMatches);

          // 记录一下实际的订单情况
          const sampleOrders = await Order.findAll({
            attributes: ['id'],
            limit: 5,
            order: [['createdAt', 'DESC']]
          });
          
          console.log('数据库中最近5个订单ID样本:', sampleOrders.map(o => o.id));

          // 搜索订单ID - 使用LIKE
          const orderIdPartialMatches = await Order.findAll({
            attributes: ['id'],
            where: {
              id: { [Op.like]: `%${searchTerm}%` }
            }
          }).then(orders => orders.map(order => order.id));
          
          console.log('订单ID模糊匹配结果:', orderIdPartialMatches);
          
          // 搜索客户名称和电话
          const customerMatches = await Order.findAll({
            attributes: ['id'],
            where: {
              [Op.or]: [
                { customerName: { [Op.like]: `%${searchTerm}%` } },
                { customerPhone: { [Op.like]: `%${searchTerm}%` } }
              ]
            }
          }).then(orders => orders.map(order => order.id));
          
          console.log('客户信息匹配结果:', customerMatches);

          // 搜索关联产品
          const productMatches = await Order.findAll({
            attributes: ['id'],
            include: [{
              model: Product,
              as: 'product',
              where: {
                title: { [Op.like]: `%${searchTerm}%` }
              }
            }]
          }).then(orders => orders.map(order => order.id));
          
          console.log('产品匹配结果:', productMatches);

          // 合并所有匹配的订单ID
          const allMatches = [
            ...orderIdMatches,
            ...orderIdPartialMatches,
            ...customerMatches,
            ...productMatches
          ];
          
          const matchedOrderIds = [...new Set(allMatches)];
          
          console.log('搜索匹配的订单IDs:', matchedOrderIds, '总数:', matchedOrderIds.length);

          // 如果有匹配的订单，使用这些ID与原始where条件组合
          if (matchedOrderIds.length > 0) {
            finalQueryOptions.where = {
              ...whereClause,
              id: { [Op.in]: matchedOrderIds }
            };
          } else {
            // 如果没有匹配的订单但有搜索关键词，则返回空结果
            console.log('搜索无匹配结果，设置为空结果');
            finalQueryOptions.where = { id: -1 }; // 不存在的ID，确保返回空结果
          }

          // 重新计算匹配搜索条件的订单总数
          const searchTotal = await Order.count({
            where: finalQueryOptions.where
          });

          // 更新分页信息
          const searchTotalPages = Math.ceil(searchTotal / pageSize) || 1; // 至少有1页，避免除以0
          console.log('搜索后的统计数据:', { searchTotal, searchTotalPages, whereClause: finalQueryOptions.where });
          
          // 将新的统计数据返回给前端，而不是修改常量
          return res.json({
            orders: await Order.findAll(finalQueryOptions).then(orders => 
              orders.map(order => {
                const orderData = order.toJSON();
                return {
                  id: orderData.id,
                  amount: Number(orderData.amount || 0),
                  status: orderData.status || 'pending',
                  customerName: orderData.customerName || '',
                  customerPhone: orderData.customerPhone || '',
                  agentCommission: Number(orderData.agentCommission || 0),
                  parentAgentCommission: Number(orderData.parentAgentCommission || 0),
                  adminCommission: Number(orderData.adminCommission || 0),
                  createdAt: orderData.createdAt,
                  paidAt: orderData.paidAt || null,
                  product: orderData.product ? {
                    title: orderData.product.title || '',
                    price: Number(orderData.product.price || 0)
                  } : null,
                  agent: orderData.orderAgent ? {
                    id: orderData.orderAgent.id,
                    username: orderData.orderAgent.username || '',
                    name: orderData.orderAgent.username || '',
                    level: orderData.orderAgent.level || 2
                  } : null,
                  productLink: orderData.productLink ? {
                    code: orderData.productLink.code
                  } : null
                };
              })
            ),
            pagination: {
              total: searchTotal,
              totalPaid,
              totalPages: searchTotalPages,
              currentPage: page,
              pageSize,
              hasMore: page < searchTotalPages
            }
          });
        }
      } catch (error) {
        console.error('处理搜索参数时出错:', error);
      }
    }

    // 获取分页数据
    const orders = await Order.findAll(finalQueryOptions);

    console.log(`获取到第${page}页数据, 共${orders.length}条记录`);

    // 格式化订单数据
    const formattedOrders = orders.map(order => {
      const orderData = order.toJSON();
      return {
        id: orderData.id,
        amount: Number(orderData.amount || 0),
        status: orderData.status || 'pending',
        customerName: orderData.customerName || '',
        customerPhone: orderData.customerPhone || '',
        agentCommission: Number(orderData.agentCommission || 0),
        parentAgentCommission: Number(orderData.parentAgentCommission || 0),
        adminCommission: Number(orderData.adminCommission || 0),
        createdAt: orderData.createdAt,
        paidAt: orderData.paidAt || null,
        product: orderData.product ? {
          title: orderData.product.title || '',
          price: Number(orderData.product.price || 0)
        } : null,
        agent: orderData.orderAgent ? {
          id: orderData.orderAgent.id,
          username: orderData.orderAgent.username || '',
          name: orderData.orderAgent.username || '',
          level: orderData.orderAgent.level || 2
        } : null,
        productLink: orderData.productLink ? {
          code: orderData.productLink.code
        } : null
      };
    });

    // 返回数据
    return res.json({
      orders: formattedOrders,
      pagination: {
        total,
        totalPaid,
        totalPages,
        currentPage: page,
        pageSize,
        hasMore: page < totalPages
      }
    });

  } catch (error) {
    console.error('获取订单列表失败:', error);
    return res.status(500).json({
      message: '获取订单列表失败',
      error: error.message,
      orders: [],
      pagination: {
        total: 0,
        totalPaid: 0,
        totalPages: 1,
        currentPage: 1,
        pageSize: 10,
        hasMore: false
      }
    });
  }
});

// 通过推广链接创建订单
router.post('/purchase/:code', async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    const { code } = req.params;
    const { 
      customerPhone, 
      deviceId,
      // 八字查询相关字段
      name,
      gender,
      calendarType,
      birthYear,
      birthMonth,
      birthDay,
      birthHour,
      birthMinute,
      birthProvince,
      birthCity
    } = req.body;
    
    // 获取客户端IP地址
    const clientIp = req.headers['x-forwarded-for'] || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     req.connection.socket?.remoteAddress || 
                     '0.0.0.0';
    
    console.log('订单创建请求 - 客户信息:', {
      clientIp,
      deviceId: deviceId || '未提供',
      customerPhone: customerPhone || '未提供',
      baziData: name ? { name, gender, birthYear, birthMonth, birthDay } : '无八字数据'
    });

    // 检查是否有未支付的相同订单（5秒内）
    const existingOrderQuery = {
      where: {
        '$productLink.code$': code,
        status: 'pending',
        createdAt: {
          [Op.gte]: new Date(Date.now() - 5 * 1000) // 5秒内
        }
      },
      include: [{
        model: ProductLink,
        as: 'productLink',
        attributes: ['code']
      }]
    };
    
    // 如果提供了手机号，添加到查询条件
    if (customerPhone) {
      existingOrderQuery.where.customerPhone = customerPhone;
    }
    
    // 如果提供了设备ID，添加到查询条件
    if (deviceId) {
      existingOrderQuery.where.deviceId = deviceId;
    }
    
    // 添加IP地址到查询条件
    existingOrderQuery.where.clientIp = clientIp;
    
    const existingOrder = await Order.findOne(existingOrderQuery, { transaction: t });

    if (existingOrder) {
      await t.rollback();
      return res.status(400).json({ 
        message: '您在短时间内已经下过单，请稍后再试或完成之前的订单',
        orderId: existingOrder.id
      });
    }

    // 查找推广链接
    const productLink = await ProductLink.findOne({
      where: { code, status: 'active' },
      include: [{
        model: Product,
        as: 'product',
        attributes: ['id', 'title', 'price', 'status', 'type']
      }, {
        model: User,
        as: 'linkAgent',
        attributes: ['id', 'level', 'parentAgentId', 'commissionRate']
      }]
    }, { transaction: t });

    if (!productLink) {
      await t.rollback();
      return res.status(404).json({ message: '推广链接不存在或已失效' });
    }

    if (!productLink.product) {
      await t.rollback();
      return res.status(404).json({ message: '产品不存在' });
    }

    if (productLink.product.status !== 'active') {
      await t.rollback();
      return res.status(400).json({ message: '产品已下架' });
    }

    const agent = productLink.linkAgent;
    if (!agent) {
      await t.rollback();
      return res.status(404).json({ message: '代理商不存在' });
    }

    // 检查是否是八字查询产品
    const isBaziProduct = productLink.product.type === 'service' && 
                         (productLink.product.title.includes('八字') || 
                          productLink.product.title.includes('命理'));

    console.log('产品类型检查:', {
      productType: productLink.product.type,
      productTitle: productLink.product.title,
      isBaziProduct
    });

    // 如果是八字产品，验证八字数据
    if (isBaziProduct) {
      if (!name || !gender || !birthYear || !birthMonth || !birthDay || 
          !birthHour || !birthMinute || !birthProvince || !birthCity) {
        await t.rollback();
        return res.status(400).json({ 
          message: '八字查询服务需要完整的生辰信息' 
        });
      }
    }

    // 获取订单金额
    const orderAmount = Number(productLink.product.price);
    console.log('订单金额:', orderAmount);

    // 计算佣金
    const { agentCommission, parentAgentCommission, adminCommission } = await calculateCommissions(agent, orderAmount);

    console.log('佣金计算结果:', {
      agentCommission,
      parentAgentCommission,
      adminCommission
    });

    // 创建订单
    const order = await Order.create({
      id: generateOrderNumber(),
      productLinkId: productLink.id,
      productId: productLink.product.id,
      amount: orderAmount,
      quantity: 1,  // 默认数量为1
      unitPrice: orderAmount,  // 单价等于订单金额
      totalAmount: orderAmount,  // 总金额等于订单金额
      orderType: productLink.product.type || 'physical',  // 使用产品的类型
      status: 'pending',
      agentId: agent.id,
      parentAgentId: agent.parentAgentId,
      customerName: name || null,  // 使用八字查询的姓名或为空
      customerPhone: customerPhone || null,
      customerAddress: isBaziProduct ? `${birthProvince} ${birthCity}` : null,  // 八字产品使用出生地
      deviceId: deviceId || null,
      clientIp: clientIp,
      agentCommission,
      parentAgentCommission,
      adminCommission
    }, { transaction: t });

    console.log('订单创建成功:', {
      orderId: order.id,
      amount: order.amount,
      agentId: order.agentId,
      isBaziProduct
    });

    // 如果是八字产品，创建八字订单详情
    if (isBaziProduct) {
      const baziOrder = await BaziOrder.create({
        orderId: order.id,
        name,
        gender,
        calendarType: calendarType || 'gregorian',
        birthYear,
        birthMonth,
        birthDay,
        birthHour,
        birthMinute,
        birthProvince,
        birthCity,
        reportStatus: 'pending'
      }, { transaction: t });

      console.log('八字订单详情创建成功:', {
        baziOrderId: baziOrder.id,
        orderId: order.id,
        name,
        gender
      });
    }

    // 更新推广链接点击次数
    await productLink.increment('clicks', { by: 1, transaction: t });

    // 检查产品类型 - 增强判断逻辑
    const productTitle = productLink.product.title || '';
    const productType = productLink.product.type || '';
    
    // 1. 检查是否是八字查询服务
    const isBaziService = productType === 'service' && 
                         (productTitle.includes('八字') || productTitle.includes('命理'));
    
    // 2. 检查是否是Deepseek AI课程
    const isDeepseekCourse = productTitle.includes('Deepseek') || productTitle.includes('大模型') || productTitle.includes('AI');
    
    console.log('【订单创建】产品标题:', productTitle);
    console.log('【订单创建】产品类型:', productType);
    console.log('【订单创建】是否八字服务:', isBaziService);
    console.log('【订单创建】是否Deepseek课程:', isDeepseekCourse);
    
    let productTypeDesc;
    if (isBaziService) {
      productTypeDesc = '八字查询服务';
    } else if (isDeepseekCourse) {
      productTypeDesc = 'Deepseek AI课程';
    } else {
      productTypeDesc = '儿童纪录片';
    }
    console.log('【订单创建】产品类型描述:', productTypeDesc);
    
    // 生成支付URL
    const paymentUrl = await createPaymentUrl({
      orderId: order.id,
      amount: orderAmount,
      productName: productLink.product.title,
      userAgent: req.headers['user-agent'] || '',
      agentId: agent.id,
      isDeepseekCourse: isDeepseekCourse, // 传递Deepseek课程信息
      isBaziService: isBaziService // 传递八字服务信息
    });

    // 提交事务
    await t.commit();

    res.status(201).json({
      message: '订单创建成功',
      order,
      paymentUrl
    });
  } catch (error) {
    await t.rollback();
    console.error('创建订单失败:', error);
    res.status(500).json({ message: '创建订单失败', error: error.message });
  }
});

// 创建订单
router.post('/', auth, async (req, res) => {
  try {
    const { productId } = req.body;
    
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({ message: '产品不存在' });
    }

    // 使用数据库中的产品价格
    const productPrice = Number(product.price);
    console.log('创建订单 - 产品信息:', {
      productId: product.id,
      title: product.title,
      price: product.price,
      priceNumber: productPrice,
      type: typeof productPrice
    });

    const order = await Order.create({
      productId,
      amount: productPrice,  // 使用数据库中的价格
      quantity: 1,  // 默认数量为1
      unitPrice: productPrice,  // 单价等于产品价格
      totalAmount: productPrice,  // 总金额等于产品价格
      orderType: product.type || 'physical',  // 使用产品的类型
      agentId: req.user.id,
      parentAgentId: req.user.parentAgentId,
      status: 'pending'
    });

    res.status(201).json(order);
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({ message: '创建订单失败，请重试' });
  }
});

// 删除订单（仅管理员）
router.delete('/:id', adminAuth, async (req, res) => {
  try {
    const order = await Order.findByPk(req.params.id);
    if (!order) {
      return res.status(404).json({ message: '订单不存在' });
    }
    
    await order.destroy();
    res.json({ message: '订单删除成功' });
  } catch (error) {
    console.error('删除订单失败:', error);
    res.status(500).json({ message: '删除订单失败，请重试' });
  }
});

// 更新订单状态
router.put('/:id/status', adminAuth, async (req, res) => {
  const t = await sequelize.transaction();
  
  try {
    const { status } = req.body;
    const order = await Order.findByPk(req.params.id, { transaction: t });

    if (!order) {
      throw new Error('订单不存在');
    }

    // 更新订单状态
    await order.update({ status }, { transaction: t });

    // 根据订单状态更新佣金状态
    if (status === 'refunded' || status === 'cancelled') {
      await Commission.update(
        { status: 'cancelled' },
        { 
          where: { orderId: order.id },
          transaction: t 
        }
      );
    } else if (status === 'paid') {
      // 订单支付时创建佣金记录
      const commissionAmount = calculateCommissions(order);
      await Commission.create({
        orderId: order.id,
        agentId: order.agentId,
        amount: commissionAmount,
        status: 'pending'
      }, { transaction: t });
    }

    await t.commit();
    res.json({ message: '订单状态更新成功' });
  } catch (error) {
    await t.rollback();
    console.error('更新订单状态失败:', error);
    res.status(500).json({ message: '更新订单状态失败，请重试' });
  }
});

export default router;