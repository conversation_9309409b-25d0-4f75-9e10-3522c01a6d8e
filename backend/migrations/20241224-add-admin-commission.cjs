'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Orders', 'adminCommission', {
      type: Sequelize.DECIMAL(10, 2),
      defaultValue: 0,
      comment: '管理员佣金'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Orders', 'adminCommission');
  }
};
