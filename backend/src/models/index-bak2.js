import User from './User.js';
import Order from './Order.js';
import Commission from './Commission.js';
import Product from './Product.js';
import ProductLink from './ProductLink.js';
import Withdrawal from './Withdrawal.js';

// 定义用户之间的关联关系
User.hasMany(User, {
  as: 'downlineAgents',
  foreignKey: 'parentAgentId',
  sourceKey: 'id'
});

User.belongsTo(User, {
  as: 'uplineAgent',
  foreignKey: 'parentAgentId',
  targetKey: 'id'
});

// 定义用户和订单的关联关系
User.hasMany(Order, {
  foreignKey: 'agentId',
  as: 'userOrders'
});

Order.belongsTo(User, {
  foreignKey: 'agentId',
  as: 'orderUser'
});

// 定义用户和佣金的关联关系
User.hasMany(Commission, {
  foreignKey: 'agentId',
  as: 'userCommissions'
});

Commission.belongsTo(User, {
  foreignKey: 'agentId',
  as: 'commissionUser'
});

// 定义产品和订单的关联关系
Product.hasMany(Order, {
  foreignKey: 'productId',
  as: 'productOrders'
});

Order.belongsTo(Product, {
  foreignKey: 'productId',
  as: 'orderProduct'
});

// 定义产品链接的关联关系
Product.hasMany(ProductLink, {
  foreignKey: 'productId',
  as: 'productToLinks'
});

ProductLink.belongsTo(Product, {
  foreignKey: 'productId',
  as: 'linkToProduct'
});

User.hasMany(ProductLink, {
  foreignKey: 'agentId',
  as: 'userLinks'
});

ProductLink.belongsTo(User, {
  foreignKey: 'agentId',
  as: 'linkUser'
});

// 定义提现的关联关系
// 代理人和提现的关系
User.hasMany(Withdrawal, {
  foreignKey: 'agentId',
  as: 'initiatedWithdrawals'  // 代理发起的提现
});

Withdrawal.belongsTo(User, {
  foreignKey: 'agentId',
  as: 'initiator'  // 发起提现的代理
});

// 审批人和提现的关系
User.hasMany(Withdrawal, {
  foreignKey: 'approvedBy',
  as: 'processedWithdrawals'  // 审批人处理的提现
});

Withdrawal.belongsTo(User, {
  foreignKey: 'approvedBy',
  as: 'processor'  // 处理提现的审批人
});

// 定义佣金和提现的关联关系
Withdrawal.hasMany(Commission, {
  foreignKey: 'withdrawalId',
  as: 'includedCommissions'  // 包含在提现中的佣金
});

Commission.belongsTo(Withdrawal, {
  foreignKey: 'withdrawalId',
  as: 'associatedWithdrawal'  // 关联的提现
});

export {
  User,
  Order,
  Commission,
  Product,
  ProductLink,
  Withdrawal
};




////////////
// import User from './User.js';
// import Order from './Order.js';
// import Commission from './Commission.js';
// import Product from './Product.js';
// import ProductLink from './ProductLink.js';
// import Withdrawal from './Withdrawal.js';

// // 定义用户之间的关联关系
// User.hasMany(User, {
//   as: 'downlineAgents',
//   foreignKey: 'parentAgentId',
//   sourceKey: 'id'
// });

// User.belongsTo(User, {
//   as: 'uplineAgent',
//   foreignKey: 'parentAgentId',
//   targetKey: 'id'
// });

// // 定义用户和订单的关联关系
// User.hasMany(Order, {
//   foreignKey: 'agentId',
//   as: 'agentOrders'
// });

// User.hasMany(Order, {
//   foreignKey: 'parentAgentId',
//   as: 'parentAgentOrders'
// });

// Order.belongsTo(User, {
//   foreignKey: 'agentId',
//   as: 'orderAgent'
// });

// Order.belongsTo(User, {
//   foreignKey: 'parentAgentId',
//   as: 'orderParentAgent'
// });

// // 定义用户和佣金的关联关系
// User.hasMany(Commission, {
//   foreignKey: 'agentId',
//   as: 'userCommissions'
// });

// Commission.belongsTo(User, {
//   foreignKey: 'agentId',
//   as: 'commissionAgent'
// });

// // 定义产品和订单的关联关系
// Product.hasMany(Order, {
//   foreignKey: 'productId',
//   as: 'productOrders'
// });

// Order.belongsTo(Product, {
//   foreignKey: 'productId',
//   as: 'orderProduct'
// });

// // 定义产品链接的关联关系
// Product.hasMany(ProductLink, {
//   foreignKey: 'productId',
//   as: 'productLinks',
//   onDelete: 'CASCADE',
//   onUpdate: 'CASCADE'
// });

// ProductLink.belongsTo(Product, {
//   foreignKey: 'productId',
//   as: 'linkProduct',
//   targetKey: 'id'
// });

// User.hasMany(ProductLink, {
//   foreignKey: 'agentId',
//   as: 'userLinks',
//   onDelete: 'CASCADE',
//   onUpdate: 'CASCADE'
// });

// ProductLink.belongsTo(User, {
//   foreignKey: 'agentId',
//   as: 'linkAgent',
//   targetKey: 'id'
// });

// // 定义提现的关联关系
// User.hasMany(Withdrawal, {
//   foreignKey: 'agentId',
//   as: 'agentWithdrawals'
// });

// Withdrawal.belongsTo(User, {
//   foreignKey: 'agentId',
//   as: 'withdrawalAgent'
// });

// User.hasMany(Withdrawal, {
//   foreignKey: 'approvedBy',
//   as: 'approvedWithdrawals'
// });

// Withdrawal.belongsTo(User, {
//   foreignKey: 'approvedBy',
//   as: 'withdrawalApprover'
// });

// // 定义佣金和提现的关联关系
// Commission.belongsTo(Withdrawal, {
//   foreignKey: 'withdrawalId',
//   as: 'withdrawal'
// });

// Withdrawal.hasMany(Commission, {
//   foreignKey: 'withdrawalId',
//   as: 'commissions'
// });

// export {
//   User,
//   Order,
//   Commission,
//   Product,
//   ProductLink,
//   Withdrawal
// };