export interface FormData {
  name: string;
  sex: '1' | '2'; // 1: 男, 2: 女
  birthday: string;
  address: string;
  yearType: '1' | '2'; // 1: 公历, 2: 农历
  date: {
    yearType: number;
    currentDate: number[];
    currentTime: number[];
  };
  relation: string;
}

export interface LinkConfig {
  app: number;
  theme: number;
  button: number;
  oc: Record<string, any>;
  bc: Record<string, any>;
  config?: {
    openRelation?: boolean;
  };
}

export interface Trade {
  tradeid: string;
  status: number;
  amount: string;
  query: FormData;
  content?: any;
}

export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

export interface DatePickerData {
  yearType: number;
  currentDate: number[];
  currentTime: number[];
}

export interface AreaData {
  code: string;
  name: string;
  children?: AreaData[];
}

export enum TradeStatus {
  created = 0,
  paid = 1,
  success = 2,
  failed = 3,
  refund = 4,
  closed = 5,
  finished = 6,
}
