import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import sequelize from '../config/database.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function applyDatabaseChanges() {
  try {
    console.log('开始应用数据库更改...');
    
    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, 'add_order_tracking_fields.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // 按语句分割SQL内容
    const sqlStatements = sqlContent
      .split(';')
      .filter(statement => statement.trim() !== '')
      .map(statement => statement.trim() + ';');
    
    // 逐条执行SQL语句
    for (const statement of sqlStatements) {
      console.log(`执行SQL语句: ${statement}`);
      try {
        await sequelize.query(statement);
        console.log('SQL语句执行成功');
      } catch (error) {
        // 如果字段或索引已存在，忽略错误
        if (error.message.includes('Duplicate column name') || 
            error.message.includes('Duplicate key name')) {
          console.log('字段或索引已存在，跳过');
        } else {
          throw error;
        }
      }
    }
    
    console.log('数据库更改应用完成');
  } catch (error) {
    console.error('应用数据库更改时出错:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行更改
applyDatabaseChanges();
