-- 数据库结构冲突修复脚本
-- 执行前请备份数据库！

USE commission_platform4;

-- 1. 删除重复的外键约束（保留较新的约束）

-- PaymentConfigs 表
ALTER TABLE PaymentConfigs DROP FOREIGN KEY paymentconfigs_ibfk_1;

-- ProductImages 表  
ALTER TABLE ProductImages DROP FOREIGN KEY productimages_ibfk_1;

-- ProductLinks 表
ALTER TABLE ProductLinks DROP FOREIGN KEY productlinks_ibfk_1;
ALTER TABLE ProductLinks DROP FOREIGN KEY productlinks_ibfk_2;

-- Withdrawals 表
ALTER TABLE Withdrawals DROP FOREIGN KEY withdrawals_ibfk_1;
ALTER TABLE Withdrawals DROP FOREIGN KEY withdrawals_ibfk_2;
ALTER TABLE Withdrawals DROP FOREIGN KEY withdrawals_ibfk_4;

-- 2. 修复 Orders 表的 agentId 字段，允许 NULL 值（用于起名业务等没有代理的订单）
ALTER TABLE Orders MODIFY COLUMN agentId INT NULL;

-- 3. 检查并修复可能的数据不一致问题
-- 查找没有有效 agentId 的订单
SELECT id, customerName, orderType, agentId 
FROM Orders 
WHERE agentId NOT IN (SELECT id FROM Users);

-- 4. 添加索引优化（如果不存在）
-- 为经常查询的字段添加索引
CREATE INDEX IF NOT EXISTS idx_orders_status ON Orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_order_type ON Orders(orderType);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON Orders(createdAt);
CREATE INDEX IF NOT EXISTS idx_commissions_status ON Commissions(status);
CREATE INDEX IF NOT EXISTS idx_products_type ON Products(type);

-- 5. 验证外键约束的一致性
-- 检查是否有孤立的记录
SELECT 'Orders with invalid productId' as issue, COUNT(*) as count
FROM Orders o 
LEFT JOIN Products p ON o.productId = p.id 
WHERE p.id IS NULL;

SELECT 'Orders with invalid agentId' as issue, COUNT(*) as count
FROM Orders o 
LEFT JOIN Users u ON o.agentId = u.id 
WHERE o.agentId IS NOT NULL AND u.id IS NULL;

SELECT 'Commissions with invalid orderId' as issue, COUNT(*) as count
FROM Commissions c 
LEFT JOIN Orders o ON c.orderId = o.id 
WHERE o.id IS NULL;

SELECT 'Commissions with invalid agentId' as issue, COUNT(*) as count
FROM Commissions c 
LEFT JOIN Users u ON c.agentId = u.id 
WHERE u.id IS NULL;

-- 6. 清理可能的重复数据
-- 检查是否有重复的产品链接
SELECT agentId, productId, COUNT(*) as count
FROM ProductLinks 
GROUP BY agentId, productId 
HAVING COUNT(*) > 1;

-- 7. 验证枚举值的一致性
-- 检查 Orders 表的状态值
SELECT DISTINCT status FROM Orders;

-- 检查 Products 表的类型值
SELECT DISTINCT type FROM Products;

-- 检查 Users 表的角色值
SELECT DISTINCT role FROM Users;

-- 8. 优化表结构建议
-- 添加缺失的注释
ALTER TABLE Orders MODIFY COLUMN agentId INT NULL COMMENT '代理ID，起名业务等可以为空';
ALTER TABLE Orders MODIFY COLUMN productLinkId INT NULL COMMENT '产品链接ID，直接订单可以为空';

-- 9. 创建视图简化查询
CREATE OR REPLACE VIEW order_summary AS
SELECT 
    o.id,
    o.orderNumber,
    o.amount,
    o.status,
    o.orderType,
    o.customerName,
    o.createdAt,
    p.title as productTitle,
    p.type as productType,
    u.name as agentName,
    CASE 
        WHEN o.productLinkId IS NOT NULL THEN '代理订单'
        ELSE '直接订单'
    END as orderSource
FROM Orders o
LEFT JOIN Products p ON o.productId = p.id
LEFT JOIN Users u ON o.agentId = u.id;

-- 10. 显示修复结果
SELECT '数据库结构冲突修复完成' as status;
SELECT 'PaymentConfigs外键约束' as table_name, COUNT(*) as constraint_count
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'commission_platform4' 
AND TABLE_NAME = 'PaymentConfigs' 
AND REFERENCED_TABLE_NAME IS NOT NULL;

SELECT 'ProductImages外键约束' as table_name, COUNT(*) as constraint_count
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'commission_platform4' 
AND TABLE_NAME = 'ProductImages' 
AND REFERENCED_TABLE_NAME IS NOT NULL;
