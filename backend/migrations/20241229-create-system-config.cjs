 'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 创建SystemConfigs表
    await queryInterface.createTable('SystemConfigs', {
      key: {
        type: Sequelize.STRING,
        primaryKey: true,
        allowNull: false
      },
      value: {
        type: Sequelize.TEXT,
        allowNull: false
      },
      description: {
        type: Sequelize.STRING,
        allowNull: true
      },
      group: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'general'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // 添加默认系统配置
    return queryInterface.bulkInsert('SystemConfigs', [
      {
        key: 'default_commission_rate',
        value: '80',
        description: '默认佣金比例（%）',
        group: 'commission',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'min_withdrawal_amount',
        value: '50',
        description: '最小提现金额（元）',
        group: 'withdrawal',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'admin_commission_rate',
        value: '20',
        description: '平台管理员佣金比例（%）',
        group: 'commission',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        key: 'system_name',
        value: '"多级代理佣金平台"',
        description: '系统名称',
        group: 'general',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  async down(queryInterface, Sequelize) {
    return queryInterface.dropTable('SystemConfigs');
  }
};