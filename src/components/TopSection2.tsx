import React from 'react';

// 导入图片
const a1 = '/assets/theme/2/1.png';
const a3 = '/assets/theme/2/3.png';
const a4 = '/assets/theme/2/4.png';
const a5 = '/assets/theme/2/5.png';

export const TopSection2: React.FC = () => {
  return (
    <div className="top-animate2">
      <div className="inner">
        <img className="bg" src={a1} alt="Background" />
        <img className="large" src={a3} alt="Large circle" />
        <img className="lite" src={a4} alt="Small circle" />
        <img className="cover" src={a5} alt="Cover" />
      </div>
    </div>
  );
};
