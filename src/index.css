@import 'antd/dist/reset.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* 精确匹配传统八字网站配色方案 */
    --background: 51 100% 90%;         /* 背景色 #FFF7CC */
    --foreground: 210 20% 20%;         /* 深墨色文字 #333a4d */
    --card: 0 0% 100%;                 /* 纯白卡片 */
    --card-foreground: 210 20% 20%;    /* 深墨色卡片文字 */
    --popover: 0 0% 100%;              /* 纯白弹窗 */
    --popover-foreground: 210 20% 20%; /* 深墨色弹窗文字 */
    --primary: 38 65% 45%;             /* 主色 #C38022 */
    --primary-foreground: 0 0% 100%;   /* 白色主色文字 */
    --secondary: 51 100% 92%;          /* 浅次要色 #FFF5D9 */
    --secondary-foreground: 210 20% 20%; /* 深墨色次要文字 */
    --muted: 51 100% 95%;              /* 柔和背景色 #FFFAE5 */
    --muted-foreground: 42 79% 25%;    /* 小字体颜色 #73590D */
    --accent: 43 74% 49%;              /* 传统金色强调色 #d4af37 */
    --accent-foreground: 210 20% 20%;  /* 深墨色强调文字 */
    --destructive: 0 84% 27%;          /* 深红色警告 #8b0000 */
    --destructive-foreground: 0 0% 100%; /* 白色警告文字 */
    --border: 51 60% 85%;              /* 浅边框色 #F0E6A3 */
    --input: 51 60% 85%;               /* 浅输入框色 #F0E6A3 */
    --ring: 38 65% 45%;                /* 主色焦点环 */
    --radius: 0.5rem;
  }

  .dark {
    /* 深色模式保持传统风格 */
    --background: 210 20% 8%;          /* 深墨色背景 */
    --foreground: 45 20% 96%;          /* 米白色文字 */
    --card: 210 20% 12%;               /* 深灰卡片 */
    --card-foreground: 45 20% 96%;     /* 米白色卡片文字 */
    --popover: 210 20% 12%;            /* 深灰弹窗 */
    --popover-foreground: 45 20% 96%;  /* 米白色弹窗文字 */
    --primary: 45 50% 65%;             /* 金色主色 */
    --primary-foreground: 210 20% 8%;  /* 深墨色主色文字 */
    --secondary: 210 15% 20%;          /* 深灰次要色 */
    --secondary-foreground: 45 20% 96%; /* 米白色次要文字 */
    --muted: 210 15% 20%;              /* 深灰柔和色 */
    --muted-foreground: 45 10% 75%;    /* 浅米色文字 */
    --accent: 45 50% 65%;              /* 金色强调色 */
    --accent-foreground: 210 20% 8%;   /* 深墨色强调文字 */
    --destructive: 0 70% 55%;          /* 朱红色警告 */
    --destructive-foreground: 0 0% 100%; /* 白色警告文字 */
    --border: 210 15% 20%;             /* 深灰边框 */
    --input: 210 15% 20%;              /* 深灰输入框 */
    --ring: 45 50% 65%;                /* 金色焦点环 */
  }
}

/* 添加自定义全局样式 */

/* 传统中国风装饰元素 */
.traditional-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23D4AF37' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
}

/* 精确匹配传统八字网站样式 */
.bazi-report {
  background: linear-gradient(135deg, #FFF7CC 0%, #F5F0B8 100%);
  min-height: 100vh;
}

.bazi-section {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid hsl(51, 60%, 85%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(195, 128, 34, 0.1);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.bazi-title {
  color: #C38022;
  font-weight: 600;
  border-bottom: 2px solid #C38022;
  padding-bottom: 6px;
  margin-bottom: 12px;
}

.bazi-content {
  color: #73590D;
  line-height: 1.6;
}

.bazi-highlight {
  background: linear-gradient(135deg, hsl(43, 74%, 49%) 0%, hsl(43, 80%, 55%) 100%);
  color: hsl(210, 20%, 20%);
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(212, 175, 55, 0.3);
}

.bazi-lucky {
  color: hsl(120, 60%, 30%);
  font-weight: 600;
}

.bazi-unlucky {
  color: hsl(0, 84%, 27%);
  font-weight: 600;
}

/* 五行颜色 */
.bazi-wuxing-wood { color: hsl(120, 60%, 30%); }
.bazi-wuxing-fire { color: hsl(0, 84%, 27%); }
.bazi-wuxing-earth { color: hsl(43, 74%, 49%); }
.bazi-wuxing-metal { color: hsl(210, 10%, 50%); }
.bazi-wuxing-water { color: hsl(210, 100%, 20%); }

/* 响应式表格样式 */
@media (max-width: 768px) {
  .responsive-table .ant-table-cell {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }
  
  .responsive-table .ant-table-thead > tr > th,
  .responsive-table .ant-table-tbody > tr > td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
  }
  
  .responsive-table .ant-table-thead > tr > th:first-child,
  .responsive-table .ant-table-tbody > tr > td:first-child {
    max-width: 80px;
  }
  
  /* 强化滚动指示器 */
  .responsive-table-container {
    position: relative;
  }
  
  .responsive-table-container::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    width: 20px;
    background: linear-gradient(to right, rgba(255,255,255,0), rgba(0,0,0,0.05));
    pointer-events: none;
    z-index: 1;
  }
  
  /* 改善表格行的可读性 */
  .responsive-table .ant-table-tbody > tr > td {
    border-bottom: 1px solid rgba(0,0,0,0.06);
  }
  
  .responsive-table .ant-table-tbody > tr:hover > td {
    background-color: rgba(212, 175, 55, 0.1) !important;
  }
  
  /* 简化操作按钮 */
  .responsive-table .ant-btn {
    padding: 0 8px;
    height: 24px;
    font-size: 12px;
  }
  
  .responsive-table .ant-space-item {
    margin-right: 4px !important;
  }
}

@media (max-width: 576px) {
  body {
    font-size: 14px;
  }
  
  .ant-modal {
    width: 95% !important;
    max-width: 500px;
  }
  
  .ant-form-item-label {
    padding: 0 0 4px !important;
  }
  
  .responsive-table .ant-pagination-item,
  .responsive-table .ant-pagination-prev,
  .responsive-table .ant-pagination-next {
    min-width: 24px !important;
    height: 24px !important;
    line-height: 22px !important;
  }
  
  .responsive-table .ant-pagination-item a {
    font-size: 12px !important;
  }
  
  /* 模态框样式优化 */
  .commission-modal .ant-modal-content {
    padding: 16px !important;
  }
  
  .commission-modal .ant-modal-header {
    padding: 16px 0 8px !important;
    margin-bottom: 8px !important;
  }
  
  .commission-modal .ant-modal-title {
    font-size: 16px !important;
  }
  
  .commission-modal .ant-modal-body {
    padding: 8px 0 !important;
  }
  
  .commission-modal .ant-modal-footer {
    margin-top: 8px !important;
    padding: 8px 0 0 !important;
  }
  
  .commission-modal .ant-form-item {
    margin-bottom: 12px !important;
  }
  
  /* 移动端滚动指示样式 */
  .responsive-table-container {
    position: relative;
  }
  
  .responsive-table-container::before {
    content: '← 左右滑动查看更多 →';
    display: block;
    text-align: center;
    padding: 8px;
    background-color: rgba(212, 175, 55, 0.1);
    border-radius: 4px;
    color: hsl(210, 15%, 15%);
    font-size: 12px;
    margin-bottom: 8px;
    animation: fadeOut 5s forwards 1s;
  }
  
  @keyframes fadeOut {
    0% { opacity: 1; }
    70% { opacity: 1; }
    100% { opacity: 0; visibility: hidden; }
  }
}
