import { Order, User, Product } from '../models/index.js';
import { createPayment, verifyPayment } from '../services/alipay.js';
import { calculateCommissions } from '../utils/commissionUtils.js';
import { generateOrderNumber } from '../utils/orderUtils.js';
import sequelize from '../config/database.js';
import { Op } from 'sequelize';

// 创建订单
export const createOrder = async (req, res) => {
  try {
    const { productId, customerName, customerPhone } = req.body;
    const agent = req.user;

    // 查找产品
    const product = await Product.findByPk(productId);
    if (!product) {
      return res.status(404).json({ message: '产品不存在' });
    }

    // 计算佣金
    let commissions;
    if (agent.level === 0 && agent.id === req.user.id) {  // 管理员使用自己的链接下单
      commissions = {
        adminCommission: product.price,  // 管理员获取100%的佣金
        agentCommission: 0,
        parentAgentCommission: 0
      };
    } else {  // 代理下单或通过代理链接下单
      // commissions = calculateCommissions(
      //   product.price,
      //   agent.commissionRate * 100,
      //   agent.level === 2 && agent.parentAgentId ? 
      //     (await User.findByPk(agent.parentAgentId))?.commissionRate * 100 : 
      //     null
      // );
     // 使用代理对象和订单金额计算佣金
commissions = await calculateCommissions(agent, product.price);
    }

    // 创建订单
    const order = await Order.create({
      id: generateOrderNumber(),
      productId,
      amount: product.price,
      agentId: agent.id,
      parentAgentId: agent.parentAgentId,
      status: 'pending',
      customerName,
      customerPhone,
      adminCommission: commissions.adminCommission,
      agentCommission: commissions.agentCommission || commissions.level1Commission || 0,
      parentAgentCommission: commissions.parentAgentCommission || commissions.level2Commission || 0
    });

    // 生成支付链接
    const paymentUrl = await createPayment(order);
    
    res.status(201).json({ 
      message: '订单创建成功',
      order,
      paymentUrl 
    });
  } catch (error) {
    console.error('创建订单失败:', error);
    res.status(500).json({ message: '创建订单失败', error: error.message });
  }
};

// 获取代理订单
export const getAgentOrders = async (req, res) => {
  try {
    const agent = req.user;
    let where = {};
    
    if (agent.level === 0) {  // 管理员可以看到所有订单
      where = {};
    } else {  // 代理只能看到自己的订单
      where = {
        [Op.or]: [
          { agentId: agent.id },
          { parentAgentId: agent.id }
        ]
      };
    }

    const orders = await Order.findAll({
      where,
      include: [
        {
          model: Product,
          attributes: ['id', 'title', 'price']
        },
        {
          model: User,
          as: 'orderAgent',
          attributes: ['id', 'username', 'level']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    res.json(orders);
  } catch (error) {
    console.error('获取订单列表失败:', error);
    res.status(500).json({ message: '获取订单列表失败', error: error.message });
  }
};

// 处理支付回调
export const handlePaymentCallback = async (req, res) => {
  try {
    const isValid = await verifyPayment(req.body);
    if (!isValid) {
      return res.status(400).json({ message: '支付签名无效' });
    }

    const order = await Order.findOne({ 
      where: { 
        id: req.body.out_trade_no,
        status: 'pending'
      }
    });

    if (!order) {
      return res.status(404).json({ message: '订单不存在或已处理' });
    }

    // 开启事务
    const t = await sequelize.transaction();

    try {
      // 1. 更新订单状态
      await order.update({
        status: 'paid',
        paidAt: new Date()
      }, { transaction: t });

      // 2. 更新管理员佣金
      const admin = await User.findOne({
        where: { level: 0 }
      });
      if (admin) {
        await admin.addCommission(order.adminCommission);
      }

      // 3. 更新代理佣金
      const agent = await User.findByPk(order.agentId);
      if (agent) {
        await agent.addCommission(order.agentCommission);
      }

      // 4. 更新上级代理佣金
      if (order.parentAgentId) {
        const parentAgent = await User.findByPk(order.parentAgentId);
        if (parentAgent) {
          await parentAgent.addCommission(order.parentAgentCommission);
        }
      }

      await t.commit();
      res.json({ message: '支付成功' });
    } catch (error) {
      await t.rollback();
      throw error;
    }
  } catch (error) {
    console.error('处理支付回调失败:', error);
    res.status(500).json({ message: '处理支付回调失败', error: error.message });
  }
};