import { User, ProductLink } from '../models/index.js';

async function checkAgent2() {
  try {
    // 查询 agent2 用户信息
    const agent2 = await User.findOne({
      where: { username: 'agent2' },
      raw: true
    });

    console.log('Agent2 用户信息:', agent2);

    if (agent2) {
      // 查询 agent2 的产品链接信息
      const productLinks = await ProductLink.findAll({
        where: { agentId: agent2.id },
        raw: true
      });

      console.log('Agent2 的产品链接:', productLinks);
    }

    process.exit(0);
  } catch (error) {
    console.error('查询失败:', error);
    process.exit(1);
  }
}

checkAgent2();
