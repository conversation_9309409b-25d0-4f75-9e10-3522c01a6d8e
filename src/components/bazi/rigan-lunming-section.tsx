import type { MingZhuRiGanLunMingXinXi } from '../../types/bazi';
import { SectionCard } from './section-card';
import { InfoItem, ParagraphText } from './info-item';
import { Sun } from 'lucide-react';

interface RiGanLunMingSectionProps {
  data: MingZhuRiGanLunMingXinXi;
}

export function RiGanLunMingSection({ data }: RiGanLunMingSectionProps) {
  return (
    <SectionCard title="命主日干论命信息" icon={Sun} contentClassName="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-3">
        <InfoItem label="生辰八字" value={data.生辰八字.join(' ')} />
        <InfoItem label="八字五行" value={data.八字五行.join(' ')} />
        <InfoItem label="五行纳音" value={data.五行纳音.join('，')} />
        <InfoItem label="五行个数" value={data.五行个数} />
      </div>
      <InfoItem label="同类异类" value={data.同类异类} />
      <InfoItem label="五行旺衰" value={<ParagraphText text={data.五行旺衰} />} />
      <InfoItem label="四季用神" value={data.四季用神} />
      <InfoItem label="日干心性" value={<ParagraphText text={data.日干心性} />} />
      <InfoItem label="日干层次" value={<ParagraphText text={data.日干层次} />} />
      <InfoItem label="日干支分析" value={<ParagraphText text={data.日干支分析} />} />
      <div className="mt-4 pt-4 border-t border-border/50">
        <h3 className="text-lg font-semibold text-primary mb-3">详细解读</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-primary mb-1.5 text-base">性格分析:</h4>
            <ParagraphText text={data.性格分析} />
          </div>
          <div>
            <h4 className="font-medium text-primary mb-1.5 text-base">爱情分析:</h4>
            <ParagraphText text={data.爱情分析} />
          </div>
          <div>
            <h4 className="font-medium text-primary mb-1.5 text-base">事业分析:</h4>
            <ParagraphText text={data.事业分析} />
          </div>
          <div>
            <h4 className="font-medium text-primary mb-1.5 text-base">财运分析:</h4>
            <ParagraphText text={data.财运分析} />
          </div>
          <div>
            <h4 className="font-medium text-primary mb-1.5 text-base">健康分析:</h4>
            <ParagraphText text={data.健康分析} />
          </div>
        </div>
      </div>
    </SectionCard>
  );
} 