import React, { useState, useEffect } from 'react';
import { ScrollText, Star, Sparkles, Crown, Download, Share2, RefreshCw } from 'lucide-react';
import { MingZhuInfoSection } from '../components/bazi/mingzhu-info-section';
import { ChengGuInfoSection } from '../components/bazi/chenggu-info-section';
import '../styles/BaziReportEnhanced.css';

// 模拟数据
const mockReportData = {
  命主信息: {
    姓名: "张三",
    性别: "男",
    岁数: "28岁",
    星座: "天秤座",
    生肖: "龙",
    公历生日: "1996年10月15日 14时30分",
    农历生日: "丙子年九月初三日未时",
    出生地点: "北京市朝阳区"
  },
  命主八字称骨信息: {
    称骨重量: "4两1钱",
    称骨诗: "此命推来自不同，为人能干异凡庸。中年还有逍遥福，不比前时运来通。",
    称骨解释: "此命为人性躁，心直口快，有才能，见善不欺，逢恶不怕，事有始终，量能宽大，但不能聚财，兄弟六亲无力，自立家计，出外方好，初限二十三四五不遂，二十七八有好运到，犹如枯木逢春，中限四十九岁有险，四十多岁古镜重磨，明月再圆。五十六七八九岁末限明月又被云侵，交七十方大运。"
  }
};

const BaziReportDemo: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // 模拟加载过程
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-yellow-50 text-foreground">
        {/* 顶部装饰 */}
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-amber-400 via-orange-400 to-yellow-400"></div>
        
        <div className="flex flex-col items-center justify-center min-h-screen mobile-container p-4 relative">
          {/* 背景装饰 */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute top-20 left-10 w-32 h-32 bg-amber-200/20 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute bottom-20 right-10 w-40 h-40 bg-orange-200/20 rounded-full blur-xl animate-pulse delay-1000"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-yellow-200/10 rounded-full blur-2xl animate-pulse delay-500"></div>
          </div>

          {/* 主要内容 */}
          <div className="relative z-10 text-center max-w-2xl mx-auto">
            {/* 图标和标题 */}
            <div className="mb-8">
              <div className="relative inline-block">
                <div className="absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-400 rounded-full blur-lg opacity-30 animate-pulse"></div>
                <div className="relative bg-white rounded-full p-6 shadow-xl">
                  <Sparkles className="h-12 w-12 text-amber-500 animate-spin" style={{ animationDuration: '3s' }} />
                </div>
              </div>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mt-6 mb-4">
                正在生成您的专属八字命盘
              </h1>
              <p className="text-lg text-gray-600 mb-2">
                请稍候，我们正在为您准备专属的八字报告
              </p>
            </div>

            {/* 进度指示器 */}
            <div className="mb-8">
              <div className="flex items-center justify-center space-x-3 text-lg font-medium text-gray-700">
                <div className="h-6 w-6 animate-spin text-amber-500">
                  <div className="h-full w-full border-2 border-amber-500 border-t-transparent rounded-full"></div>
                </div>
                <span>系统正在为您计算八字命盘...</span>
              </div>
              
              {/* 进度条 */}
              <div className="mt-4 w-full max-w-md mx-auto">
                <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div className="progress-bar h-full rounded-full" style={{ width: '75%' }}></div>
                </div>
              </div>
            </div>

            {/* 详细步骤 */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-amber-100">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center justify-center">
                <Crown className="h-5 w-5 text-amber-500 mr-2" />
                命理分析进行中
              </h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>✓ 正在分析您的生辰八字</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
                  <span>⟳ 正在计算五行旺衰</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
                  <span>○ 正在生成详细报告</span>
                </div>
              </div>
              <div className="mt-4 text-xs text-gray-500">
                预计还需要 15 秒
              </div>
            </div>

            {/* 底部提示 */}
            <div className="mt-8 text-sm text-gray-500">
              <p>请保持页面打开，我们正在为您精心准备专业的命理分析</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bazi-report min-h-screen bg-gradient-to-br from-amber-50/30 via-white to-orange-50/30">
      {/* 顶部装饰条 */}
      <div className="h-1 bg-gradient-to-r from-amber-400 via-orange-400 to-yellow-400"></div>
      
      {/* 优化的页面头部 */}
      <header className="relative bg-gradient-to-r from-amber-50 via-orange-50 to-yellow-50 border-b border-amber-200/50">
        <div className="absolute inset-0 opacity-50" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f59e0b' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
        
        <div className="relative container mx-auto px-4 py-8 md:py-12">
          <div className="text-center">
            {/* 主标题区域 */}
            <div className="mb-6">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-amber-400 to-orange-400 rounded-full shadow-lg mb-4">
                <ScrollText className="h-8 w-8 text-white" />
              </div>
              <h1 className="enhanced-title text-3xl md:text-4xl lg:text-5xl font-bold mb-3">
                张三 命盘报告
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-4">
                公历生日: 1996年10月15日 14时30分
              </p>
              
              {/* 报告状态标签 */}
              <div className="flex items-center justify-center space-x-4 text-sm">
                <div className="status-indicator flex items-center space-x-2 bg-green-100 text-green-700 px-3 py-1 rounded-full">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span>报告已生成</span>
                </div>
                <div className="flex items-center space-x-2 bg-amber-100 text-amber-700 px-3 py-1 rounded-full">
                  <Star className="h-3 w-3" />
                  <span>专业解读</span>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="flex flex-wrap items-center justify-center gap-3">
              <button 
                onClick={() => window.print()}
                className="enhanced-button flex items-center space-x-2 bg-white hover:bg-gray-50 text-gray-700 px-4 py-2 rounded-lg border border-gray-200 shadow-sm transition-colors hover-lift"
              >
                <Download className="h-4 w-4" />
                <span>保存报告</span>
              </button>
              <button 
                onClick={() => alert('分享功能演示')}
                className="enhanced-button glow-effect flex items-center space-x-2 bg-amber-500 hover:bg-amber-600 text-white px-4 py-2 rounded-lg shadow-sm transition-colors hover-lift"
              >
                <Share2 className="h-4 w-4" />
                <span>分享报告</span>
              </button>
              <button 
                onClick={() => window.location.reload()}
                className="enhanced-button flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg border border-gray-200 shadow-sm transition-colors hover-lift"
              >
                <RefreshCw className="h-4 w-4" />
                <span>刷新</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="container mx-auto px-3 sm:px-4 md:px-6 py-8 md:py-12">
        {/* 内容提示 */}
        <div className="mb-8 bg-gradient-to-r from-amber-100 to-orange-100 border border-amber-200 rounded-xl p-4 md:p-6">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-amber-500 rounded-full flex items-center justify-center">
                <Sparkles className="h-4 w-4 text-white" />
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-amber-800 mb-2">您的专属八字命盘报告</h3>
              <p className="text-amber-700 text-sm leading-relaxed">
                以下是根据您的生辰八字精心计算的详细命理分析。每个部分都包含了深度的命理解读，
                帮助您更好地了解自己的性格特点、运势走向和人生指导。
              </p>
            </div>
          </div>
        </div>

        {/* 报告内容 */}
        <div className="space-y-8 md:space-y-12">
          <div className="content-section">
            <MingZhuInfoSection data={mockReportData.命主信息 as any} />
          </div>
          <div className="content-section">
            <ChengGuInfoSection data={mockReportData.命主八字称骨信息 as any} />
          </div>
        </div>
      </main>

      {/* 优化的页脚 */}
      <footer className="bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-2 text-gray-600">
              <Crown className="h-5 w-5 text-amber-500" />
              <span className="font-medium">专业命理分析服务</span>
            </div>
            <p className="text-sm text-gray-500 max-w-2xl mx-auto leading-relaxed">
              本报告基于传统八字命理学说生成，仅供参考和娱乐。命运掌握在自己手中，
              积极向上的心态和不懈的努力才是改变人生的关键。
            </p>
            <div className="flex items-center justify-center space-x-6 text-xs text-gray-400">
              <span>&copy; {new Date().getFullYear()} 八字命盘</span>
              <span>•</span>
              <span>All rights reserved</span>
              <span>•</span>
              <span>仅供参考</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default BaziReportDemo;
