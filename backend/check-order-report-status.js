import { Order, BaziOrder, Product } from './src/models/index.js';

async function checkOrderReportStatus() {
  console.log('=== 检查订单报告状态 ===\n');
  
  const orderId = '250707185840805299';
  
  try {
    // 查找订单及其八字订单信息
    const order = await Order.findByPk(orderId, {
      include: [{
        model: BaziOrder,
        as: 'baziOrder',  // 使用正确的别名
        required: false
      }, {
        model: Product,
        as: 'product'  // 使用正确的别名
      }]
    });
    
    if (!order) {
      console.log('❌ 订单不存在');
      return;
    }
    
    console.log('📋 订单基本信息:');
    console.log(`  订单ID: ${order.id}`);
    console.log(`  订单号: ${order.orderNumber}`);
    console.log(`  客户姓名: ${order.customerName}`);
    console.log(`  订单状态: ${order.status}`);
    console.log(`  产品名称: ${order.product?.name}`);
    console.log(`  产品类型: ${order.product?.type}`);
    console.log(`  创建时间: ${order.createdAt}`);
    console.log(`  支付时间: ${order.paidAt}`);
    
    if (order.baziOrder) {
      console.log('\n🔮 八字订单信息:');
      console.log(`  八字订单ID: ${order.baziOrder.id}`);
      console.log(`  报告状态: ${order.baziOrder.reportStatus}`);
      console.log(`  客户信息存在: ${!!order.baziOrder.customerInfo}`);
      console.log(`  报告数据存在: ${!!order.baziOrder.reportData}`);
      console.log(`  创建时间: ${order.baziOrder.createdAt}`);
      console.log(`  更新时间: ${order.baziOrder.updatedAt}`);
      
      if (order.baziOrder.customerInfo) {
        console.log('\n👤 客户信息:');
        try {
          const customerInfo = JSON.parse(order.baziOrder.customerInfo);
          Object.entries(customerInfo).forEach(([key, value]) => {
            console.log(`  ${key}: ${value}`);
          });
        } catch (e) {
          console.log('  客户信息解析失败:', e.message);
        }
      }
      
      // 分析报告状态
      console.log('\n🔍 状态分析:');
      const reportStatus = order.baziOrder.reportStatus;
      
      switch (reportStatus) {
        case 'pending':
          console.log('⏳ 报告状态为pending - 尚未生成报告');
          console.log('💡 需要调用生成报告接口');
          break;
        case 'generating':
          console.log('⚙️ 报告状态为generating - 正在生成中');
          console.log('💡 请等待生成完成');
          break;
        case 'generated':
          console.log('✅ 报告状态为generated - 报告已生成');
          console.log('💡 可以正常获取报告');
          break;
        case 'failed':
          console.log('❌ 报告状态为failed - 生成失败');
          console.log('💡 需要重新生成报告');
          break;
        default:
          console.log(`❓ 未知的报告状态: ${reportStatus}`);
      }
      
      // 如果报告状态不是generated，提供解决方案
      if (reportStatus !== 'generated') {
        console.log('\n🛠️ 解决方案:');
        console.log('1. 手动触发报告生成:');
        console.log(`   POST /api/bazi/orders/${orderId}/generate-report`);
        console.log('2. 或者运行以下脚本生成报告:');
        console.log(`   node generate-missing-report.js ${orderId}`);
      }
      
    } else {
      console.log('\n❌ 没有找到对应的八字订单记录');
      console.log('💡 这可能是数据不一致的问题');
      
      // 检查是否有孤立的BaziOrder记录
      const allBaziOrders = await BaziOrder.findAll({
        where: {
          orderId: orderId
        }
      });
      
      if (allBaziOrders.length > 0) {
        console.log('\n🔍 找到孤立的八字订单记录:');
        allBaziOrders.forEach((baziOrder, index) => {
          console.log(`  记录${index + 1}:`);
          console.log(`    ID: ${baziOrder.id}`);
          console.log(`    订单ID: ${baziOrder.orderId}`);
          console.log(`    报告状态: ${baziOrder.reportStatus}`);
        });
      }
    }
    
  } catch (error) {
    console.error('检查过程中出错:', error);
  }
}

checkOrderReportStatus();