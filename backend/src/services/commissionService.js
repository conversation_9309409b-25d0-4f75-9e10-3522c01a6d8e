import { Product, User, Order, Commission, SystemConfig } from '../models/index.js';
import sequelize from '../config/database.js';

/**
 * 计算特定产品和代理商的佣金
 * @param {string} productId - 产品ID
 * @param {string} agentId - 代理商ID
 * @returns {Object} - 佣金计算结果
 */
export const calculateCommissions = async (productId, agentId) => {
  const [product, agent] = await Promise.all([
    Product.findByPk(productId),
    User.findByPk(agentId, {
      include: [{ model: User, as: 'parentAgent' }]
    })
  ]);

  if (!product || !agent || agent.status !== 'approved') {
    throw new Error('无效的产品或代理商');
  }

  // 从系统配置中获取平台佣金比例
  const adminCommissionRate = await SystemConfig.getValue('admin_commission_rate', 20) / 100;

  const amount = product.price;
  let agentCommission = 0;
  let parentAgentCommission = 0;

  if (agent.level === 1) {
    // 一级代理商佣金由管理员设置
    agentCommission = amount * (agent.commissionRate / 100);
  } else if (agent.level === 2 && agent.parentAgent) {
    // 二级代理商佣金由上级代理商设置
    const totalCommissionRate = agent.parentAgent.commissionRate / 100;
    const agentCommissionRate = agent.commissionRate / 100;
    
    const totalCommission = amount * totalCommissionRate;
    agentCommission = amount * agentCommissionRate;
    parentAgentCommission = totalCommission - agentCommission;
  }

  const platformCommission = amount - agentCommission - parentAgentCommission;

  // 验证最小提现金额
  const minWithdrawalAmount = await SystemConfig.getValue('min_withdrawal_amount', 1);
  if (agentCommission < minWithdrawalAmount || (parentAgentCommission > 0 && parentAgentCommission < minWithdrawalAmount)) {
    throw new Error('佣金金额低于最小提现阈值');
  }

  return {
    amount,
    agentCommission,
    parentAgentCommission,
    platformCommission
  };
};

/**
 * 为已支付订单创建佣金记录
 * @param {string} orderId - 订单ID
 * @returns {Promise<Array>} - 创建的佣金记录
 */
export const createCommissionsForOrder = async (orderId) => {
  // 使用事务确保数据一致性
  const transaction = await sequelize.transaction();

  try {
    const order = await Order.findByPk(orderId, {
      include: [
        { model: User, as: 'orderAgent' },
        { model: User, as: 'orderParentAgent' },
        { model: Product, as: 'product' }
      ],
      transaction
    });

    if (!order || (order.status !== 'paid' && order.status !== 'completed')) {
      throw new Error('订单不存在或未支付');
    }

    // 检查是否已经创建了佣金记录
    const existingCommissions = await Commission.count({
      where: { orderId },
      transaction
    });

    if (existingCommissions > 0) {
      await transaction.rollback();
      return { message: '该订单已创建佣金记录' };
    }

    const commissions = [];

    // 创建代理商佣金记录
    if (order.agentCommission > 0) {
      const agentCommission = await Commission.create({
        orderId: order.id,
        agentId: order.agentId,
        amount: order.agentCommission,
        status: 'pending',
        description: `订单 ${order.id} 的佣金`
      }, { transaction });
      
      commissions.push(agentCommission);
      
      // 更新代理商总佣金
      await User.increment(
        { totalCommission: order.agentCommission, monthlyCommission: order.agentCommission },
        { where: { id: order.agentId }, transaction }
      );
    }

    // 创建上级代理商佣金记录
    if (order.parentAgentCommission > 0 && order.parentAgentId) {
      const parentCommission = await Commission.create({
        orderId: order.id,
        agentId: order.parentAgentId,
        amount: order.parentAgentCommission,
        status: 'pending',
        description: `下级代理商 ${order.agentId} 的订单 ${order.id} 佣金`
      }, { transaction });
      
      commissions.push(parentCommission);
      
      // 更新上级代理商总佣金
      await User.increment(
        { totalCommission: order.parentAgentCommission, monthlyCommission: order.parentAgentCommission },
        { where: { id: order.parentAgentId }, transaction }
      );
    }

    await transaction.commit();
    return commissions;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * 检查并修复用户佣金统计与实际佣金记录的一致性
 * @returns {Promise<Object>} - 修复结果
 */
export const fixCommissionConsistency = async () => {
  const transaction = await sequelize.transaction();
  
  try {
    // 获取所有代理商
    const agents = await User.findAll({
      where: {
        role: 'agent',
        status: 'approved'
      },
      transaction
    });
    
    const results = {
      fixed: 0,
      users: []
    };
    
    // 循环检查每个代理商
    for (const agent of agents) {
      // 计算实际佣金总额
      const totalCommission = await Commission.sum('amount', {
        where: {
          agentId: agent.id,
          status: { [sequelize.Op.in]: ['pending', 'available', 'withdrawn'] }
        },
        transaction
      }) || 0;
      
      // 计算本月佣金
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      
      const monthlyCommission = await Commission.sum('amount', {
        where: {
          agentId: agent.id,
          status: { [sequelize.Op.in]: ['pending', 'available', 'withdrawn'] },
          createdAt: { [sequelize.Op.gte]: startOfMonth }
        },
        transaction
      }) || 0;
      
      // 如果数据不一致，更新用户数据
      if (totalCommission !== agent.totalCommission || monthlyCommission !== agent.monthlyCommission) {
        await agent.update({
          totalCommission,
          monthlyCommission
        }, { transaction });
        
        results.fixed++;
        results.users.push({
          id: agent.id,
          username: agent.username,
          before: {
            totalCommission: agent.totalCommission,
            monthlyCommission: agent.monthlyCommission
          },
          after: {
            totalCommission,
            monthlyCommission
          }
        });
      }
    }
    
    await transaction.commit();
    return results;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};