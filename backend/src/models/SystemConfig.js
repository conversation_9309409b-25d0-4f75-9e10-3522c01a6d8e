 import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

/**
 * 系统配置模型
 * 用于存储系统全局配置项，包括默认佣金比例、提现设置等
 */
const SystemConfig = sequelize.define('SystemConfig', {
  // 配置键名
  key: {
    type: DataTypes.STRING,
    primaryKey: true,
    allowNull: false
  },
  
  // 配置值（字符串形式，可存储JSON字符串）
  value: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  
  // 配置项描述
  description: {
    type: DataTypes.STRING,
    allowNull: true
  },
  
  // 配置分组，用于前端展示和管理
  group: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'general'
  }
}, {
  tableName: 'SystemConfigs',
  timestamps: true
});

/**
 * 获取配置值，支持数字、布尔值、对象等类型自动转换
 * @param {string} key - 配置键名
 * @param {any} defaultValue - 默认值
 * @returns {Promise<any>} - 转换后的配置值
 */
SystemConfig.getValue = async function(key, defaultValue = null) {
  const config = await this.findByPk(key);
  
  if (!config) return defaultValue;
  
  try {
    // 尝试将值解析为JSON
    return JSON.parse(config.value);
  } catch (e) {
    // 如果不是有效的JSON，则直接返回原始值
    return config.value;
  }
};

/**
 * 设置配置值，自动处理不同类型的值
 * @param {string} key - 配置键名
 * @param {any} value - 配置值
 * @param {string} description - 配置描述
 * @param {string} group - 配置分组
 * @returns {Promise<SystemConfig>} - 更新后的配置对象
 */
SystemConfig.setValue = async function(key, value, description = null, group = 'general') {
  // 对非字符串类型的值进行序列化
  const valueToStore = typeof value === 'string' ? value : JSON.stringify(value);
  
  const [config, created] = await this.findOrCreate({
    where: { key },
    defaults: {
      value: valueToStore,
      description,
      group
    }
  });
  
  if (!created) {
    await config.update({
      value: valueToStore,
      description: description || config.description,
      group: group || config.group
    });
  }
  
  return config;
};

export default SystemConfig;