import { create } from 'zustand';
import { FormData, LinkConfig, Trade } from '@/types';
import { apiService } from '@/services/api';
import { getQueryString } from '@/utils';

interface AppState {
  // 元数据
  meta: {
    query: Record<string, string>;
    linkId: string | null;
    serviceImg: string | null;
  };
  
  // 链接配置
  link: LinkConfig | null;
  linkOwnerConfig: Record<string, any>;
  linkBossConfig: Record<string, any>;
  
  // 表单数据
  formData: FormData;
  
  // 订单相关
  currentTrade: Trade | null;
  trades: Trade[];
  
  // 报告数据
  report: any;
  
  // 加载状态
  loading: boolean;
  
  // Actions
  setMeta: (meta: Partial<AppState['meta']>) => void;
  setFormData: (data: Partial<FormData>) => void;
  getLink: () => Promise<void>;
  createTrade: () => Promise<Trade>;
  getTrade: (tradeid: string) => Promise<void>;
  getReport: (tradeid: string) => Promise<void>;
  gotoTrade: (tradeid: string) => void;
  gotoOrder: (tradeid: string) => void;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  meta: {
    query: {},
    linkId: null,
    serviceImg: null,
  },
  
  link: null,
  linkOwnerConfig: { showService: false },
  linkBossConfig: {},
  
  formData: {
    name: '',
    sex: '1',
    birthday: '',
    address: '',
    yearType: '2',
    date: {
      yearType: 2,
      currentDate: [],
      currentTime: [],
    },
    relation: '本人',
  },
  
  currentTrade: null,
  trades: [],
  report: null,
  loading: false,
  
  // Actions
  setMeta: (meta) => set((state) => ({
    meta: { ...state.meta, ...meta }
  })),
  
  setFormData: (data) => set((state) => ({
    formData: { ...state.formData, ...data }
  })),
  
  getLink: async () => {
    const { meta } = get();
    const linkId = meta.linkId || localStorage.getItem('linkId');
    
    if (!linkId) return;
    
    try {
      set({ loading: true });
      const response = await apiService.getLink(linkId);
      
      if (response.code === 0) {
        const link = response.data;
        set({
          link,
          linkOwnerConfig: link.oc || {},
          linkBossConfig: link.bc || {},
        });
      }
    } catch (error) {
      console.error('获取链接配置失败:', error);
    } finally {
      set({ loading: false });
    }
  },
  
  createTrade: async () => {
    const { formData, meta } = get();
    
    try {
      set({ loading: true });
      
      // 构造提交数据
      const submitData = {
        ...formData,
        app: 200, // fantasy app
        linkId: meta.linkId,
        male: formData.sex === '1' ? '男' : '女',
        age: new Date().getFullYear() - formData.date.currentDate[0],
      };
      
      const response = await apiService.createTrade(submitData);
      
      if (response.code === 0) {
        const trade = response.data;
        set({ currentTrade: trade });
        
        // 缓存订单ID
        const cachedTrades = JSON.parse(localStorage.getItem('trades') || '[]');
        cachedTrades.unshift(trade.tradeid);
        localStorage.setItem('trades', JSON.stringify(cachedTrades.slice(0, 10)));
        
        return trade;
      }
      
      throw new Error(response.message);
    } catch (error) {
      console.error('创建订单失败:', error);
      throw error;
    } finally {
      set({ loading: false });
    }
  },
  
  getTrade: async (tradeid: string) => {
    try {
      set({ loading: true });
      const response = await apiService.getTrade({ tradeid, app: 200 });
      
      if (response.code === 0) {
        set({ currentTrade: response.data });
      }
    } catch (error) {
      console.error('获取订单失败:', error);
    } finally {
      set({ loading: false });
    }
  },
  
  getReport: async (tradeid: string) => {
    try {
      set({ loading: true });
      const response = await apiService.getReport({ tradeid });
      
      if (response.code === 0) {
        set({ report: response.data });
      }
    } catch (error) {
      console.error('获取报告失败:', error);
    } finally {
      set({ loading: false });
    }
  },
  
  gotoTrade: (tradeid: string) => {
    // 跳转到订单处理页面
    window.location.href = `/trade/${tradeid}`;
  },
  
  gotoOrder: (tradeid: string) => {
    // 跳转到支付页面
    window.location.href = `/order/${tradeid}`;
  },
}));

// 初始化应用状态
export const initializeApp = () => {
  const store = useAppStore.getState();
  const params = getQueryString();
  
  store.setMeta({
    query: params,
    linkId: params.a || localStorage.getItem('linkId'),
  });
  
  if (store.meta.linkId) {
    localStorage.setItem('linkId', store.meta.linkId);
    store.getLink();
  }
};
