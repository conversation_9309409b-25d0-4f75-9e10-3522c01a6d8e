import axios, { AxiosResponse } from 'axios';
import { API_BASE_URL } from '@/config';
import { ApiResponse, FormData, Trade, LinkConfig } from '@/types';

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response;
    if (data.code === 0) {
      return response;
    }
    throw new Error(data.message || '请求失败');
  },
  (error) => {
    const message = error.response?.data?.message || error.message || '网络错误';
    throw new Error(message);
  }
);

// API接口
export const apiService = {
  // 获取链接配置
  getLink: (id: string): Promise<ApiResponse<LinkConfig>> =>
    api.get(`/merge/link/${id}`).then(res => res.data),

  // 创建订单
  createTrade: (data: FormData): Promise<ApiResponse<Trade>> =>
    api.post('/merge/trade', data).then(res => res.data),

  // 获取订单详情
  getTrade: (params: { tradeid: string; app?: number }): Promise<ApiResponse<Trade>> =>
    api.get('/merge/trade', { params }).then(res => res.data),

  // 获取订单列表
  getTrades: (params: { trades: string[] }): Promise<ApiResponse<Trade[]>> =>
    api.get('/merge/trades', { params }).then(res => res.data),

  // 获取报告
  getReport: (params: { tradeid: string }): Promise<ApiResponse<any>> =>
    api.get('/merge/getReport', { params }).then(res => res.data),

  // 获取示例报告
  getDemoReport: (): Promise<ApiResponse<any>> =>
    api.get('/merge/demoReport').then(res => res.data),

  // 预支付
  prepay: (params: { tradeid: string; payType?: string }): Promise<ApiResponse<any>> =>
    api.get('/pay/prepay', { params }).then(res => res.data),

  // 检查支付状态
  isPaid: (params: { tradeid: string; payType?: string }): Promise<ApiResponse<boolean>> =>
    api.get('/pay/isPaid', { params }).then(res => res.data),
};

export default api;
