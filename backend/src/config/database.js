import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 根据环境加载相应的配置文件
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.develop';
const envPath = path.resolve(process.cwd(), envFile);

// 使用dotenv加载环境变量
dotenv.config({ path: envPath });

// 打印数据库配置信息，方便调试
console.log('环境模式:', process.env.NODE_ENV);
console.log('数据库配置信息:', {
  name: process.env.DB_NAME,
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  port: process.env.DB_PORT
});

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    dialectOptions: {
      charset: 'utf8mb4',
      timezone: '+08:00', // 设置时区为东八区
      dateStrings: true
    },
    timezone: '+08:00', // 设置时区为东八区
    define: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci'
    },
    logging: true // 开启 SQL 日志以便调试
  }
);

export default sequelize;