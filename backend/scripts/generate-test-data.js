/**
 * 测试数据生成脚本
 * 用于生成订单和佣金数据，测试佣金计算和提现功能
 * 
 * 使用方法：
 * node scripts/generate-test-data.js
 */

import '../src/config/dotenv.js';
import sequelize from '../src/config/database.js';
import User from '../src/models/User.js';
import Product from '../src/models/Product.js';
import Order from '../src/models/Order.js';
import Commission from '../src/models/Commission.js';
import { generateOrderNumber } from '../src/utils/orderUtils.js';

// 随机生成一个订单号
const generateRandomOrderId = () => {
  return generateOrderNumber();
};

// 随机生成一个金额（100-1000之间）
const generateRandomAmount = (min = 100, max = 1000) => {
  return parseFloat((Math.random() * (max - min) + min).toFixed(2));
};

// 随机生成一个日期（过去30天内）
const generateRandomDate = (daysAgo = 30) => {
  const date = new Date();
  date.setDate(date.getDate() - Math.floor(Math.random() * daysAgo));
  return date;
};

// 随机生成一个手机号
const generateRandomPhone = () => {
  return `1${Math.floor(Math.random() * 9) + 1}${Array(9).fill(0).map(() => Math.floor(Math.random() * 10)).join('')}`;
};

// 随机生成一个客户名
const generateRandomName = () => {
  const surnames = ['张', '王', '李', '赵', '刘', '陈', '杨', '黄', '周', '吴'];
  const names = ['明', '华', '强', '伟', '芳', '娜', '静', '秀', '丽', '军', '杰', '涛', '敏', '燕'];
  return surnames[Math.floor(Math.random() * surnames.length)] + names[Math.floor(Math.random() * names.length)];
};

// 主函数
async function generateTestData() {
  try {
    console.log('开始生成测试数据...');

    // 1. 查找agent1和agent2用户
    const agent1 = await User.findOne({ where: { username: 'agent1' } });
    const agent2 = await User.findOne({ where: { username: 'agent2' } });

    if (!agent1 || !agent2) {
      console.error('找不到agent1或agent2用户，请先创建这些用户');
      return;
    }

    console.log(`找到agent1(ID: ${agent1.id})和agent2(ID: ${agent2.id})`);

    // 2. 查找可用的产品
    const products = await Product.findAll({ where: { status: 'active' } });

    if (products.length === 0) {
      console.error('找不到可用的产品，请先创建产品');
      return;
    }

    console.log(`找到${products.length}个可用产品`);

    // 3. 为每个代理生成订单和佣金
    const agentsToProcess = [agent1, agent2];
    const ordersByAgent = {};

    for (const agent of agentsToProcess) {
      console.log(`\n为代理 ${agent.username} 生成订单和佣金...`);
      ordersByAgent[agent.id] = [];

      // 为每个代理生成5-10个订单
      const orderCount = Math.floor(Math.random() * 6) + 5;
      
      for (let i = 0; i < orderCount; i++) {
        // 随机选择一个产品
        const product = products[Math.floor(Math.random() * products.length)];
        const orderAmount = generateRandomAmount();
        
        // 计算佣金
        const agentCommissionRate = agent.commissionRate / 100; // 转换为小数
        const agentCommission = parseFloat((orderAmount * agentCommissionRate).toFixed(2));
        
        let parentAgentCommission = 0;
        if (agent.parentAgentId) {
          const parentAgent = await User.findByPk(agent.parentAgentId);
          if (parentAgent) {
            const parentCommissionRate = (parentAgent.commissionRate - agent.commissionRate) / 100;
            parentAgentCommission = parseFloat((orderAmount * parentCommissionRate).toFixed(2));
          }
        }
        
        // 创建订单
        const order = await Order.create({
          id: generateRandomOrderId(),
          amount: orderAmount,
          status: 'paid', // 已支付状态
          customerName: generateRandomName(),
          customerPhone: generateRandomPhone(),
          productId: product.id,
          agentId: agent.id,
          parentAgentId: agent.parentAgentId,
          agentCommission: agentCommission,
          parentAgentCommission: parentAgentCommission,
          paidAt: generateRandomDate()
        });
        
        console.log(`创建订单: ${order.id}, 金额: ${orderAmount}, 佣金: ${agentCommission}`);
        ordersByAgent[agent.id].push(order);
        
        // 创建代理佣金记录
        const commission = await Commission.create({
          amount: agentCommission,
          status: Math.random() > 0.3 ? 'available' : 'pending', // 70%为可提现状态
          orderId: order.id,
          agentId: agent.id
        });
        
        console.log(`创建佣金记录: ID ${commission.id}, 金额: ${agentCommission}, 状态: ${commission.status}`);
        
        // 如果有上级代理，也创建上级代理的佣金记录
        if (agent.parentAgentId && parentAgentCommission > 0) {
          const parentCommission = await Commission.create({
            amount: parentAgentCommission,
            status: Math.random() > 0.3 ? 'available' : 'pending',
            orderId: order.id,
            agentId: agent.parentAgentId
          });
          
          console.log(`创建上级代理佣金记录: ID ${parentCommission.id}, 金额: ${parentAgentCommission}, 状态: ${parentCommission.status}`);
        }
      }
    }

    // 4. 生成统计信息
    for (const agent of agentsToProcess) {
      // 查询该代理的所有佣金
      const commissions = await Commission.findAll({
        where: { agentId: agent.id }
      });
      
      // 计算统计数据
      const totalCommission = commissions.reduce((sum, commission) => sum + Number(commission.amount), 0);
      const availableCommission = commissions
        .filter(commission => commission.status === 'available')
        .reduce((sum, commission) => sum + Number(commission.amount), 0);
      const pendingCommission = commissions
        .filter(commission => commission.status === 'pending')
        .reduce((sum, commission) => sum + Number(commission.amount), 0);
      
      console.log(`\n代理 ${agent.username} 佣金统计:`);
      console.log(`- 总佣金: ¥${totalCommission.toFixed(2)}`);
      console.log(`- 可提现佣金: ¥${availableCommission.toFixed(2)}`);
      console.log(`- 待结算佣金: ¥${pendingCommission.toFixed(2)}`);
      console.log(`- 订单数量: ${ordersByAgent[agent.id].length}`);
    }

    console.log('\n测试数据生成完成！');

  } catch (error) {
    console.error('生成测试数据时出错:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行主函数
generateTestData();
