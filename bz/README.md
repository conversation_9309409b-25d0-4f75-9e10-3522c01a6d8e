# 八字查询页面 - React + TailwindCSS 版本

这是一个完全移植自 Vue.js 版本的八字查询页面，使用 React + TailwindCSS 重新实现，保证前端页面外观和功能完全一致。

## 项目特性

- ✅ **完全移植**: 从 Vue.js 版本完整移植所有功能和样式
- ✅ **像素级还原**: 使用 TailwindCSS 精确还原原版样式
- ✅ **现代技术栈**: React 18 + TypeScript + TailwindCSS + Vite
- ✅ **状态管理**: 使用 Zustand 替代 Pinia
- ✅ **动画效果**: 使用 Framer Motion 实现罗盘旋转等动画
- ✅ **响应式设计**: 移动端优先的响应式布局
- ✅ **API 集成**: 完整的后端 API 调用功能

## 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: TailwindCSS
- **状态管理**: Zustand
- **动画库**: Framer Motion
- **HTTP 客户端**: Axios
- **表单处理**: React Hook Form + Zod
- **图标库**: Lucide React
- **日期处理**: date-fns

## 项目结构

```
bz/
├── public/                 # 静态资源
│   └── assets/
│       └── images/        # 图片资源（从原项目复制）
├── src/
│   ├── components/        # 组件
│   │   ├── ui/           # 基础 UI 组件
│   │   ├── FormFields.tsx # 表单字段组件
│   │   ├── TopSection.tsx # 顶部罗盘动画
│   │   ├── FormSection.tsx # 表单区域
│   │   ├── ImageBlock.tsx # 示例图片区域
│   │   ├── DateTimePicker.tsx # 日期时间选择器
│   │   └── AreaPicker.tsx # 地区选择器
│   ├── pages/            # 页面组件
│   │   └── Home.tsx      # 主页
│   ├── services/         # API 服务
│   │   └── api.ts        # API 接口定义
│   ├── store/            # 状态管理
│   │   └── index.ts      # Zustand store
│   ├── types/            # TypeScript 类型定义
│   ├── utils/            # 工具函数
│   ├── config/           # 配置文件
│   └── index.css         # 全局样式
├── package.json
├── vite.config.ts
├── tailwind.config.js
└── tsconfig.json
```

## 快速开始

### 1. 安装依赖

```bash
cd bz
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:3000 启动（如果端口被占用会自动选择其他端口）。

### 3. 构建生产版本

```bash
npm run build
```

## 功能说明

### 主要功能

1. **姓名输入**: 支持中文姓名验证
2. **性别选择**: 男/女单选
3. **生日选择**: 支持公历/农历选择，包含年月日时分
4. **地区选择**: 省市区三级联动选择
5. **关系选择**: 根据配置显示关系选项（本人、孩子等）
6. **表单验证**: 完整的前端验证逻辑
7. **订单创建**: 提交表单创建八字分析订单
8. **动画效果**: 顶部罗盘旋转动画

### API 接口

项目包含完整的 API 服务层，支持：

- 获取链接配置
- 创建订单
- 获取订单详情
- 获取分析报告
- 支付相关接口

### 样式特性

- 完全还原原版 Vue.js 项目的视觉效果
- 使用 TailwindCSS 实现响应式设计
- 支持移动端优化
- 包含所有原版动画和交互效果

## 与原版对比

| 特性 | Vue.js 版本 | React 版本 |
|------|-------------|------------|
| 框架 | Vue 3 + Composition API | React 18 + Hooks |
| 样式 | Stylus + Vant UI | TailwindCSS + 自定义组件 |
| 状态管理 | Pinia | Zustand |
| 构建工具 | Vite | Vite |
| 动画 | anime.js | Framer Motion |
| 表单 | 自定义 | React Hook Form |

## 开发说明

### 环境要求

- Node.js >= 16
- npm >= 8

### 开发模式

项目支持热重载，修改代码后会自动刷新页面。

### 生产部署

构建后的文件在 `dist` 目录，可以部署到任何静态文件服务器。

## 注意事项

1. **图片资源**: 所有图片资源已从原项目复制到 `public/assets/images/` 目录
2. **API 配置**: 默认连接到 `http://localhost:9600`，可在 `src/config/index.ts` 中修改
3. **移动端适配**: 项目采用移动端优先设计，在桌面端也有良好表现
4. **浏览器兼容**: 支持现代浏览器，建议使用 Chrome、Firefox、Safari 最新版本

---

# 🚀 移植指南 - 如何将此项目移植到其他React项目

## 📋 移植清单

### 1. 依赖包安装

在目标项目中安装以下依赖：

```bash
# 核心依赖
npm install react react-dom react-router-dom
npm install zustand axios clsx date-fns
npm install react-hook-form @hookform/resolvers zod
npm install lucide-react

# 开发依赖
npm install -D @types/react @types/react-dom
npm install -D typescript @typescript-eslint/eslint-plugin @typescript-eslint/parser
npm install -D tailwindcss postcss autoprefixer
npm install -D vite @vitejs/plugin-react

# 可选：动画效果
npm install framer-motion
```

### 2. 配置文件

#### 2.1 TailwindCSS配置 (`tailwind.config.js`)

```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#b87c3d',
        'primary-light': '#ffa648',
        'bg-light': '#FEF4E0',
        'color-border': '#E1B467',
        'color-text': '#3f2410',
        'color-light': '#b87c3d',
        'color-light1': '#FEF1C3',
        'color-red': '#fb5757',
        'color-warning': '#ffa648',
        'color-desc': 'rgba(63,36,16,0.7)',
        'color-highlight': '#fe8d59',
        'bg-gray': '#EDEDED',
        'bg-color': '#AD7E47',
        'bg-body': '#ecedee',
      },
      backgroundImage: {
        'gradient-main': 'linear-gradient(141deg,#e87947 6%,#eb8833 52%,#e9543f 100%)',
      },
      fontFamily: {
        sans: ['PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Arial', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
```

#### 2.2 Vite配置 (`vite.config.ts`)

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    host: true,
  },
})
```

#### 2.3 TypeScript配置 (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
```

### 3. 文件结构复制

需要复制以下目录和文件到目标项目：

```
src/
├── components/
│   ├── ui/
│   │   ├── SimpleModal.tsx      # ✅ 简单模态框组件
│   │   ├── Field.tsx           # ✅ 表单字段组件
│   │   ├── Radio.tsx           # ✅ 单选按钮组件
│   │   └── Tag.tsx             # ✅ 标签组件
│   ├── CalendarTypeSelector.tsx # ✅ 公历/农历选择器
│   ├── DateTimeSelector.tsx     # ✅ 日期时间选择器
│   ├── AreaSelector.tsx         # ✅ 地区选择器
│   ├── FormFields.tsx           # ✅ 表单字段集合
│   └── FormSection2.tsx         # ✅ 主表单组件
├── store/
│   └── index.ts                 # ✅ Zustand状态管理
├── types/
│   └── index.ts                 # ✅ TypeScript类型定义
├── services/
│   └── api.ts                   # ✅ API服务
├── utils/
│   └── index.ts                 # ✅ 工具函数
├── config/
│   └── index.ts                 # ✅ 配置文件
└── index.css                    # ✅ 样式文件（关键Modal样式）
```

### 4. 核心组件接口

#### 4.1 SimpleModal - 基础模态框
```typescript
interface SimpleModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
}
```

#### 4.2 CalendarTypeSelector - 公历/农历选择器
```typescript
interface CalendarTypeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (type: 'solar' | 'lunar') => void;
}
```

#### 4.3 DateTimeSelector - 日期时间选择器
```typescript
interface DateTimeSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: {
    year: number;
    month: number;
    day: number;
    hour: number;
    minute: number;
    calendarType: 'solar' | 'lunar';
  }) => void;
  calendarType: 'solar' | 'lunar';
}
```

#### 4.4 AreaSelector - 地区选择器
```typescript
interface AreaSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: {
    province: string;
    city: string;
    district?: string
  }) => void;
}
```

### 5. 使用示例

#### 5.1 基础表单使用

```tsx
import React from 'react';
import { FormFields } from './components/FormFields';
import { useAppStore } from './store';

export const BaziForm: React.FC = () => {
  const { formData, setFormData, createTrade } = useAppStore();

  const handleSubmit = async () => {
    try {
      const trade = await createTrade();
      console.log('订单创建成功:', trade);
      // 处理成功逻辑
    } catch (error) {
      console.error('提交失败:', error);
      // 处理错误逻辑
    }
  };

  return (
    <div className="bazi-form">
      <FormFields />
      <button
        onClick={handleSubmit}
        className="w-full bg-primary text-white py-3 rounded-lg"
      >
        立即解析八字命格
      </button>
    </div>
  );
};
```

#### 5.2 独立组件使用

```tsx
import React, { useState } from 'react';
import { CalendarTypeSelector } from './components/CalendarTypeSelector';
import { DateTimeSelector } from './components/DateTimeSelector';
import { AreaSelector } from './components/AreaSelector';

export const CustomForm: React.FC = () => {
  const [showCalendarType, setShowCalendarType] = useState(false);
  const [showDateTime, setShowDateTime] = useState(false);
  const [showArea, setShowArea] = useState(false);
  const [calendarType, setCalendarType] = useState<'solar' | 'lunar'>('solar');

  return (
    <div>
      {/* 日期选择 */}
      <button onClick={() => setShowCalendarType(true)}>
        选择生日
      </button>

      <CalendarTypeSelector
        isOpen={showCalendarType}
        onClose={() => setShowCalendarType(false)}
        onSelect={(type) => {
          setCalendarType(type);
          setShowCalendarType(false);
          setShowDateTime(true);
        }}
      />

      <DateTimeSelector
        isOpen={showDateTime}
        onClose={() => setShowDateTime(false)}
        onConfirm={(data) => {
          console.log('选择的日期时间:', data);
          setShowDateTime(false);
        }}
        calendarType={calendarType}
      />

      {/* 地区选择 */}
      <button onClick={() => setShowArea(true)}>
        选择地区
      </button>

      <AreaSelector
        isOpen={showArea}
        onClose={() => setShowArea(false)}
        onConfirm={(data) => {
          console.log('选择的地区:', data);
          setShowArea(false);
        }}
      />
    </div>
  );
};
```

### 6. 关键样式文件

将以下CSS添加到您的主样式文件中：

```css
/* 新的简单Modal样式 */
.simple-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.simple-modal-content {
  background: white;
  border-radius: 12px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* 公历农历选择器样式 */
.calendar-type-selector {
  min-width: 300px;
}

.calendar-type-button {
  display: block;
  width: 100%;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  text-align: left;
  transition: all 0.2s;
}

.calendar-type-button:hover {
  border-color: #1989fa;
  background-color: #f7f8fa;
}

/* 日期时间选择器样式 */
.datetime-selector {
  min-width: 350px;
}

.datetime-selectors {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.selector-group {
  display: flex;
  flex-direction: column;
}

.selector-group label {
  font-size: 14px;
  color: #646566;
  margin-bottom: 8px;
  font-weight: 500;
}

.selector-group select {
  padding: 12px;
  border: 1px solid #ebedf0;
  border-radius: 6px;
  font-size: 16px;
  background: white;
  cursor: pointer;
}

.selector-group select:focus {
  outline: none;
  border-color: #1989fa;
}

/* 地区选择器样式 */
.area-selector {
  min-width: 350px;
}

.area-selectors {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

/* 按钮样式 */
.cancel-button {
  width: 100%;
  padding: 14px;
  background: #f7f8fa;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  color: #323233;
  cursor: pointer;
  margin-top: 8px;
}

.cancel-button:hover {
  background: #ebedf0;
}

.confirm-button {
  background: #1989fa;
  color: white;
}

.confirm-button:hover {
  background: #1976d2;
}

.datetime-actions,
.area-actions {
  display: flex;
  gap: 12px;
}

.datetime-actions button,
.area-actions button {
  flex: 1;
  padding: 14px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
}
```

### 7. API服务配置

修改 `src/services/api.ts` 中的API基础URL：

```typescript
const API_BASE_URL = 'https://your-api-domain.com'; // 替换为您的API地址

export const apiService = {
  // 获取链接配置
  getLink: (linkId: string) =>
    request.get(`/api/link/${linkId}`),

  // 创建订单
  createTrade: (data: any) =>
    request.post('/api/trade', data),

  // 获取订单
  getTrade: (params: { tradeid: string; app: number }) =>
    request.get('/api/trade', { params }),

  // 获取报告
  getReport: (params: { tradeid: string }) =>
    request.get('/api/report', { params }),
};
```

### 8. 自定义配置

#### 8.1 修改主题颜色

在 `tailwind.config.js` 中修改颜色配置：

```javascript
colors: {
  primary: '#your-primary-color',
  'primary-light': '#your-light-color',
  // ... 其他颜色
}
```

#### 8.2 修改地区数据

在 `src/components/AreaSelector.tsx` 中修改 `areaData` 对象，添加您需要的省市区数据。

#### 8.3 修改表单字段

在 `src/types/index.ts` 中修改 `FormData` 接口，添加或删除字段。

### 9. 测试功能

移植完成后，测试以下功能：
- ✅ 姓名输入
- ✅ 性别选择
- ✅ 日期选择（公历/农历）
- ✅ 地区选择（省市区）
- ✅ 表单提交
- ✅ Modal弹窗功能

### 10. 注意事项

1. **API集成**：需要根据您的后端API调整服务接口
2. **图片资源**：复制 `public/assets` 目录中的图片资源
3. **路由配置**：如果使用React Router，需要配置相应的路由
4. **环境变量**：设置API地址等环境变量
5. **样式冲突**：检查是否与现有项目样式冲突

### 11. 启动项目

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build
```

---

## 许可证

本项目仅用于学习和演示目的。
