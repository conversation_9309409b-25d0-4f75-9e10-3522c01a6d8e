#!/bin/bash

# 本地部署脚本（用于本地测试）
echo "🚀 开始本地部署..."

# 检查是否在正确的目录
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 1. 停止现有服务
echo "📦 停止现有服务..."
pm2 stop commission-platform-api 2>/dev/null || echo "服务未运行"
pm2 delete commission-platform-api 2>/dev/null || echo "服务不存在"

# 2. 清理端口3005
echo "🔧 清理端口3005..."
lsof -ti:3005 | xargs kill -9 2>/dev/null || echo "端口3005未被占用"

# 3. 构建前端
echo "🏗️ 构建前端..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ 前端构建失败"
    exit 1
fi

# 4. 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
npm install --production
if [ $? -ne 0 ]; then
    echo "❌ 后端依赖安装失败"
    exit 1
fi

# 5. 启动后端服务
echo "🚀 启动后端服务..."
pm2 start start-production.js --name commission-platform-api
if [ $? -ne 0 ]; then
    echo "❌ 后端服务启动失败"
    exit 1
fi

# 6. 保存PM2配置
echo "💾 保存PM2配置..."
pm2 save

# 7. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 8. 测试本地API
echo "🧪 测试本地API..."
for i in {1..5}; do
    curl -f http://localhost:3005/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}' > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ 本地API测试成功"
        break
    else
        echo "⏳ 等待API启动... ($i/5)"
        sleep 5
    fi
done

# 9. 显示服务状态
echo "📊 服务状态:"
pm2 status commission-platform-api

echo ""
echo "🎉 本地部署完成！"
echo "🌐 本地前端: http://localhost:3000 (需要单独启动前端开发服务器)"
echo "🔌 本地API: http://localhost:3005"
echo "📊 服务状态: pm2 status"
echo "📝 查看日志: pm2 logs commission-platform-api"
echo "🔧 重启服务: pm2 restart commission-platform-api"
