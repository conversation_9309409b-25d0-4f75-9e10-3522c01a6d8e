#!/usr/bin/env node

// 测试支付跳转地址配置
import dotenv from 'dotenv';
import path from 'path';

// 加载开发环境配置
dotenv.config({ path: path.join(process.cwd(), 'backend/.env.develop') });

console.log('🧪 测试支付跳转地址配置');
console.log('=====================================');

// 模拟八字报告支付成功跳转逻辑
const orderId = '250711195627186931';
const frontendUrl = process.env.FRONTEND_URL || 'https://ye.bzcy.xyz';
const redirectUrl = `${frontendUrl}/bazi-report?orderId=${orderId}&status=success`;

console.log('📋 当前配置:');
console.log(`   NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
console.log(`   FRONTEND_URL: ${process.env.FRONTEND_URL || 'undefined'}`);
console.log('');
console.log('🎯 八字报告支付跳转地址:');
console.log(`   ${redirectUrl}`);
console.log('');

// 检查前端服务是否运行
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function checkFrontendService() {
  try {
    const url = new URL(frontendUrl);
    const port = url.port || (url.protocol === 'https:' ? '443' : '80');
    
    if (port !== '443' && port !== '80') {
      const { stdout } = await execAsync(`lsof -i :${port}`);
      const isRunning = stdout.includes('node') || stdout.includes('npm') || stdout.includes('vite');
      
      console.log('🔍 前端服务检查:');
      console.log(`   端口 ${port}: ${isRunning ? '✅ 运行中' : '❌ 未运行'}`);
      
      if (!isRunning) {
        console.log('⚠️  警告: 前端服务未在指定端口运行');
        console.log('   请确保前端服务已启动，或修改 FRONTEND_URL 配置');
      }
    } else {
      console.log('🌐 生产环境配置，跳过本地端口检查');
    }
  } catch (error) {
    console.log('🔍 前端服务检查: ❌ 检查失败');
  }
}

async function main() {
  await checkFrontendService();
  
  console.log('');
  console.log('💡 如需修改配置:');
  console.log('   编辑文件: backend/.env.develop');
  console.log('   修改配置: FRONTEND_URL=http://localhost:3000');
  console.log('   重启服务: pm2 restart commission-platform-api');
}

main().catch(console.error);
