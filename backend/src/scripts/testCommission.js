import { calculateCommissions } from '../utils/commissionUtils.js';

// 场景1：一级代理（无二级代理）
const result1 = calculateCommissions(100, 0.8, null);
console.log('场景1：一级代理（无二级代理）');
console.log('订单金额：100元，一级代理佣金比例：80%');
console.log('管理员佣金：', result1.adminCommission);  // 应该是 20 元
console.log('一级代理佣金：', result1.level1Commission);  // 应该是 80 元
console.log('二级代理佣金：', result1.level2Commission);  // 应该是 0 元

// 场景2：二级代理
const result2 = calculateCommissions(100, 0.8, 0.9);
console.log('\n场景2：二级代理');
console.log('订单金额：100元，一级代理佣金比例：80%，二级代理佣金比例：90%');
console.log('管理员佣金：', result2.adminCommission);  // 应该是 20 元
console.log('一级代理佣金：', result2.level1Commission);  // 应该是 8 元
console.log('二级代理佣金：', result2.level2Commission);  // 应该是 72 元

// // 验证佣金总和是否等于订单金额
console.log('\n验证佣金总和：');
console.log('场景1总和：', result1.adminCommission + result1.level1Commission + result1.level2Commission);  // 应该是 100 元
console.log('场景2总和：', result2.adminCommission + result2.level1Commission + result2.level2Commission);  // 应该是 100 元
