/**
 * 称骨算命服务
 * 根据出生年月日时计算骨重，并返回对应的称骨歌和解释
 */

class ChengguService {
  constructor() {
    // 年份对应的骨重（天干地支）
    this.yearWeights = {
      // 甲子年系列
      '1924': 1.2, '1984': 1.2, // 甲子
      '1925': 0.9, '1985': 0.9, // 乙丑
      '1926': 1.6, '1986': 1.6, // 丙寅
      '1927': 0.7, '1987': 0.7, // 丁卯
      '1928': 1.2, '1988': 1.2, // 戊辰
      '1929': 0.5, '1989': 0.5, // 己巳
      '1930': 0.9, '1990': 0.9, // 庚午
      '1931': 0.8, '1991': 0.8, // 辛未
      '1932': 0.7, '1992': 0.7, // 壬申
      '1933': 0.8, '1993': 0.8, // 癸酉
      '1934': 1.5, '1994': 1.5, // 甲戌
      '1935': 0.9, '1995': 0.9, // 乙亥
      '1936': 0.6, '1996': 0.6, // 丙子
      '1937': 0.7, '1997': 0.7, // 丁丑
      '1938': 1.9, '1998': 1.9, // 戊寅
      '1939': 1.9, '1999': 1.9, // 己卯
      '1940': 1.2, '2000': 1.2, // 庚辰
      '1941': 0.6, '2001': 0.6, // 辛巳
      '1942': 1.0, '2002': 1.0, // 壬午
      '1943': 0.7, '2003': 0.7, // 癸未
      '1944': 0.8, '2004': 0.8, // 甲申
      '1945': 1.5, '2005': 1.5, // 乙酉
      '1946': 0.6, '2006': 0.6, // 丙戌
      '1947': 1.6, '2007': 1.6, // 丁亥
      '1948': 1.0, '2008': 1.0, // 戊子
      '1949': 0.7, '2009': 0.7, // 己丑
      '1950': 0.9, '2010': 0.9, // 庚寅
      '1951': 1.2, '2011': 1.2, // 辛卯
      '1952': 1.0, '2012': 1.0, // 壬辰
      '1953': 0.7, '2013': 0.7, // 癸巳
      '1954': 1.5, '2014': 1.5, // 甲午
      '1955': 0.6, '2015': 0.6, // 乙未
      '1956': 0.5, '2016': 0.5, // 丙申
      '1957': 1.4, '2017': 1.4, // 丁酉
      '1958': 1.4, '2018': 1.4, // 戊戌
      '1959': 0.9, '2019': 0.9, // 己亥
      '1960': 0.7, '2020': 0.7, // 庚子
      '1961': 0.5, '2021': 0.5, // 辛丑
      '1962': 0.9, '2022': 0.9, // 壬寅
      '1963': 1.2, '2023': 1.2, // 癸卯
      '1964': 0.8, '2024': 0.8, // 甲辰
      '1965': 0.7, '2025': 0.7, // 乙巳
    };

    // 月份对应的骨重
    this.monthWeights = {
      '01': 0.6, '02': 0.7, '03': 1.8, '04': 0.9,
      '05': 0.5, '06': 1.6, '07': 0.9, '08': 1.5,
      '09': 1.8, '10': 0.8, '11': 0.9, '12': 0.5
    };

    // 日期对应的骨重
    this.dayWeights = {
      '01': 0.5, '02': 1.0, '03': 0.8, '04': 1.5, '05': 1.6,
      '06': 1.5, '07': 0.8, '08': 1.6, '09': 0.8, '10': 1.6,
      '11': 0.9, '12': 1.7, '13': 0.8, '14': 1.7, '15': 1.0,
      '16': 0.8, '17': 0.9, '18': 1.8, '19': 0.5, '20': 1.5,
      '21': 1.0, '22': 0.9, '23': 0.8, '24': 0.9, '25': 1.5,
      '26': 1.8, '27': 0.7, '28': 0.8, '29': 1.6, '30': 0.6, '31': 1.0
    };

    // 时辰对应的骨重
    this.hourWeights = {
      '23': 1.6, '00': 1.6, '01': 0.6, '02': 0.6, // 子时 23-01
      '03': 1.0, '04': 1.0, // 丑时 01-03
      '05': 0.7, '06': 0.7, // 寅时 03-05
      '07': 1.0, '08': 1.0, // 卯时 05-07
      '09': 0.9, '10': 0.9, // 辰时 07-09
      '11': 1.6, '12': 1.6, // 巳时 09-11
      '13': 1.0, '14': 1.0, // 午时 11-13
      '15': 0.8, '16': 0.8, // 未时 13-15
      '17': 0.8, '18': 0.8, // 申时 15-17
      '19': 0.9, '20': 0.9, // 酉时 17-19
      '21': 0.6, '22': 0.6  // 戌时 19-21, 亥时 21-23
    };

    // 称骨歌诀
    this.chengguSongs = {
      2.2: {
        weight: '二两二钱',
        destiny: '身寒骨冷苦伶仃，此命推来行乞人',
        song: '身寒骨冷苦伶仃，此命推来行乞人；\n劳劳碌碌无度日，中年打拱过平生。',
        interpretation: '此命劳碌一生，难得安逸，中年之后稍有改善，但总体运势平平。',
        detail: '此命为人性躁，能随机应变，常近贵人，祖业无成，骨肉六亲少义，一个自立家计，初限交来财运如霜雪，中限略可成家，大运突来能立家业，妻有克，小配无刑，子息三人，寿元七十七，死于七月中。'
      },
      2.3: {
        weight: '二两三钱',
        destiny: '此命推来骨轻轻，求谋做事事难成',
        song: '此命推来骨轻轻，求谋做事事难成；\n妻儿兄弟应难许，别处他乡作散人。',
        interpretation: '此命骨格轻薄，做事难成，亲情淡薄，适合离乡发展。',
        detail: '此命为人心灵性巧，做事细致，足智多谋，志气高昂，少年勤学有功名之格，青年欠利，腹中多谋，有礼有义，有才能，做事勤俭，一生福禄无亏，与人做事，反为不美，六亲骨肉可靠，交朋友，四海春风，中限光耀门庭，逢善不欺，逢恶不怕，事有始终，吉人天相，四海扬名，成家立业，安然到老，高楼大厦，妻宫硬配，子息三人，只一子送终，寿元七十七，春光中死。'
      },
      // 继续添加更多称骨歌诀...
      4.0: {
        weight: '四两',
        destiny: '平生衣禄是绵长，件件心中自主张',
        song: '平生衣禄是绵长，件件心中自主张；\n前面风霜多受过，后来必定享安康。',
        interpretation: '您生平比较平淡，您也无什么大理想，过一天算一天吧，不过总有苦尽甘来的那一天。',
        detail: '此命为人性格刚强，一生不受他人欺负，有多技能，祖业无靠，骨肉六亲无情，一个自立家计，初限交来财运如霜雪，中限略可成家，大运突来能立家业，妻有克，小配无刑，子息三人，寿元七十七，死于七月中。'
      }
    };
  }

  /**
   * 计算称骨重量
   * @param {string} year - 出生年份
   * @param {string} month - 出生月份 (01-12)
   * @param {string} day - 出生日期 (01-31)
   * @param {string} hour - 出生小时 (00-23)
   * @returns {Object} 称骨结果
   */
  calculateChenggu(year, month, day, hour) {
    try {
      // 获取各部分骨重
      const yearWeight = this.yearWeights[year] || 1.0;
      const monthWeight = this.monthWeights[month] || 0.8;
      const dayWeight = this.dayWeights[day] || 1.0;
      const hourWeight = this.hourWeights[hour] || 1.0;

      // 计算总骨重
      const totalWeight = yearWeight + monthWeight + dayWeight + hourWeight;
      
      // 四舍五入到一位小数
      const roundedWeight = Math.round(totalWeight * 10) / 10;

      // 获取对应的称骨歌诀
      const chengguInfo = this.getChengguInfo(roundedWeight);

      return {
        success: true,
        data: {
          称骨重量: chengguInfo.weight,
          称骨重量_命数: chengguInfo.destiny,
          称骨重量_歌诀: chengguInfo.song,
          称骨重量_歌诀释义: chengguInfo.interpretation,
          称骨重量_命运详解: chengguInfo.detail,
          详细计算: {
            年骨重: `${yearWeight}两`,
            月骨重: `${monthWeight}两`,
            日骨重: `${dayWeight}两`,
            时骨重: `${hourWeight}两`,
            总骨重: `${roundedWeight}两`
          }
        }
      };

    } catch (error) {
      console.error('称骨计算失败:', error);
      return {
        success: false,
        error: error.message,
        data: {
          称骨重量: null,
          称骨重量_命数: null,
          称骨重量_歌诀: null,
          称骨重量_歌诀释义: null,
          称骨重量_命运详解: null
        }
      };
    }
  }

  /**
   * 根据骨重获取称骨信息
   * @param {number} weight - 总骨重
   * @returns {Object} 称骨信息
   */
  getChengguInfo(weight) {
    // 查找最接近的称骨歌诀
    const availableWeights = Object.keys(this.chengguSongs).map(w => parseFloat(w)).sort((a, b) => a - b);
    
    let closestWeight = availableWeights[0];
    let minDiff = Math.abs(weight - closestWeight);
    
    for (const w of availableWeights) {
      const diff = Math.abs(weight - w);
      if (diff < minDiff) {
        minDiff = diff;
        closestWeight = w;
      }
    }

    return this.chengguSongs[closestWeight] || this.chengguSongs[4.0];
  }
}

export default new ChengguService();
