#!/usr/bin/env node

// 生产环境启动脚本
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 设置生产环境变量
process.env.NODE_ENV = 'production';
process.env.PORT = '3005';

console.log('🚀 启动生产环境后端服务...');
console.log('📍 端口: 3005');
console.log('🌍 环境: production');

// 启动应用
const child = spawn('node', [join(__dirname, 'src/index.js')], {
  stdio: 'inherit',
  env: process.env
});

child.on('error', (error) => {
  console.error('❌ 启动失败:', error);
  process.exit(1);
});

child.on('exit', (code) => {
  console.log(`🔚 进程退出，代码: ${code}`);
  process.exit(code);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到中断信号，正在关闭服务...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务...');
  child.kill('SIGTERM');
});
