import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>ield, GenderField, BirthdayField, AddressField, RelationField } from './FormFields';
import { useAppStore } from '@/store';
import { regChineseName } from '@/config';

export const FormSection: React.FC = () => {
  const { formData, createTrade, loading } = useAppStore();
  const [showConfirm, setShowConfirm] = useState(false);

  // 表单验证
  const validateForm = () => {
    if (!formData.name.trim()) {
      alert('请输入姓名');
      return false;
    }

    if (!regChineseName.test(formData.name)) {
      alert('姓名必须是2-8个汉字');
      return false;
    }

    if (!formData.birthday) {
      alert('请选择出生日期');
      return false;
    }

    if (!formData.address) {
      alert('请选择出生地区');
      return false;
    }

    return true;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) return;

    setShowConfirm(true);
  };

  // 确认提交
  const handleConfirm = async () => {
    try {
      const trade = await createTrade();
      setShowConfirm(false);
      
      // 跳转到支付页面
      window.location.href = `/order/${trade.tradeid}`;
    } catch (error) {
      alert(error instanceof Error ? error.message : '创建订单失败');
    }
  };

  return (
    <div className="main-form">
      {/* 表单标题 */}
      <div className="bg-title" />

      {/* 表单内容 */}
      <div className="inner">
        <div className="space-y-3">
          <NameField />
          <GenderField />
          <BirthdayField />
          <AddressField />
          <RelationField />
        </div>

        {/* 提交按钮 */}
        <motion.div
          className="submit"
          onClick={handleSubmit}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          style={{ cursor: loading ? 'not-allowed' : 'pointer' }}
        >
          {loading && (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
            </div>
          )}
        </motion.div>

        {/* 历史订单提示 */}
        <div className="history">
          查看历史订单请联系客服
        </div>
      </div>

      {/* 确认对话框 */}
      {showConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <motion.div
            className="bg-white rounded-lg p-6 max-w-sm w-full"
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
          >
            <h3 className="text-lg font-semibold mb-4 text-center">确认信息</h3>
            
            <div className="space-y-2 mb-6 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">姓名：</span>
                <span>{formData.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">性别：</span>
                <span>{formData.sex === '1' ? '男' : '女'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">生日：</span>
                <span>{formData.birthday}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">地区：</span>
                <span>{formData.address}</span>
              </div>
              {formData.relation && (
                <div className="flex justify-between">
                  <span className="text-gray-600">关系：</span>
                  <span>{formData.relation}</span>
                </div>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700"
                onClick={() => setShowConfirm(false)}
              >
                取消
              </button>
              <button
                className="flex-1 py-2 px-4 bg-primary text-white rounded-lg"
                onClick={handleConfirm}
                disabled={loading}
              >
                {loading ? '提交中...' : '确认提交'}
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};
