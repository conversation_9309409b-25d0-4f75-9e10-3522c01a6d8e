#!/bin/bash

echo "🔍 诊断远程服务器问题..."

PROJECT_PATH="/data/commission-platform8z"
cd $PROJECT_PATH/backend

echo "📋 当前目录: $(pwd)"
echo "📁 目录内容:"
ls -la

echo ""
echo "🔍 检查Node.js版本:"
node --version
npm --version

echo ""
echo "🔍 检查PM2状态:"
pm2 status

echo ""
echo "🔍 检查PM2日志:"
pm2 logs commission-platform-api --lines 20 --nostream

echo ""
echo "🔍 检查端口占用:"
lsof -i :3005 || echo "端口3005未被占用"

echo ""
echo "🔍 检查环境变量文件:"
if [ -f ".env.production" ]; then
    echo "✅ .env.production 存在"
    echo "📄 内容预览:"
    head -10 .env.production
else
    echo "❌ .env.production 不存在"
fi

echo ""
echo "🔍 检查启动文件:"
if [ -f "start-production.js" ]; then
    echo "✅ start-production.js 存在"
else
    echo "❌ start-production.js 不存在"
fi

if [ -f "src/index.js" ]; then
    echo "✅ src/index.js 存在"
else
    echo "❌ src/index.js 不存在"
fi

echo ""
echo "🔧 尝试手动启动服务..."

# 停止现有服务
pm2 stop commission-platform-api 2>/dev/null || echo "服务未运行"
pm2 delete commission-platform-api 2>/dev/null || echo "服务不存在"

# 清理端口
lsof -ti:3005 | xargs kill -9 2>/dev/null || echo "端口3005未被占用"

# 设置环境变量
export NODE_ENV=production
export PORT=3005

echo ""
echo "🚀 尝试直接启动 src/index.js..."
timeout 10s node src/index.js &
DIRECT_PID=$!
sleep 5

if kill -0 $DIRECT_PID 2>/dev/null; then
    echo "✅ 直接启动成功"
    kill $DIRECT_PID
else
    echo "❌ 直接启动失败"
fi

echo ""
echo "🚀 尝试通过 start-production.js 启动..."
timeout 10s node start-production.js &
PROD_PID=$!
sleep 5

if kill -0 $PROD_PID 2>/dev/null; then
    echo "✅ start-production.js 启动成功"
    kill $PROD_PID
else
    echo "❌ start-production.js 启动失败"
fi

echo ""
echo "🚀 使用PM2启动服务..."
pm2 start start-production.js --name commission-platform-api --log-date-format="YYYY-MM-DD HH:mm:ss"

echo ""
echo "⏳ 等待服务启动..."
sleep 10

echo ""
echo "🔍 检查PM2状态:"
pm2 status

echo ""
echo "🔍 检查最新日志:"
pm2 logs commission-platform-api --lines 10 --nostream

echo ""
echo "🧪 测试API连接:"
curl -f http://localhost:3005/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}' 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ API测试成功"
else
    echo "❌ API测试失败"
fi

echo ""
echo "💾 保存PM2配置:"
pm2 save

echo ""
echo "🔧 修复完成！"
