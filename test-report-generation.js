#!/usr/bin/env node

// 测试八字报告生成和竞态条件修复
import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3005';

async function testReportGeneration() {
  console.log('🧪 测试八字报告生成竞态条件修复');
  console.log('=====================================');

  // 使用现有的订单ID进行测试
  const orderId = '250711200820554137';
  
  console.log(`📋 测试订单ID: ${orderId}`);
  
  // 1. 首先检查报告是否存在
  console.log('\n1️⃣ 检查报告当前状态...');
  try {
    const response = await fetch(`${API_BASE}/api/bazi/orders/${orderId}/report`);
    const data = await response.json();
    
    if (data.success && data.data.reportData?.data?.命主信息) {
      console.log('✅ 报告已存在，包含完整数据');
      console.log(`   命主姓名: ${data.data.reportData.data.命主信息.姓名}`);
      console.log(`   数据大小: ${JSON.stringify(data).length} 字符`);
    } else if (data.success) {
      console.log('⚠️ 报告存在但数据不完整');
    } else {
      console.log('❌ 报告不存在或获取失败');
    }
  } catch (error) {
    console.error('❌ 检查报告状态失败:', error.message);
  }

  // 2. 模拟前端的重试逻辑
  console.log('\n2️⃣ 模拟前端重试逻辑...');
  
  const maxRetries = 5;
  let attempt = 1;
  
  while (attempt <= maxRetries) {
    console.log(`\n🔄 尝试 ${attempt}/${maxRetries}...`);
    
    try {
      const response = await fetch(`${API_BASE}/api/bazi/orders/${orderId}/report`);
      const data = await response.json();
      
      if (data.success && data.data.reportData?.data?.命主信息) {
        console.log('✅ 报告获取成功！');
        console.log(`   命主信息: ${data.data.reportData.data.命主信息.姓名}`);
        console.log(`   八字: ${data.data.reportData.data.命主八字命盘信息?.八字信息?.公历?.年|| '未知'}`);
        break;
      } else if (data.success) {
        console.log('⏳ 报告正在生成中，等待重试...');
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 3000));
          attempt++;
        } else {
          console.log('❌ 重试次数已达上限');
        }
      } else {
        console.log('❌ API返回错误:', data.message);
        break;
      }
    } catch (error) {
      console.error(`❌ 尝试 ${attempt} 失败:`, error.message);
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        attempt++;
      } else {
        console.log('❌ 网络重试次数已达上限');
      }
    }
  }

  console.log('\n🎉 测试完成！');
  console.log('\n💡 前端修复要点:');
  console.log('   1. 检测报告是否完整生成');
  console.log('   2. 自动重试机制 (最多5次)');
  console.log('   3. 友好的加载提示');
  console.log('   4. 区分生成中和网络错误');
}

// 运行测试
testReportGeneration().catch(console.error);
