import express from 'express';
import { SystemConfig } from '../models/index.js';
import { adminAuth } from '../middleware/auth.js';

const router = express.Router();

// 获取起名业务配置
router.get('/naming', adminAuth, async (req, res) => {
  try {
    const config = {
      minPrice: await SystemConfig.getValue('naming_service_min_price', 99),
      maxPrice: await SystemConfig.getValue('naming_service_max_price', 299),
      commissionRate: await SystemConfig.getValue('naming_service_commission_rate', 20),
      isEnabled: await SystemConfig.getValue('naming_service_enabled', true)
    };

    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('获取起名业务配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
});

// 更新起名业务配置
router.put('/naming', adminAuth, async (req, res) => {
  try {
    const { minPrice, maxPrice, commissionRate, enabled } = req.body;

    // 验证输入
    if (minPrice && (isNaN(minPrice) || minPrice < 1)) {
      return res.status(400).json({
        success: false,
        message: '最低价格必须是大于0的数字'
      });
    }

    if (maxPrice && (isNaN(maxPrice) || maxPrice < 1)) {
      return res.status(400).json({
        success: false,
        message: '最高价格必须是大于0的数字'
      });
    }

    if (minPrice && maxPrice && Number(maxPrice) < Number(minPrice)) {
      return res.status(400).json({
        success: false,
        message: '最高价格必须大于等于最低价格'
      });
    }

    if (commissionRate && (isNaN(commissionRate) || commissionRate < 0 || commissionRate > 100)) {
      return res.status(400).json({
        success: false,
        message: '佣金比例必须在0-100之间'
      });
    }

    // 更新配置
    const updates = [];
    
    if (minPrice !== undefined) {
      await SystemConfig.setValue('naming_service_min_price', minPrice.toString());
      updates.push('最低价格');
    }
    
    if (maxPrice !== undefined) {
      await SystemConfig.setValue('naming_service_max_price', maxPrice.toString());
      updates.push('最高价格');
    }
    
    if (commissionRate !== undefined) {
      await SystemConfig.setValue('naming_service_commission_rate', commissionRate.toString());
      updates.push('佣金比例');
    }
    
    if (enabled !== undefined) {
      await SystemConfig.setValue('naming_service_enabled', enabled ? 'true' : 'false');
      updates.push('启用状态');
    }

    console.log('起名业务配置已更新:', updates.join(', '));

    res.json({
      success: true,
      message: `已更新: ${updates.join(', ')}`,
      data: {
        minPrice: await SystemConfig.getValue('naming_service_min_price', 99),
        maxPrice: await SystemConfig.getValue('naming_service_max_price', 299),
        commissionRate: await SystemConfig.getValue('naming_service_commission_rate', 20),
        isEnabled: await SystemConfig.getValue('naming_service_enabled', true)
      }
    });

  } catch (error) {
    console.error('更新起名业务配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新配置失败',
      error: error.message
    });
  }
});

// 获取所有系统配置
router.get('/all', adminAuth, async (req, res) => {
  try {
    const configs = await SystemConfig.findAll({
      order: [['group', 'ASC'], ['key', 'ASC']]
    });

    // 按组分类配置
    const groupedConfigs = configs.reduce((acc, config) => {
      const group = config.group || 'general';
      if (!acc[group]) {
        acc[group] = [];
      }
      acc[group].push({
        key: config.key,
        value: config.value,
        description: config.description
      });
      return acc;
    }, {});

    res.json({
      success: true,
      data: groupedConfigs
    });
  } catch (error) {
    console.error('获取系统配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置失败',
      error: error.message
    });
  }
});

export default router;
