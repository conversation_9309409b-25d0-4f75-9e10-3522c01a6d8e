'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // 添加佣金相关字段
    await queryInterface.addColumn('Users', 'totalCommission', {
      type: Sequelize.DECIMAL(10, 2),
      defaultValue: 0,
      comment: '累积佣金总额'
    });

    await queryInterface.addColumn('Users', 'monthlyCommission', {
      type: Sequelize.DECIMAL(10, 2),
      defaultValue: 0,
      comment: '当月佣金总额'
    });

    await queryInterface.addColumn('Users', 'lastCommissionReset', {
      type: Sequelize.DATE,
      allowNull: true,
      comment: '上次佣金重置时间（用于月度统计）'
    });
  },

  async down(queryInterface, Sequelize) {
    // 如果需要回滚，则删除这些字段
    await queryInterface.removeColumn('Users', 'totalCommission');
    await queryInterface.removeColumn('Users', 'monthlyCommission');
    await queryInterface.removeColumn('Users', 'lastCommissionReset');
  }
};
