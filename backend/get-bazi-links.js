import { Product, ProductLink, User } from './src/models/index.js';
import sequelize from './src/config/database.js';

async function getBaziProductLink() {
  try {
    await sequelize.authenticate();
    
    // 查找八字产品
    const baziProduct = await Product.findOne({
      where: { 
        type: 'service',
        title: { [sequelize.Sequelize.Op.like]: '%八字%' }
      }
    });
    
    if (!baziProduct) {
      console.log('❌ 没有找到八字产品');
      return;
    }
    
    console.log('✅ 找到八字产品:');
    console.log(`   ID: ${baziProduct.id}`);
    console.log(`   标题: ${baziProduct.title}`);
    console.log(`   类型: ${baziProduct.type}`);
    console.log(`   价格: ${baziProduct.price} 元`);
    
    // 查找该产品的推广链接
    const productLinks = await ProductLink.findAll({
      where: { productId: baziProduct.id },
      include: [{
        model: User,
        as: 'linkAgent',
        attributes: ['id', 'name', 'username']
      }]
    });
    
    console.log(`\n✅ 找到 ${productLinks.length} 个推广链接:`);
    productLinks.forEach(link => {
      console.log(`   代码: ${link.code}`);
      console.log(`   代理: ${link.linkAgent?.name || '未知'} (${link.linkAgent?.username || '未知'})`);
      console.log(`   测试链接: http://localhost:3000/bazi/${link.code}`);
      console.log('');
    });
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  } finally {
    await sequelize.close();
  }
}

getBaziProductLink();
