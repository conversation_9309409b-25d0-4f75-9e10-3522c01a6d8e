'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.addColumn('Orders', 'deviceId', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: '设备标识，用于防止短时间内重复下单'
    });

    await queryInterface.addColumn('Orders', 'clientIp', {
      type: Sequelize.STRING,
      allowNull: true,
      comment: '客户端IP地址，用于防止短时间内重复下单'
    });

    // 添加索引以加速查询
    await queryInterface.addIndex('Orders', ['deviceId', 'clientIp', 'createdAt'], {
      name: 'orders_device_ip_created_idx'
    });
  },

  async down (queryInterface, Sequelize) {
    // 先删除索引
    await queryInterface.removeIndex('Orders', 'orders_device_ip_created_idx');
    
    // 再删除列
    await queryInterface.removeColumn('Orders', 'deviceId');
    await queryInterface.removeColumn('Orders', 'clientIp');
  }
};
