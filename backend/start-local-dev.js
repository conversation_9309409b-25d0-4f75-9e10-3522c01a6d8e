/**
 * 本地开发环境启动脚本
 * 确保使用正确的环境变量配置
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 强制加载本地开发环境配置
dotenv.config({ path: path.join(__dirname, '.env.local') });

console.log('🚀 启动本地开发环境');
console.log('='.repeat(50));

console.log('环境变量配置:');
console.log(`NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`PORT: ${process.env.PORT}`);
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL}`);
console.log(`DB_HOST: ${process.env.DB_HOST}`);
console.log(`DB_NAME: ${process.env.DB_NAME}`);
console.log();

// 验证关键配置
const requiredVars = [
  'NODE_ENV',
  'PORT', 
  'FRONTEND_URL',
  'DB_HOST',
  'DB_NAME',
  'ALIPAY_APPID'
];

const missingVars = requiredVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ 缺少必要的环境变量:');
  missingVars.forEach(varName => console.error(`  - ${varName}`));
  process.exit(1);
}

console.log('✅ 环境变量配置正确');
console.log();

// 测试八字产品跳转逻辑
console.log('🧪 测试八字产品跳转逻辑:');

const mockOrder = {
  id: 'TEST123456',
  productLink: {
    product: {
      title: '玄易八字命盘',
      type: 'service'
    }
  }
};

const productTitle = mockOrder.productLink.product.title;
const productType = mockOrder.productLink.product.type;

const isBaziService = productType === 'service' && 
                     (productTitle.includes('八字') || productTitle.includes('命理'));

let redirectUrl;
if (isBaziService) {
  redirectUrl = `${process.env.FRONTEND_URL}/bazi-report?orderId=${mockOrder.id}`;
} else {
  redirectUrl = process.env.PAID_RETURN_URL;
}

console.log(`产品: ${productTitle} (${productType})`);
console.log(`是否八字服务: ${isBaziService}`);
console.log(`跳转地址: ${redirectUrl}`);

if (isBaziService && redirectUrl.includes('localhost:3000')) {
  console.log('✅ 本地开发环境配置正确！八字产品会跳转到本地前端');
} else if (isBaziService && redirectUrl.includes('ye.bzcy.xyz')) {
  console.log('⚠️  八字产品会跳转到生产环境前端');
} else {
  console.log('❌ 八字产品跳转配置有问题');
}

console.log();
console.log('🎯 使用说明:');
console.log('1. 确保前端服务运行在 http://localhost:3000');
console.log('2. 使用此脚本启动后端服务: node start-local-dev.js');
console.log('3. 八字产品支付成功后会跳转到: http://localhost:3000/bazi-report');
console.log();

// 动态导入并启动服务器
import('./src/app.js').then(app => {
  console.log('🌟 后端服务已启动，使用本地开发环境配置');
}).catch(error => {
  console.error('❌ 启动服务失败:', error);
  process.exit(1);
});
