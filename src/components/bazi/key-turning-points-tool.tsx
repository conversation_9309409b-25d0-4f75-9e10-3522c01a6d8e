import { useState } from 'react';
// 你需要将 getKeyTurningPoints 方法迁移到主项目的 ai/flows/key-turning-points，并修正路径
import { getKeyTurningPoints, type KeyTurningPointsInput } from '@/ai/flows/key-turning-points';
import { SectionCard } from './section-card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Loader2, Wand2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ParagraphText } from './info-item';

interface KeyTurningPointsToolProps {
  defaultInputText: string;
}

export function KeyTurningPointsTool({ defaultInputText }: KeyTurningPointsToolProps) {
  const [inputText, setInputText] = useState(defaultInputText);
  const [result, setResult] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const input: KeyTurningPointsInput = { comprehensiveEightCharacterData: inputText };
      const output = await getKeyTurningPoints(input);
      setResult(output.keyTurningPoints);
    } catch (e) {
      console.error("Error calling AI flow:", e);
      setError(e instanceof Error ? e.message : "An unknown error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SectionCard title="AI 智能解读：人生关键转折点" icon={Wand2}>
      <div className="space-y-4">
        <div>
          <label htmlFor="aiInput" className="block text-sm font-medium text-primary mb-1">
            综合八字数据 (AI分析输入)
          </label>
          <Textarea
            id="aiInput"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            placeholder="请输入或粘贴用于分析的八字数据..."
            rows={8}
            className="bg-background/50 focus:border-accent"
            disabled={isLoading}
          />
          <p className="text-xs text-muted-foreground mt-1">
            此内容已根据您的命盘信息预填，您可以按需修改。
          </p>
        </div>

        <Button onClick={handleSubmit} disabled={isLoading || !inputText.trim()} className="w-full sm:w-auto">
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              分析中...
            </>
          ) : (
            <>
              <Wand2 className="mr-2 h-4 w-4" />
              获取 AI 分析
            </>
          )}
        </Button>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle className="text-sm sm:text-base">分析出错</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <div className="mt-4 p-4 border border-accent/50 rounded-md bg-accent/10">
            <h3 className="text-base sm:text-lg font-semibold text-primary mb-2">AI 分析结果：</h3>
            <ParagraphText text={result} />
          </div>
        )}
      </div>
    </SectionCard>
  );
} 