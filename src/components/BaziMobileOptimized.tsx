import React, { useState, useEffect } from 'react';
import { Button, Card, Steps, Progress, Typography, Space, Divider } from 'antd';
import { 
  UserOutlined, 
  CalendarOutlined, 
  EnvironmentOutlined,
  CheckCircleOutlined,
  StarOutlined,
  SafetyOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

interface BaziMobileOptimizedProps {
  onStartQuery: () => void;
  productInfo?: {
    name: string;
    price: number;
    description: string;
  };
}

const BaziMobileOptimized: React.FC<BaziMobileOptimizedProps> = ({ onStartQuery, productInfo }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    setIsVisible(true);
    const timer = setInterval(() => {
      setCurrentStep((prev) => (prev + 1) % 3);
    }, 3000);
    return () => clearInterval(timer);
  }, []);

  const features = [
    {
      icon: <UserOutlined style={{ fontSize: '2rem', color: '#667eea' }} />,
      title: '个人专属',
      description: '基于您的生辰八字生成专属命理报告'
    },
    {
      icon: <StarOutlined style={{ fontSize: '2rem', color: '#667eea' }} />,
      title: '精准分析',
      description: '运用千年传承的命理学精准解析'
    },
    {
      icon: <SafetyOutlined style={{ fontSize: '2rem', color: '#667eea' }} />,
      title: '隐私保护',
      description: '严格保护个人信息，仅用于报告生成'
    }
  ];

  const steps = [
    {
      title: '填写信息',
      description: '输入准确的生辰信息',
      icon: <UserOutlined />
    },
    {
      title: '智能分析',
      description: 'AI结合传统命理学分析',
      icon: <CalendarOutlined />
    },
    {
      title: '获得报告',
      description: '获取专业详细的命理报告',
      icon: <CheckCircleOutlined />
    }
  ];

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '1rem',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")`,
        opacity: 0.5
      }} />

      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* 头部 */}
        <div style={{
          textAlign: 'center',
          marginBottom: '2rem',
          color: 'white',
          transform: isVisible ? 'translateY(0)' : 'translateY(-20px)',
          opacity: isVisible ? 1 : 0,
          transition: 'all 0.6s ease'
        }}>
          <div style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '0.5rem',
            background: 'linear-gradient(45deg, #FFD700, #FFA500)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text'
          }}>
            ✨ 八字命理 ✨
          </div>
          <div style={{ fontSize: '1rem', opacity: 0.9 }}>
            千年传承 · 精准解析
          </div>
          <div style={{ fontSize: '0.9rem', opacity: 0.8, marginTop: '0.5rem' }}>
            已服务 <span style={{ color: '#FFD700', fontWeight: 'bold' }}>100,000+</span> 用户
          </div>
        </div>

        {/* 产品卡片 */}
        {productInfo && (
          <Card style={{
            borderRadius: '20px',
            background: 'rgba(255,255,255,0.95)',
            backdropFilter: 'blur(10px)',
            border: 'none',
            marginBottom: '2rem',
            boxShadow: '0 10px 30px rgba(0,0,0,0.1)'
          }}>
            <div style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              color: 'white',
              padding: '1.5rem',
              borderRadius: '15px',
              textAlign: 'center',
              marginBottom: '1.5rem'
            }}>
              <Title level={3} style={{ color: 'white', marginBottom: '1rem' }}>
                {productInfo.name}
              </Title>
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: '1rem',
                marginBottom: '1rem'
              }}>
                <div style={{
                  background: 'rgba(255,255,255,0.2)',
                  padding: '0.8rem 1.5rem',
                  borderRadius: '20px',
                  fontSize: '1.3rem',
                  fontWeight: 'bold'
                }}>
                  ¥{productInfo.price}
                </div>
                <div style={{
                  background: 'rgba(255,215,0,0.2)',
                  padding: '0.5rem 1rem',
                  borderRadius: '15px',
                  fontSize: '0.8rem',
                  border: '1px solid rgba(255,215,0,0.3)'
                }}>
                  🔥 限时优惠
                </div>
              </div>
              <Paragraph style={{ color: 'rgba(255,255,255,0.9)', margin: 0 }}>
                {productInfo.description}
              </Paragraph>
            </div>

            {/* 特色功能 */}
            <div style={{ marginBottom: '1.5rem' }}>
              <Title level={5} style={{ textAlign: 'center', marginBottom: '1rem' }}>
                为什么选择我们
              </Title>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                {features.map((feature, index) => (
                  <div key={index} style={{
                    display: 'flex',
                    alignItems: 'center',
                    padding: '1rem',
                    background: '#f8f9fa',
                    borderRadius: '10px',
                    border: '1px solid #e9ecef'
                  }}>
                    <div style={{ marginRight: '1rem' }}>
                      {feature.icon}
                    </div>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: '0.3rem' }}>
                        {feature.title}
                      </div>
                      <div style={{ fontSize: '0.85rem', color: '#666' }}>
                        {feature.description}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* 流程步骤 */}
            <div style={{ marginBottom: '1.5rem' }}>
              <Title level={5} style={{ textAlign: 'center', marginBottom: '1rem' }}>
                简单三步，获取专业报告
              </Title>
              <Steps
                current={currentStep}
                direction="vertical"
                size="small"
                style={{ padding: '0 1rem' }}
              >
                {steps.map((step, index) => (
                  <Step
                    key={index}
                    title={step.title}
                    description={step.description}
                    icon={step.icon}
                  />
                ))}
              </Steps>
            </div>

            {/* 进度条 */}
            <div style={{ marginBottom: '1.5rem' }}>
              <Progress
                percent={(currentStep + 1) * 33.33}
                strokeColor={{
                  '0%': '#667eea',
                  '100%': '#764ba2',
                }}
                showInfo={false}
                strokeWidth={8}
                style={{ marginBottom: '0.5rem' }}
              />
              <div style={{ textAlign: 'center', fontSize: '0.8rem', color: '#666' }}>
                第 {currentStep + 1} 步 / 共 3 步
              </div>
            </div>

            {/* CTA按钮 */}
            <Button
              type="primary"
              size="large"
              onClick={onStartQuery}
              style={{
                width: '100%',
                height: '50px',
                fontSize: '16px',
                fontWeight: 'bold',
                borderRadius: '12px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                boxShadow: '0 6px 15px rgba(102, 126, 234, 0.3)'
              }}
            >
              立即开始查询 →
            </Button>
          </Card>
        )}

        {/* 用户评价 */}
        <Card style={{
          borderRadius: '15px',
          background: 'rgba(255,255,255,0.9)',
          backdropFilter: 'blur(10px)',
          border: 'none',
          marginBottom: '2rem'
        }}>
          <Title level={5} style={{ textAlign: 'center', marginBottom: '1rem' }}>
            用户真实反馈
          </Title>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>
              ⭐⭐⭐⭐⭐
            </div>
            <Paragraph style={{ fontStyle: 'italic', marginBottom: '0.5rem' }}>
              "分析很准确，对我的人生规划很有帮助"
            </Paragraph>
            <Text type="secondary">- 张女士，32岁，北京</Text>
          </div>
        </Card>

        {/* 安全保障 */}
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '15px',
          padding: '1rem',
          textAlign: 'center',
          color: 'white'
        }}>
          <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>🔒</div>
          <div style={{ fontSize: '0.9rem', marginBottom: '0.3rem' }}>
            信息安全保护
          </div>
          <div style={{ fontSize: '0.8rem', opacity: 0.8 }}>
            您的个人信息将被严格保密
          </div>
        </div>
      </div>
    </div>
  );
};

export default BaziMobileOptimized;
