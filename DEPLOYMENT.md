# 部署指南

## 🚀 远程服务器部署

### 前提条件
1. 确保可以SSH连接到远程服务器：`root@47.100.46.63`
2. 远程服务器已安装：Node.js, npm, PM2, Nginx
3. 本地已配置SSH密钥或密码认证

### 部署步骤

1. **运行远程部署脚本**：
```bash
./deploy-production.sh
```

这个脚本会自动：
- 在本地构建前端
- 创建部署包
- 上传文件到远程服务器
- 在远程服务器上执行部署
- 测试API连接

### 手动部署（如果自动脚本失败）

1. **本地构建**：
```bash
npm run build
```

2. **上传文件到服务器**：
```bash
rsync -avz --delete ./ root@47.100.46.63:/data/commission-platform8z/
```

3. **SSH到服务器执行部署**：
```bash
ssh root@47.100.46.63
cd /data/commission-platform8z

# 停止现有服务
pm2 stop commission-platform-api
pm2 delete commission-platform-api

# 清理端口
lsof -ti:3005 | xargs kill -9

# 部署前端
mkdir -p /data/commission-platform
rm -rf /data/commission-platform/*
cp -r dist/* /data/commission-platform/
chown -R www-data:www-data /data/commission-platform

# 更新Nginx配置
cp nginx.conf /etc/nginx/sites-available/commission-platform
ln -sf /etc/nginx/sites-available/commission-platform /etc/nginx/sites-enabled/commission-platform
nginx -t
systemctl reload nginx

# 启动后端
cd backend
npm install --production
pm2 start start-production.js --name commission-platform-api
pm2 save
```

## 🏠 本地部署

如果需要在本地测试生产环境配置：

```bash
./deploy-local.sh
```

## 📊 监控和管理

### 远程服务器管理命令

```bash
# 查看服务状态
ssh root@47.100.46.63 'pm2 status'

# 查看日志
ssh root@47.100.46.63 'pm2 logs commission-platform-api'

# 重启服务
ssh root@47.100.46.63 'pm2 restart commission-platform-api'

# 查看Nginx状态
ssh root@47.100.46.63 'systemctl status nginx'

# 查看Nginx错误日志
ssh root@47.100.46.63 'tail -f /var/log/nginx/error.log'
```

### 故障排除

1. **API 404错误**：
   - 检查后端服务是否运行：`pm2 status`
   - 检查端口3005是否被占用：`lsof -i :3005`
   - 检查Nginx配置：`nginx -t`

2. **前端无法访问**：
   - 检查Nginx是否运行：`systemctl status nginx`
   - 检查前端文件是否存在：`ls -la /data/commission-platform/`

3. **SSL证书问题**：
   - 检查证书文件：`ls -la /etc/letsencrypt/live/ye.bzcy.xyz/`
   - 更新证书：`certbot renew`

## 🔧 配置文件

- **Nginx配置**：`nginx.conf`
- **后端环境配置**：`backend/.env.production`
- **前端配置**：`src/config.ts`
- **PM2启动脚本**：`backend/start-production.js`

## 📝 注意事项

1. 部署前确保所有代码已提交并推送到Git仓库
2. 生产环境使用端口3005，确保防火墙允许此端口
3. 定期备份数据库和配置文件
4. 监控服务器资源使用情况
