import { Order, User, Product } from '../models/index.js';
import { calculateCommissions } from '../utils/commissionUtils.js';
import sequelize from '../config/database.js';

const fixHistoricalOrders = async () => {
  try {
    console.log('开始修复历史订单数据...\n');

    // 开启事务
    const t = await sequelize.transaction();

    try {
      // 查询所有已支付的订单
      const orders = await Order.findAll({
        where: {
          status: 'paid'
        },
        include: [
          {
            model: User,
            as: 'orderAgent',
            attributes: ['id', 'level', 'commissionRate']
          }
        ]
      });

      console.log(`找到 ${orders.length} 个已支付订单\n`);

      // 重置所有用户的佣金
      await User.update(
        {
          totalCommission: 0,
          monthlyCommission: 0,
          lastCommissionReset: new Date()
        },
        { 
          where: {},
          transaction: t
        }
      );

      for (const order of orders) {
        const agent = order.orderAgent;
        
        if (!agent) {
          console.log(`警告: 订单 ${order.id} 找不到代理信息，跳过`);
          continue;
        }

        let commissions;
        if (agent.level === 0 && order.agentId === agent.id) {  // 管理员使用自己的链接下单
          commissions = {
            adminCommission: order.amount,  // 100%佣金
            agentCommission: 0,
            parentAgentCommission: 0
          };
        } else {  // 代理下单或通过代理链接下单
          commissions = calculateCommissions(
            order.amount,
            agent.commissionRate * 100,
            order.parentAgentId ? 
              (await User.findByPk(order.parentAgentId))?.commissionRate * 100 : 
              null
          );
        }

        // 更新订单佣金
        await order.update({
          adminCommission: commissions.adminCommission,
          agentCommission: commissions.agentCommission || commissions.level1Commission || 0,
          parentAgentCommission: commissions.parentAgentCommission || commissions.level2Commission || 0
        }, { transaction: t });

        // 更新用户佣金
        // 1. 更新管理员佣金
        const admin = await User.findOne({
          where: { level: 0 }
        });
        if (admin) {
          await admin.increment('totalCommission', { 
            by: commissions.adminCommission,
            transaction: t 
          });
        }

        // 2. 更新代理佣金
        if (agent.level > 0) {
          await agent.increment('totalCommission', { 
            by: commissions.agentCommission || commissions.level1Commission || 0,
            transaction: t 
          });
        }

        // 3. 更新上级代理佣金
        if (order.parentAgentId) {
          const parentAgent = await User.findByPk(order.parentAgentId);
          if (parentAgent) {
            await parentAgent.increment('totalCommission', { 
              by: commissions.parentAgentCommission || commissions.level2Commission || 0,
              transaction: t 
            });
          }
        }

        console.log(`订单 ${order.id} 更新成功:`, {
          amount: order.amount,
          agentLevel: agent.level,
          isAdminOrder: agent.level === 0 && order.agentId === agent.id,
          adminCommission: commissions.adminCommission,
          agentCommission: commissions.agentCommission || commissions.level1Commission || 0,
          parentAgentCommission: commissions.parentAgentCommission || commissions.level2Commission || 0
        });
      }

      // 提交事务
      await t.commit();

      // 显示最终统计
      const adminStats = await User.findOne({
        where: { level: 0 },
        attributes: ['totalCommission', 'monthlyCommission']
      });

      console.log('\n修复完成！');
      console.log('管理员佣金统计:', {
        totalCommission: adminStats.totalCommission,
        monthlyCommission: adminStats.monthlyCommission
      });

    } catch (error) {
      // 如果出错，回滚事务
      await t.rollback();
      throw error;
    }

  } catch (error) {
    console.error('修复历史订单失败:', error);
  } finally {
    process.exit();
  }
};

// 执行修复
console.log('开始修复历史订单...');
fixHistoricalOrders();
