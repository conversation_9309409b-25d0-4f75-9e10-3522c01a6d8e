import type { MingZhuXingMingShuLiXinXi, ShuLiDetail } from '../../types/bazi';
import { SectionCard } from './section-card';
import { InfoItem, ParagraphText } from './info-item';
import { Badge } from '@/components/ui/badge';
import { Users, PenTool, Sigma } from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface XingMingShuLiSectionProps {
  data: MingZhuXingMingShuLiXinXi;
}

const ShuLiDetailDisplay: React.FC<{ title: string, detail: ShuLiDetail, defaultOpen?: boolean }> = ({ title, detail, defaultOpen }) => (
  <AccordionItem value={title} className="border-b-0 mb-2">
    <AccordionTrigger className="bg-background/50 hover:bg-muted/60 px-3 py-2.5 sm:px-4 sm:py-3 rounded-md text-base sm:text-lg font-medium text-primary hover:no-underline text-left">
      {title} ({detail.五行}行,{detail.数理}数,
      <Badge 
        variant={detail.吉凶 === "吉" || detail.吉凶 === "大吉" ? "default" : "destructive"} 
        className="ml-2 px-1.5 py-0.5 text-xs"
      >
        {detail.吉凶}
      </Badge>
      )
    </AccordionTrigger>
    <AccordionContent className="pt-3 px-3 sm:px-4 text-sm space-y-1"> 
      <InfoItem label="象词" value={detail.象词} />
      <InfoItem label="签语" value={<ParagraphText text={detail.签语} />} />
      <InfoItem label="数理暗示" value={<ParagraphText text={detail.数理暗示} />} />
      <InfoItem label="含义" value={<ParagraphText text={detail.含义} />} />
      <InfoItem label="主运影响" value={detail.主运} />
    </AccordionContent>
  </AccordionItem>
);

export function XingMingShuLiSection({ data }: XingMingShuLiSectionProps) {
  return (
    <SectionCard title="命主姓名数理信息" icon={PenTool} contentClassName="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-primary mb-2 mt-4 flex items-center">
          <Sigma className="mr-2 h-5 w-5 text-accent" />
          姓名总分
        </h3>
        <div className="p-4 border-2 border-accent/60 rounded-lg bg-gradient-to-br from-accent/10 to-accent/5 shadow-lg">
          <p className="text-3xl sm:text-4xl font-bold text-center mb-3">
            <span className="bazi-highlight">{data.姓名总分.分数}分</span>
          </p>
          <ParagraphText text={data.姓名总分.说明} />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-primary mb-2">姓名五行</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"> 
          {data.姓名五行.map((item, index) => (
            <div key={index} className="p-3 border border-border/70 rounded-md bg-background/30 shadow-sm">
              <p className="font-semibold text-base sm:text-lg">{item.简体} ({item.繁体})</p>
              <p className="text-sm">拼音: {item.拼音}</p> 
              <p className="text-sm">五行: {item.繁体五行}</p>
              <p className="text-sm">笔画: {item.繁体笔画}</p>
            </div>
          ))}
        </div>
      </div>

      <Accordion type="multiple" className="w-full space-y-3" defaultValue={["天格"]}> 
        <ShuLiDetailDisplay title="天格" detail={data.天格} defaultOpen />
        <ShuLiDetailDisplay title="人格" detail={data.人格} />
        <ShuLiDetailDisplay title="地格" detail={data.地格} />
        <ShuLiDetailDisplay title="外格" detail={data.外格} />
        <ShuLiDetailDisplay title="总格" detail={data.总格} />
      </Accordion>
      
      <div>
        <h3 className="text-lg font-semibold text-primary mb-3 mt-4 flex items-center">
          <Users className="mr-2 h-5 w-5 text-accent" />
          三才配置 ({data.三才配置.三才吉凶})
        </h3>
        <div className="space-y-3 p-3 border border-border/70 rounded-md bg-background/30 shadow-sm"> 
          {data.三才配置.重点 && 
            <InfoItem 
              label="重点" 
              value={data.三才配置.重点} 
              labelClassName="text-destructive-foreground !font-bold" 
              valueClassName="text-destructive-foreground" 
              className="bg-destructive p-2 rounded shadow"
            />
          }
          <ParagraphText text={data.三才配置.三才解析} />
          <InfoItem label="基础运" value={data.三才配置.基础运} />
          <InfoItem label="成功运" value={data.三才配置.成功运} />
          <InfoItem label="社交运" value={data.三才配置.社交运} />
        </div>
      </div>
    </SectionCard>
  );
} 