// SEO优化组件 - 产品级SEO管理

import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  locale?: string;
  siteName?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
  noindex?: boolean;
  canonical?: string;
  structuredData?: object;
}

const SEOHead: React.FC<SEOProps> = ({
  title = '八字精批 - 专业命理测算 | 权威大师在线解读',
  description = '专业八字精批测算，权威命理大师在线解读。提供详细的性格分析、事业财运、感情婚姻、健康养生等全方位命理指导。准确率95%以上，24小时内出报告。',
  keywords = [
    '八字精批',
    '八字测算',
    '命理分析',
    '生辰八字',
    '算命',
    '占卜',
    '运势',
    '命理大师',
    '在线测算',
    '免费算命'
  ],
  image = '/images/bazi-og-image.jpg',
  url = typeof window !== 'undefined' ? window.location.href : '',
  type = 'website',
  author = '八字精批专业团队',
  publishedTime,
  modifiedTime,
  locale = 'zh_CN',
  siteName = '八字精批 - 专业命理测算平台',
  twitterCard = 'summary_large_image',
  noindex = false,
  canonical,
  structuredData
}) => {
  // 默认结构化数据
  const defaultStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebApplication',
    name: siteName,
    description,
    url,
    applicationCategory: 'LifestyleApplication',
    operatingSystem: 'Web',
    offers: {
      '@type': 'Offer',
      price: '99',
      priceCurrency: 'CNY',
      description: '八字精批详细报告'
    },
    provider: {
      '@type': 'Organization',
      name: '八字精批',
      url: url,
      logo: '/images/logo.png'
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      reviewCount: '1250',
      bestRating: '5',
      worstRating: '1'
    },
    review: [
      {
        '@type': 'Review',
        author: {
          '@type': 'Person',
          name: '张女士'
        },
        reviewRating: {
          '@type': 'Rating',
          ratingValue: '5'
        },
        reviewBody: '非常准确的八字分析，大师解读很专业，对我的人生规划很有帮助。'
      }
    ]
  };

  const finalStructuredData = structuredData || defaultStructuredData;

  return (
    <Helmet>
      {/* 基础SEO标签 */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      
      {/* 语言和地区 */}
      <meta name="language" content="zh-CN" />
      <meta name="geo.region" content="CN" />
      <meta name="geo.country" content="China" />
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      <meta name="apple-mobile-web-app-title" content="八字精批" />
      
      {/* 搜索引擎指令 */}
      {noindex ? (
        <meta name="robots" content="noindex, nofollow" />
      ) : (
        <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
      )}
      
      {/* 规范链接 */}
      {canonical && <link rel="canonical" href={canonical} />}
      
      {/* Open Graph 标签 */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={locale} />
      
      {publishedTime && <meta property="article:published_time" content={publishedTime} />}
      {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
      {author && <meta property="article:author" content={author} />}
      
      {/* Twitter Card 标签 */}
      <meta name="twitter:card" content={twitterCard} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      
      {/* 微信分享优化 */}
      <meta name="weixin:title" content={title} />
      <meta name="weixin:description" content={description} />
      <meta name="weixin:image" content={image} />
      
      {/* QQ分享优化 */}
      <meta name="qq:title" content={title} />
      <meta name="qq:description" content={description} />
      <meta name="qq:image" content={image} />
      
      {/* 微博分享优化 */}
      <meta name="weibo:title" content={title} />
      <meta name="weibo:description" content={description} />
      <meta name="weibo:image" content={image} />
      
      {/* 百度相关 */}
      <meta name="baidu-site-verification" content="your-baidu-verification-code" />
      <meta name="360-site-verification" content="your-360-verification-code" />
      <meta name="sogou_site_verification" content="your-sogou-verification-code" />
      
      {/* 网站图标 */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* DNS预解析 */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//cdn.jsdelivr.net" />
      
      {/* 预加载关键资源 */}
      <link rel="preload" href="/fonts/chinese-font.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      
      {/* 结构化数据 */}
      <script type="application/ld+json">
        {JSON.stringify(finalStructuredData)}
      </script>
      
      {/* 额外的结构化数据 - 面包屑导航 */}
      <script type="application/ld+json">
        {JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'BreadcrumbList',
          itemListElement: [
            {
              '@type': 'ListItem',
              position: 1,
              name: '首页',
              item: url.replace(/\/[^\/]*$/, '')
            },
            {
              '@type': 'ListItem',
              position: 2,
              name: '八字精批',
              item: url
            }
          ]
        })}
      </script>
      
      {/* FAQ结构化数据 */}
      <script type="application/ld+json">
        {JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'FAQPage',
          mainEntity: [
            {
              '@type': 'Question',
              name: '八字精批准确吗？',
              acceptedAnswer: {
                '@type': 'Answer',
                text: '我们的八字精批由专业命理大师解读，准确率达95%以上，已为数万用户提供准确的命理指导。'
              }
            },
            {
              '@type': 'Question',
              name: '多久能收到报告？',
              acceptedAnswer: {
                '@type': 'Answer',
                text: '付费后24小时内即可收到详细的八字精批报告，包含性格分析、事业财运、感情婚姻等全方位解读。'
              }
            },
            {
              '@type': 'Question',
              name: '个人信息安全吗？',
              acceptedAnswer: {
                '@type': 'Answer',
                text: '我们严格保护用户隐私，所有个人信息均采用加密存储，绝不泄露给第三方。'
              }
            }
          ]
        })}
      </script>
    </Helmet>
  );
};

export default SEOHead;
