{"timestamp": "2025-03-13T14:01:36.526Z", "summary": {"total": 15, "passed": 1, "failed": 14, "skipped": 0}, "details": [{"name": "用户登录 - 管理员", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "Invalid credentials"}}, {"name": "获取管理员个人资料", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "获取所有类别", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "创建测试类别", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "获取所有产品", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "创建测试产品", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "获取所有用户", "result": "failed", "error": "Request failed with status code 404", "details": {"success": false, "error": "路由未找到: GET /api/users"}}, {"name": "注册测试用户", "result": "passed"}, {"name": "生成代理邀请链接", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "用户登录 - 普通用户", "result": "failed", "error": "Request failed with status code 403", "details": {"message": "Account pending approval"}}, {"name": "获取个人资料", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "浏览产品列表", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "查询佣金记录", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "查询提现记录", "result": "failed", "error": "Request failed with status code 401", "details": {"message": "请先登录"}}, {"name": "API健康检查", "result": "failed", "error": "API健康检查失败: Request failed with status code 401"}], "config": {"baseURL": "https://ye.bzcy.xyz", "includeAdmin": true}}