/**
 * 生成订单号
 * 格式：年月日时分秒 + 6位随机数
 * 示例：241229001234123456 (16位)
 */
export function generateOrderNumber() {
  const now = new Date();
  const year = String(now.getFullYear()).slice(-2);
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const random = String(Math.floor(Math.random() * 1000000)).padStart(6, '0');

  return `${year}${month}${day}${hours}${minutes}${seconds}${random}`;
}

/**
 * 解析订单号
 * @param {string} orderNumber - 订单号
 * @returns {{
 *   year: string,
 *   month: string,
 *   day: string,
 *   hours: string,
 *   minutes: string,
 *   seconds: string,
 *   random: string,
 *   timestamp: Date
 * }}
 */
export function parseOrderNumber(orderNumber) {
  const year = orderNumber.slice(0, 2);
  const month = orderNumber.slice(2, 4);
  const day = orderNumber.slice(4, 6);
  const hours = orderNumber.slice(6, 8);
  const minutes = orderNumber.slice(8, 10);
  const seconds = orderNumber.slice(10, 12);
  const random = orderNumber.slice(12);

  return {
    year,
    month,
    day,
    hours,
    minutes,
    seconds,
    random,
    timestamp: new Date(`20${year}-${month}-${day} ${hours}:${minutes}:${seconds}`)
  };
}
