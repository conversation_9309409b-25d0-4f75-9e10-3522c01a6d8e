import fetch from 'node-fetch';

async function testBaziAPI() {
  console.log('=== 测试八字报告API ===\n');
  
  const orderId = '250707185840805299'; // 已支付的八字订单
  const baseUrl = 'http://localhost:3005';
  
  try {
    console.log(`1. 测试获取订单 ${orderId} 的八字报告:`);
    
    const response = await fetch(`${baseUrl}/api/bazi/orders/${orderId}/report`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log(`响应状态: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('❌ API调用失败');
      console.log('错误响应:', errorText);
      return;
    }
    
    const data = await response.json();
    console.log('✅ API调用成功');
    console.log('响应数据结构:');
    console.log(`  success: ${data.success}`);
    console.log(`  message: ${data.message || '无'}`);
    
    if (data.data) {
      console.log('  data 字段存在:');
      console.log(`    reportData 存在: ${!!data.data.reportData}`);
      console.log(`    customerInfo 存在: ${!!data.data.customerInfo}`);
      
      if (data.data.reportData) {
        console.log('    reportData 主要字段:');
        const reportData = data.data.reportData;
        console.log(`      命主信息: ${!!reportData.命主信息}`);
        console.log(`      命主八字命盘信息: ${!!reportData.命主八字命盘信息}`);
        
        if (reportData.命主信息) {
          console.log('      命主信息详情:');
          Object.entries(reportData.命主信息).forEach(([key, value]) => {
            console.log(`        ${key}: ${value}`);
          });
        }
      }
    } else {
      console.log('  ❌ data 字段不存在');
    }
    
    // 2. 测试前端页面访问
    console.log('\n2. 测试前端八字报告页面访问:');
    
    const frontendUrl = `https://ye.bzcy.xyz/bazi-report?orderId=${orderId}`;
    console.log(`前端URL: ${frontendUrl}`);
    
    try {
      const frontendResponse = await fetch(frontendUrl, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; TestBot/1.0)'
        }
      });
      
      console.log(`前端页面响应状态: ${frontendResponse.status} ${frontendResponse.statusText}`);
      
      if (frontendResponse.ok) {
        console.log('✅ 前端页面可以访问');
      } else {
        console.log('❌ 前端页面访问失败');
      }
    } catch (frontendError) {
      console.log('❌ 前端页面访问出错:', frontendError.message);
    }
    
    // 3. 分析问题
    console.log('\n3. 问题分析:');
    
    if (data.success && data.data && data.data.reportData) {
      console.log('✅ 后端API正常，能够返回八字报告数据');
      console.log('\n🔍 可能的问题原因:');
      console.log('1. 生产环境的后端服务可能还是旧版本');
      console.log('2. 生产环境的支付回调处理逻辑可能没有更新');
      console.log('3. 生产环境的前端可能没有部署最新版本');
      console.log('4. 支付回调时的跳转逻辑可能被缓存或有其他问题');
      
      console.log('\n🛠️  建议的解决步骤:');
      console.log('1. 确认生产环境后端代码已更新到最新版本');
      console.log('2. 重启生产环境的后端服务（PM2或其他进程管理器）');
      console.log('3. 确认生产环境前端已部署最新版本');
      console.log('4. 清除浏览器缓存，重新测试支付流程');
      console.log('5. 检查生产环境的nginx配置，确保路由正确');
    } else {
      console.log('❌ 后端API有问题，无法正常返回八字报告数据');
      console.log('需要先修复后端API问题');
    }
    
  } catch (error) {
    console.error('测试过程中出错:', error);
  }
}

testBaziAPI();