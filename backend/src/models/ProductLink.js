import { DataTypes } from 'sequelize';
import sequelize from '../config/database.js';

const ProductLink = sequelize.define('ProductLink', {
  code: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  productId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Products',
      key: 'id'
    }
  },
  agentId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'Users',
      key: 'id'
    }
  },
  commissionRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false,
    defaultValue: 80, // 默认分润比例80%
    get() {
      const value = this.getDataValue('commissionRate');
      return value === null ? null : Number(value);
    }
  },
  clicks: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  sales: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  totalCommission: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    get() {
      const value = this.getDataValue('totalCommission');
      return value === null ? null : Number(value);
    }
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active'
  }
}, {
  indexes: [
    {
      unique: true,
      fields: ['code']
    },
    {
      fields: ['productId']
    },
    {
      fields: ['agentId']
    }
  ]
});

export default ProductLink;
