import { User } from '../models/index.js';

/**
 * 计算订单佣金分配
 * @param {Object} agent - 代理用户对象
 * @param {number} orderAmount - 订单金额
 * @returns {Promise<Object>} - 返回佣金分配结果
 */
export async function calculateCommissions(agent, orderAmount) {
  console.log('开始计算佣金，输入参数：', {
    agent: agent ? {
      id: agent.id,
      level: agent.level,
      commissionRate: agent.commissionRate,
      parentAgentId: agent.parentAgentId
    } : null,
    orderAmount
  });

  if (!agent) {
    throw new Error('代理信息不存在');
  }

  orderAmount = Number(orderAmount);
  if (isNaN(orderAmount) || orderAmount <= 0) {
    throw new Error(`无效的订单金额: ${orderAmount}`);
  }

  let agentCommission = 0;
  let parentAgentCommission = 0;
  let adminCommission = 0;

  try {
    if (agent.level === 0) {
      // 管理员订单，全部金额作为管理员佣金
      adminCommission = orderAmount;
      console.log('管理员佣金计算详情：', {
        orderAmount,
        adminCommission
      });
    }
     else if (agent.level === 2) {
      // 获取上级代理信息
      const parentAgent = await User.findByPk(agent.parentAgentId);
      if (!parentAgent) {
        throw new Error(`找不到上级代理 (parentAgentId: ${agent.parentAgentId})`);
      }

      if (!parentAgent.commissionRate) {
        throw new Error(`上级代理佣金率未设置 (parentAgentId: ${parentAgent.id})`);
      }

      // 使用上级代理的佣金率计算总佣金
      // 将佣金率从整数百分比转换为小数
      const parentRate = parentAgent.commissionRate / 100;
      const totalCommission = orderAmount * parentRate;
      
      // 二级代理获得总佣金中的指定比例
      const agentRate = agent.commissionRate / 100;
      agentCommission = totalCommission * agentRate;
      parentAgentCommission = totalCommission - agentCommission;

      // 管理员佣金为订单金额减去代理商佣金
      adminCommission = orderAmount - agentCommission - parentAgentCommission;

      console.log('二级代理佣金计算详情：', {
        orderAmount,
        parentAgentRate: parentAgent.commissionRate,
        agentRate: agent.commissionRate,
        totalCommission,
        agentCommission,
        parentAgentCommission,
        adminCommission
      });
    } else if (agent.level === 1) {
      if (!agent.commissionRate) {
        throw new Error(`代理佣金率未设置 (agentId: ${agent.id})`);
      }

      // 一级代理获得全部佣金
      // 将佣金率从整数百分比转换为小数
      const agentRate = agent.commissionRate / 100;
      agentCommission = orderAmount * agentRate;
      // 管理员佣金为订单金额减去代理商佣金
      adminCommission = orderAmount - agentCommission;

      console.log('一级代理佣金计算详情：', {
        orderAmount,
        agentRate: agent.commissionRate,
        agentCommission,
        adminCommission
      });
    } else {
      throw new Error(`无效的代理级别: ${agent.level}`);
    }

    const result = {
      agentCommission: Number(agentCommission.toFixed(2)),
      parentAgentCommission: Number(parentAgentCommission.toFixed(2)),
      adminCommission: Number(adminCommission.toFixed(2))
    };

    console.log('佣金计算最终结果：', result);
    return result;
  } catch (error) {
    console.error('佣金计算错误：', error);
    throw error;
  }
};