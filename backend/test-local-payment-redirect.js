/**
 * 测试本地环境八字产品支付跳转逻辑
 * 验证支付成功后是否正确跳转到八字报告页面
 */

import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载本地环境变量（开发环境）
dotenv.config({ path: path.join(__dirname, '.env.develop') });

console.log('=== 本地环境八字产品支付跳转测试 ===\n');

// 1. 检查本地环境变量配置
console.log('1. 本地环境变量检查:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || '未设置'}`);
console.log(`FRONTEND_URL: ${process.env.FRONTEND_URL || '未设置'}`);
console.log(`PAID_RETURN_URL: ${process.env.PAID_RETURN_URL || '未设置'}`);
console.log(`PAID_RETURN_URL2: ${process.env.PAID_RETURN_URL2 || '未设置'}`);
console.log();

// 2. 模拟本地环境的支付回调处理
function simulateLocalPaymentCallback(orderId, productTitle, productType) {
    console.log(`2. 模拟本地支付回调处理:`);
    console.log(`订单ID: ${orderId}`);
    console.log(`产品标题: ${productTitle}`);
    console.log(`产品类型: ${productType}`);
    
    let redirectUrl;
    
    // 模拟 payment.js 中的本地环境跳转逻辑
    if (productType === 'service' && (productTitle.includes('八字') || productTitle.includes('命理'))) {
        // 八字服务 - 检查是否有FRONTEND_URL配置
        if (process.env.FRONTEND_URL) {
            redirectUrl = `${process.env.FRONTEND_URL}/bazi-report?orderId=${orderId}`;
            console.log(`✅ 八字产品 - 跳转到报告页面`);
        } else {
            // 如果没有FRONTEND_URL，可能会跳转到默认地址
            redirectUrl = process.env.PAID_RETURN_URL;
            console.log(`❌ 八字产品 - 缺少FRONTEND_URL，跳转到默认页面`);
        }
    } else {
        // 其他产品跳转到默认页面
        redirectUrl = process.env.PAID_RETURN_URL;
        console.log(`📦 其他产品 - 跳转到默认页面`);
    }
    
    console.log(`跳转URL: ${redirectUrl}`);
    
    // 判断跳转结果
    if (redirectUrl && redirectUrl.includes('/bazi-report')) {
        console.log(`🎉 跳转正确 - 八字报告页面`);
        return 'SUCCESS';
    } else if (redirectUrl && (redirectUrl.includes('baidu.com') || redirectUrl.includes('pan.baidu'))) {
        console.log(`❌ 跳转错误 - 百度网盘地址`);
        return 'ERROR_BAIDU';
    } else if (redirectUrl && redirectUrl.includes('localhost')) {
        console.log(`✅ 跳转正确 - 本地前端地址`);
        return 'SUCCESS_LOCAL';
    } else {
        console.log(`⚠️  跳转未知 - ${redirectUrl}`);
        return 'UNKNOWN';
    }
}

// 3. 测试不同场景
console.log('3. 测试本地环境跳转场景:');
console.log();

// 测试八字产品
const result1 = simulateLocalPaymentCallback('TEST001', '玄易八字命盘', 'service');
console.log();

const result2 = simulateLocalPaymentCallback('TEST002', '专业八字命理查询服务', 'service');
console.log();

// 测试其他产品
const result3 = simulateLocalPaymentCallback('TEST003', '儿童纪录片', 'physical');
console.log();

// 4. 检查本地前端服务状态
console.log('4. 本地前端服务检查:');
const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
console.log(`预期前端地址: ${frontendUrl}`);

// 5. 生成测试报告
console.log('5. 测试结果汇总:');
const baziResults = [result1, result2];
const successCount = baziResults.filter(r => r === 'SUCCESS' || r === 'SUCCESS_LOCAL').length;
const errorCount = baziResults.filter(r => r === 'ERROR_BAIDU').length;

console.log(`八字产品测试: ${baziResults.length} 个`);
console.log(`成功跳转: ${successCount} 个`);
console.log(`错误跳转(百度网盘): ${errorCount} 个`);

if (errorCount === 0 && successCount === baziResults.length) {
    console.log('\n🎉 本地环境八字产品支付跳转测试通过！');
    console.log('八字产品支付后会正确跳转到报告页面。');
} else if (errorCount > 0) {
    console.log('\n❌ 本地环境八字产品支付跳转测试失败！');
    console.log('八字产品支付后仍然跳转到百度网盘，需要检查配置。');
} else {
    console.log('\n⚠️  本地环境八字产品支付跳转测试结果不确定。');
}

// 6. 提供修复建议
if (!process.env.FRONTEND_URL) {
    console.log('\n💡 修复建议:');
    console.log('1. 在 .env 文件中添加: FRONTEND_URL=http://localhost:3000');
    console.log('2. 重启后端服务以应用环境变量更改');
}

console.log('\n=== 本地环境测试完成 ===');