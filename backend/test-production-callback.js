import fetch from 'node-fetch';

async function testProductionCallback() {
  console.log('🔍 测试生产环境支付回调逻辑');
  console.log('=' .repeat(50));
  
  try {
    // 1. 测试生产环境的产品信息
    console.log('\n📋 获取生产环境八字产品信息...');
    const response = await fetch('https://ye.bzcy.xyz/api/products');
    
    if (!response.ok) {
      console.log('❌ 无法访问生产环境API，状态码:', response.status);
      console.log('响应内容:', await response.text());
      return;
    }
    
    const data = await response.json();
    console.log('✅ 成功获取产品列表');
    
    // 查找八字产品
    const baziProduct = data.data?.find(p => p.title && p.title.includes('八字'));
    if (!baziProduct) {
      console.log('❌ 生产环境中未找到八字产品');
      console.log('可用产品:', data.data?.map(p => p.title) || '无');
      return;
    }
    
    console.log('✅ 找到八字产品:', {
      id: baziProduct.id,
      title: baziProduct.title,
      type: baziProduct.type,
      status: baziProduct.status
    });
    
    // 2. 模拟支付回调逻辑
    console.log('\n🔄 模拟生产环境支付回调逻辑...');
    
    const productTitle = baziProduct.title;
    const productType = baziProduct.type;
    
    // 生产环境的判断逻辑
    const isBaziService = productType === 'service' && 
                         (productTitle.includes('八字') || productTitle.includes('命理'));
    
    const isDeepseekCourse = productTitle.includes('Deepseek') || 
                            productTitle.includes('大模型') || 
                            productTitle.includes('AI');
    
    console.log('产品标题:', productTitle);
    console.log('产品类型:', productType);
    console.log('是否八字服务:', isBaziService);
    console.log('是否Deepseek课程:', isDeepseekCourse);
    
    // 3. 模拟跳转地址生成（生产环境配置）
    const FRONTEND_URL = 'https://ye.bzcy.xyz';
    const PAID_RETURN_URL = 'https://pan.baidu.com/s/1l8Q8zYJUtYUC1fKG32FM0A?pwd=qbci';
    const PAID_RETURN_URL2 = 'https://pan.quark.cn/s/59c6d1f5973e';
    
    let redirectUrl;
    let productTypeDesc;
    
    if (isBaziService) {
      redirectUrl = `${FRONTEND_URL}/bazi-report?orderId=test-order-123`;
      productTypeDesc = '八字查询服务';
    } else if (isDeepseekCourse) {
      redirectUrl = PAID_RETURN_URL2;
      productTypeDesc = 'Deepseek AI课程';
    } else {
      redirectUrl = PAID_RETURN_URL;
      productTypeDesc = '儿童纪录片';
    }
    
    console.log('\n🎯 生产环境跳转结果:');
    console.log('产品类型描述:', productTypeDesc);
    console.log('跳转地址:', redirectUrl);
    
    // 4. 验证结果
    console.log('\n✅ 验证结果:');
    if (isBaziService && redirectUrl.includes('/bazi-report')) {
      console.log('🎉 SUCCESS: 生产环境逻辑正确，应该跳转到八字报告页面');
    } else {
      console.log('❌ ERROR: 生产环境逻辑有问题');
      console.log('可能的原因:');
      console.log('- 生产环境代码版本过旧');
      console.log('- 环境变量配置不正确');
      console.log('- 产品类型判断逻辑有误');
    }
    
    // 5. 检查生产环境是否有最新的支付回调代码
    console.log('\n🔧 建议检查项目:');
    console.log('1. 确认生产环境代码已更新到最新版本');
    console.log('2. 确认生产环境的FRONTEND_URL环境变量正确设置');
    console.log('3. 检查生产环境服务器是否重启应用了新配置');
    console.log('4. 查看生产环境的支付回调日志');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testProductionCallback(); 