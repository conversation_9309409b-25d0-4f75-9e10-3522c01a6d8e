/**
 * 委托平台网站功能自动化测试脚本
 * 
 * 该脚本将全面测试网站的各项功能，包括：
 * 1. 用户认证 - 注册、登录、获取个人资料
 * 2. 产品管理 - 创建、查询、更新、删除产品
 * 3. 订单流程 - 创建订单、支付、查询
 * 4. 佣金系统 - 佣金计算、查询
 * 5. 提现功能 - 申请提现、查询提现
 * 6. 代理功能 - 生成邀请链接、注册代理
 * 
 * 使用方法：
 * 1. 确保已安装依赖：npm install axios chalk cli-table3
 * 2. 运行脚本：node test-website.js
 * 3. 可选参数：
 *    - --url=https://example.com 指定测试网站URL
 *    - --admin 包含管理员功能测试
 *    - --verbose 显示详细日志
 */

import axios from 'axios';
import chalk from 'chalk';
import Table from 'cli-table3';
import { promises as fs } from 'fs';

// 解析命令行参数
const args = process.argv.slice(2).reduce((acc, arg) => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.substring(2).split('=');
    acc[key] = value || true;
  }
  return acc;
}, {});

// 配置
const config = {
  baseURL: args.url || 'https://ye.bzcy.xyz',
  apiPath: '/api',
  verbose: !!args.verbose,
  includeAdmin: !!args.admin,
  timeout: 10000, // 请求超时时间（毫秒）
};

// 测试账号
const testAccounts = {
  admin: {
    username: 'admin',
    password: 'admin123',
  },
  agent: {
    username: 'agent1',
    password: 'agent123',
  },
  user: {
    username: 'agent2',
    password: 'agent123',
  },
  // 创建测试账号使用
  newUser: {
    username: `testuser_${Date.now().toString().slice(-6)}`,
    password: 'Test123456',
    email: `test${Date.now().toString().slice(-6)}@example.com`,
    phone: `1350000${Date.now().toString().slice(-4)}`,
    name: '测试用户',
  }
};

// HTTP客户端
const api = axios.create({
  baseURL: `${config.baseURL}${config.apiPath}`,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 测试结果统计
const testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  skipped: 0,
  details: [],
};

// 用户令牌存储
const tokens = {};

// 测试数据存储
const testData = {
  products: [],
  orders: [],
  commissions: [],
  withdrawals: [],
  inviteLinks: [],
  categories: [],
};

// 辅助函数
const logger = {
  info: (msg) => console.log(chalk.blue(`[信息] ${msg}`)),
  success: (msg) => console.log(chalk.green(`[成功] ${msg}`)),
  error: (msg) => console.log(chalk.red(`[错误] ${msg}`)),
  warn: (msg) => console.log(chalk.yellow(`[警告] ${msg}`)),
  debug: (msg) => { if (config.verbose) console.log(chalk.gray(`[调试] ${msg}`)); },
};

// 测试用例执行器
async function runTest(name, fn, options = {}) {
  const { skip = false, only = false } = options;
  
  if (skip) {
    logger.warn(`跳过测试: ${name}`);
    testResults.skipped++;
    testResults.total++;
    testResults.details.push({ name, result: 'skipped', error: '已跳过' });
    return;
  }
  
  logger.info(`开始测试: ${name}`);
  testResults.total++;
  
  try {
    await fn();
    logger.success(`通过测试: ${name}`);
    testResults.passed++;
    testResults.details.push({ name, result: 'passed' });
  } catch (error) {
    logger.error(`测试失败: ${name}`);
    logger.error(`错误信息: ${error.message}`);
    if (config.verbose && error.response) {
      logger.debug(`错误响应: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    testResults.failed++;
    testResults.details.push({ 
      name, 
      result: 'failed', 
      error: error.message,
      details: error.response ? error.response.data : undefined
    });
  }
}

// 断言函数
function assert(condition, message) {
  if (!condition) {
    throw new Error(message || '断言失败');
  }
}

// 生成测试报告
function generateReport() {
  console.log('\n');
  logger.info('测试完成，生成报告...');
  
  // 创建测试结果表格
  const table = new Table({
    head: ['测试结果', '数量'],
    colWidths: [20, 10],
    style: {
      head: ['cyan'],
    },
  });
  
  table.push(
    ['总测试数', testResults.total],
    ['通过', testResults.passed],
    ['失败', testResults.failed],
    ['跳过', testResults.skipped]
  );
  
  console.log(table.toString());
  
  // 如果有失败的测试，显示详细信息
  if (testResults.failed > 0) {
    const failureTable = new Table({
      head: ['测试名称', '错误信息'],
      colWidths: [40, 80],
      wordWrap: true,
      style: {
        head: ['red'],
      },
    });
    
    testResults.details
      .filter(detail => detail.result === 'failed')
      .forEach(detail => {
        failureTable.push([
          detail.name,
          detail.error + (detail.details ? `\n响应: ${JSON.stringify(detail.details, null, 2)}` : '')
        ]);
      });
    
    console.log('\n失败测试详情:');
    console.log(failureTable.toString());
  }
  
  // 输出最终结果
  if (testResults.failed === 0) {
    logger.success('所有测试通过！✅');
  } else {
    logger.error(`测试失败！有 ${testResults.failed} 个测试未通过 ❌`);
  }
  
  // 将测试结果写入文件
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: testResults.total,
      passed: testResults.passed,
      failed: testResults.failed,
      skipped: testResults.skipped,
    },
    details: testResults.details,
    config: {
      baseURL: config.baseURL,
      includeAdmin: config.includeAdmin,
    },
  };
  
  fs.writeFile(
    `test-report-${new Date().toISOString().replace(/:/g, '-')}.json`,
    JSON.stringify(reportData, null, 2)
  ).then(() => {
    logger.info('测试报告已保存到文件');
  }).catch(err => {
    logger.error(`无法保存测试报告: ${err.message}`);
  });
}

// 测试套件
async function runAllTests() {
  logger.info(`开始测试网站: ${config.baseURL}`);
  logger.info('正在初始化测试环境...');
  
  try {
    // 1. 身份验证测试
    await runTest('用户登录 - 管理员', async () => {
      const response = await api.post('/auth/login', testAccounts.admin);
      assert(response.status === 200, '登录失败');
      assert(response.data.token, '未返回token');
      tokens.admin = response.data.token;
      
      // 设置后续请求的认证头
      api.defaults.headers.common['Authorization'] = `Bearer ${tokens.admin}`;
    });
    
    // 仅在包含管理员测试时执行
    if (config.includeAdmin) {
      await runTest('获取管理员个人资料', async () => {
        const response = await api.get('/auth/profile');
        assert(response.status === 200, '获取个人资料失败');
        assert(response.data.user.role === 'admin', '用户角色不是管理员');
      });
      
      // 类别管理测试
      await runTest('获取所有类别', async () => {
        const response = await api.get('/categories');
        assert(response.status === 200, '获取类别失败');
        testData.categories = response.data.categories || [];
        logger.debug(`获取到 ${testData.categories.length} 个类别`);
      });
      
      // 如果没有类别，创建测试类别
      if (testData.categories.length === 0) {
        await runTest('创建测试类别', async () => {
          const response = await api.post('/categories', {
            name: '测试类别',
            description: '这是一个测试类别'
          });
          assert(response.status === 201, '创建类别失败');
          testData.categories.push(response.data.category);
        });
      }
      
      // 产品管理测试
      await runTest('获取所有产品', async () => {
        const response = await api.get('/products');
        assert(response.status === 200, '获取产品失败');
        testData.products = response.data.products || [];
        logger.debug(`获取到 ${testData.products.length} 个产品`);
      });
      
      // 如果没有产品，创建测试产品
      if (testData.products.length === 0) {
        await runTest('创建测试产品', async () => {
          const response = await api.post('/products', {
            name: '测试产品',
            description: '这是一个测试产品',
            price: 99.99,
            stock: 100,
            categoryId: testData.categories[0]?.id || null,
            commissionRate: 10, // 10% 佣金率
            status: 'active'
          });
          assert(response.status === 201, '创建产品失败');
          testData.products.push(response.data.product);
        });
      }
      
      // 用户管理测试
      await runTest('获取所有用户', async () => {
        const response = await api.get('/users');
        assert(response.status === 200, '获取用户列表失败');
        assert(Array.isArray(response.data.users), '用户列表格式错误');
      });
      
      // 尝试注册新测试用户
      await runTest('注册测试用户', async () => {
        try {
          const response = await api.post('/auth/register', testAccounts.newUser);
          assert(response.status === 201, '注册用户失败');
          logger.debug(`成功注册测试用户: ${testAccounts.newUser.username}`);
        } catch (error) {
          // 如果用户已存在，尝试使用另一个用户名
          if (error.response && error.response.status === 400) {
            testAccounts.newUser.username = `testuser_${Date.now().toString().slice(-6)}`;
            const response = await api.post('/auth/register', testAccounts.newUser);
            assert(response.status === 201, '使用新用户名注册仍然失败');
          } else {
            throw error;
          }
        }
      });
      
      // 生成邀请链接测试
      await runTest('生成代理邀请链接', async () => {
        const response = await api.post('/auth/generate-invite-link', {
          baseUrl: config.baseURL
        });
        assert(response.status === 200, '生成邀请链接失败');
        assert(response.data.inviteLink, '未返回邀请链接');
        testData.inviteLinks.push(response.data.inviteLink);
        logger.debug(`生成邀请链接: ${response.data.inviteLink}`);
      });
    }
    
    // 2. 普通用户测试
    // 先尝试使用已有测试用户登录
    await runTest('用户登录 - 普通用户', async () => {
      try {
        const response = await api.post('/auth/login', testAccounts.user);
        assert(response.status === 200, '登录失败');
        tokens.user = response.data.token;
      } catch (error) {
        // 如果登录失败，可能是测试用户不存在，尝试使用上面创建的新用户
        logger.warn('测试用户登录失败，尝试使用新创建的用户');
        const response = await api.post('/auth/login', testAccounts.newUser);
        assert(response.status === 200, '使用新创建的用户登录失败');
        tokens.user = response.data.token;
      }
      
      // 更新认证头
      api.defaults.headers.common['Authorization'] = `Bearer ${tokens.user}`;
    });
    
    await runTest('获取个人资料', async () => {
      const response = await api.get('/auth/profile');
      assert(response.status === 200, '获取个人资料失败');
    });
    
    // 3. 产品和订单测试
    await runTest('浏览产品列表', async () => {
      const response = await api.get('/products');
      assert(response.status === 200, '获取产品列表失败');
      assert(Array.isArray(response.data.products), '产品列表格式错误');
      
      // 如果有产品，保存第一个产品用于后续测试
      if (response.data.products.length > 0) {
        testData.products = response.data.products;
      }
    });
    
    if (testData.products.length > 0) {
      await runTest('查看产品详情', async () => {
        const productId = testData.products[0].id;
        const response = await api.get(`/products/${productId}`);
        assert(response.status === 200, '获取产品详情失败');
        assert(response.data.product.id === productId, '产品ID不匹配');
      });
      
      // 4. 生成推广链接测试
      await runTest('生成产品推广链接', async () => {
        const productId = testData.products[0].id;
        const response = await api.post('/product-links', { productId });
        assert(response.status === 200 || response.status === 201, '生成推广链接失败');
        assert(response.data.productLink, '未返回推广链接');
        testData.productLinks = [response.data.productLink];
        logger.debug(`生成产品推广链接: ${response.data.productLink.code}`);
      });
      
      // 5. 创建订单测试（如果环境支持）
      await runTest('创建订单', async () => {
        const productId = testData.products[0].id;
        let referralCode = null;
        
        // 如果有推广链接则使用
        if (testData.productLinks && testData.productLinks.length > 0) {
          referralCode = testData.productLinks[0].code;
        }
        
        const orderData = {
          productId,
          quantity: 1,
          referralCode,
          contactInfo: {
            name: '测试客户',
            phone: '13800138000',
            email: '<EMAIL>',
            address: '测试地址'
          }
        };
        
        const response = await api.post('/orders', orderData);
        assert(response.status === 201, '创建订单失败');
        assert(response.data.order, '未返回订单信息');
        testData.orders.push(response.data.order);
        logger.debug(`创建订单成功: ${response.data.order.id}`);
      }, { skip: !config.includeAdmin }); // 跳过真实支付测试，除非明确要求
    }
    
    // 6. 佣金测试
    await runTest('查询佣金记录', async () => {
      const response = await api.get('/commissions');
      assert(response.status === 200, '获取佣金记录失败');
      testData.commissions = response.data.commissions || [];
      logger.debug(`获取到 ${testData.commissions.length} 条佣金记录`);
    });
    
    // 7. 提现测试（跳过实际提现操作）
    await runTest('查询提现记录', async () => {
      const response = await api.get('/withdrawals');
      assert(response.status === 200, '获取提现记录失败');
      testData.withdrawals = response.data.withdrawals || [];
      logger.debug(`获取到 ${testData.withdrawals.length} 条提现记录`);
    });
    
    // 8. 如果有管理员权限，测试获取仪表板数据
    if (config.includeAdmin && tokens.admin) {
      // 切换回管理员账号
      api.defaults.headers.common['Authorization'] = `Bearer ${tokens.admin}`;
      
      await runTest('获取仪表板数据', async () => {
        const response = await api.get('/dashboard');
        assert(response.status === 200, '获取仪表板数据失败');
        assert(response.data.stats, '未返回统计数据');
      });
    }
    
    // 9. API状态检查
    await runTest('API健康检查', async () => {
      // 移除授权头以测试公开接口
      delete api.defaults.headers.common['Authorization'];
      
      try {
        // 尝试访问产品列表（公开接口）
        const response = await api.get('/products');
        assert(response.status === 200, 'API健康检查失败');
      } catch (error) {
        throw new Error(`API健康检查失败: ${error.message}`);
      }
    });
    
  } catch (error) {
    logger.error(`测试过程中发生严重错误: ${error.message}`);
    if (error.stack) {
      logger.debug(`错误堆栈: ${error.stack}`);
    }
  } finally {
    // 生成测试报告
    generateReport();
  }
}

// 运行所有测试
logger.info('委托平台网站自动化测试工具');
logger.info(`目标网站: ${config.baseURL}`);
logger.info(`包含管理员测试: ${config.includeAdmin ? '是' : '否'}`);
logger.info(`详细日志: ${config.verbose ? '开启' : '关闭'}`);
logger.info('---------------------------------------');

runAllTests();
