import express from 'express';
import { Order, Commission, ProductLink, User, Product } from '../models/index.js';
import sequelize from '../config/database.js';
import { verifyPayment } from '../services/alipay.js';
import { calculateCommissions } from '../utils/commissionUtils.js';

const router = express.Router();

// 同步回调处理
router.get('/alipay/return', async (req, res) => {
  try {
    console.log('【支付宝同步回调】开始处理...');
    console.log('请求参数:', JSON.stringify(req.query, null, 2));
    
    // 检查是否是从支付宝跳转过来的请求
    // 支付宝同步回调会带有这些参数
    const isFromAlipay = req.query.out_trade_no && req.query.trade_no && req.query.total_amount;
    
    if (!isFromAlipay) {
      console.log('【警告】直接访问同步回调地址，非支付宝跳转');
      return res.send(`
        <html>
          <head>
            <title>支付回调接口</title>
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              .container { max-width: 600px; margin: 0 auto; }
              h1 { color: #333; }
              p { color: #666; line-height: 1.6; }
              .warning { color: #e74c3c; }
            </style>
          </head>
          <body>
            <div class="container">
              <h1>支付回调接口</h1>
              <p class="warning">这是支付系统的技术接口，不是直接访问的页面。</p>
              <p>如果您是在支付过程中被重定向到此页面，请联系客服。</p>
              <p>如果您是开发人员，请确保通过正确的支付流程访问此接口。</p>
            </div>
          </body>
        </html>
      `);
    }

    // 为同步回调添加 method 参数
    const params = {
      ...req.query,
      method: 'alipay.trade.page.pay.return'
    };
    
    // 验证支付结果 - 注意：同步回调可能因为参数编码等原因导致验证失败，但订单其实已经支付成功
    // 这里我们依然验证签名，但即使失败也继续处理订单
    const isValid = await verifyPayment(params);
    if (!isValid) {
      console.log('【支付宝同步回调】签名验证失败，但继续处理订单，因为异步通知会再次验证');
    }
    console.log('【支付宝同步回调】支付验证成功');

    // 查找订单
    const orderId = req.query.out_trade_no;
    console.log('【支付宝同步回调】查找订单:', orderId);

    // 获取订单信息
    const order = await Order.findOne({
      where: { id: orderId },
      include: [{
        model: ProductLink,
        as: 'productLink',
        include: [{
          model: Product,
          as: 'product'
        }, {
          model: User,
          as: 'linkAgent',
          include: [{
            model: User,
            as: 'parentAgent'
          }]
        }]
      }, {
        model: Product,
        as: 'product'
      }]
    });

    if (!order) {
      console.error('【支付宝同步回调】订单不存在:', orderId);
      return res.redirect(`${process.env.PAID_RETURN_URL}?status=fail&reason=order_not_found`);
    }

    // 打印订单详细信息用于调试
    console.log('【支付宝同步回调】订单信息:', {
      orderId: order.id,
      amount: order.amount,
      agent: order.productLink?.linkAgent ? {
        id: order.productLink.linkAgent.id,
        level: order.productLink.linkAgent.level,
        commissionRate: order.productLink.linkAgent.commissionRate,
        parentAgent: order.productLink.linkAgent.parentAgent ? {
          id: order.productLink.linkAgent.parentAgent.id,
          commissionRate: order.productLink.linkAgent.parentAgent.commissionRate
        } : null
      } : null
    });

    // 如果订单已经处理过，直接跳转成功页面
    if (order.status === 'paid') {
      console.log('【支付宝同步回调】订单已处理过:', orderId);
      
      // 检查产品类型并决定跳转地址
      console.log('【支付宝同步回调】订单完整信息:', JSON.stringify(order, null, 2));
      console.log('【支付宝同步回调】产品链接信息:', JSON.stringify(order.productLink, null, 2));
      console.log('【支付宝同步回调】直接产品信息:', JSON.stringify(order.product, null, 2));

      // 优先使用直接关联的产品信息，如果没有则使用产品链接的产品信息
      const productTitle = order.product?.title || order.productLink?.product?.title || '';
      const productType = order.product?.type || order.productLink?.product?.type || '';
      console.log('【支付宝同步回调】提取的产品标题:', productTitle);
      console.log('【支付宝同步回调】提取的产品类型:', productType);
      
      // 判断产品类型
      let redirectUrl;
      let productTypeDesc;
      
      // 1. 检查是否是八字查询服务
      const isBaziService = productType === 'service' &&
                           (productTitle.includes('八字') || productTitle.includes('命理'));

      // 2. 检查是否是起名业务服务
      const isNamingService = productType === 'naming' ||
                             productTitle.includes('起名') ||
                             productTitle.includes('专业起名服务');

      // 3. 检查是否是Deepseek AI课程
      const hasDeepseek = productTitle.includes('Deepseek');
      const hasAI = productTitle.includes('AI');
      const hasModel = productTitle.includes('大模型');
      const isDeepseekCourse = hasDeepseek || hasModel || hasAI;

      console.log('【支付宝同步回调】关键词检查结果:', {
        hasDeepseek,
        hasAI,
        hasModel,
        isBaziService,
        isNamingService
      });

      // 根据产品类型决定跳转地址
      if (isBaziService) {
        redirectUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=${orderId}&status=success`;
        productTypeDesc = '八字查询服务';
      } else if (isNamingService) {
        // 起名业务直接跳转到企业微信客服页面
        const frontendUrl = process.env.FRONTEND_URL || 'https://ye.bzcy.xyz';

        // 从订单的customerAddress字段中提取baziReportId
        let baziReportId = orderId;
        if (order.customerAddress && order.customerAddress.includes('八字报告ID:')) {
          baziReportId = order.customerAddress.replace('八字报告ID: ', '');
          console.log('【支付宝同步回调】从订单地址字段提取八字报告ID:', baziReportId);
        } else if (orderId.startsWith('NAMING')) {
          // 兼容旧的订单号格式：NAMING{baziReportId}{timestamp}
          const match = orderId.match(/^NAMING(.+?)(\d{6})$/);
          if (match && match[1]) {
            baziReportId = match[1];
            console.log('【支付宝同步回调】从起名订单号提取八字报告ID:', baziReportId);
          }
        }

        const baziReportUrl = `${frontendUrl}/bazi-report?orderId=${baziReportId}`;
        const message = `您好！我已完成起名服务支付，这是我的八字报告链接：${baziReportUrl}，请为我提供专业的起名服务。订单号：${orderId}`;
        const encodedMessage = encodeURIComponent(message);
        redirectUrl = `https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd?msg=${encodedMessage}`;
        productTypeDesc = '起名业务服务';
        console.log('【支付宝同步回调】起名业务跳转到企业微信客服，消息:', message);
      } else if (isDeepseekCourse) {
        redirectUrl = `${process.env.PAID_RETURN_URL2}?status=success&orderId=${orderId}`;
        productTypeDesc = 'Deepseek AI课程';
      } else {
        redirectUrl = `${process.env.PAID_RETURN_URL}?status=success&orderId=${orderId}`;
        productTypeDesc = '儿童纪录片';
      }

      console.log('【支付宝同步回调】产品类型判断结果:', productTypeDesc);
      console.log('【支付宝同步回调】环境变量值:', {
        PAID_RETURN_URL: process.env.PAID_RETURN_URL,
        PAID_RETURN_URL2: process.env.PAID_RETURN_URL2,
        FRONTEND_URL: process.env.FRONTEND_URL
      });
      console.log('【支付宝同步回调】将要跳转到:', redirectUrl);

      return res.redirect(redirectUrl);
    }

    // 开启事务
    const transaction = await sequelize.transaction();
    console.log('【支付宝同步回调】开启事务');

    try {
      // 检查是否有代理信息
      const agent = order.productLink?.linkAgent;

      let commissions;
      if (agent) {
        console.log('【支付宝同步回调】代理信息:', {
          agentId: agent.id,
          level: agent.level,
          commissionRate: agent.commissionRate,
          parentAgentId: agent.parentAgentId
        });

        // 计算佣金
        commissions = await calculateCommissions(agent, order.amount);

        // 打印详细的佣金计算结果
        console.log('【支付宝同步回调】佣金计算结果:', {
          orderAmount: order.amount,
          commissions,
          agent: {
            id: agent.id,
            level: agent.level,
            commissionRate: agent.commissionRate
          }
        });

        if (!commissions || typeof commissions.agentCommission === 'undefined') {
          throw new Error('佣金计算结果无效');
        }
      } else {
        console.log('【支付宝同步回调】无代理信息，使用默认佣金设置');
        // 对于没有代理的订单（如起名业务），使用默认佣金设置
        commissions = {
          agentCommission: 0,
          parentAgentCommission: 0,
          adminCommission: order.adminCommission || 0
        };
      }

      // 更新订单状态和佣金信息
      await order.update({
        status: 'paid',
        paidAt: new Date(),
        paymentId: req.query.trade_no,
        agentCommission: commissions.agentCommission, // 二级代理佣金
        parentAgentCommission: commissions.parentAgentCommission, // 一级代理佣金
        adminCommission: commissions.adminCommission
      }, { transaction });

      console.log('【支付宝同步回调】订单状态和佣金已更新:', {
        orderId: order.id,
        status: order.status,
        paidAt: order.paidAt,
        paymentId: order.paymentId,
        agentCommission: commissions.agentCommission,
        parentAgentCommission: commissions.parentAgentCommission,
        adminCommission: commissions.adminCommission
      });

      // 创建二级代理佣金记录（仅当有代理时）
      if (agent && commissions.agentCommission > 0) {
        const commission = await Commission.create({
          orderId: order.id,
          agentId: order.productLink.agentId,
          amount: commissions.agentCommission,
          status: 'pending', // 设置为待结算状态，一天后自动变为可提现
          type: 'agent'
        }, { transaction });

        console.log('【支付宝同步回调】二级代理佣金已创建:', {
          commissionId: commission.id,
          orderId: order.id,
          agentId: order.productLink.agentId,
          amount: commissions.agentCommission
        });
      } else {
        console.log('【支付宝同步回调】无代理或佣金为0，跳过佣金记录创建');
      }

      // 如果有一级代理，创建一级代理佣金记录
      if (agent && agent.parentAgent && commissions.parentAgentCommission > 0) {
        const parentCommissionRecord = await Commission.create({
          orderId: order.id,
          agentId: agent.parentAgent.id,
          amount: commissions.parentAgentCommission,
          status: 'pending', // 设置为待结算状态，一天后自动变为可提现
          type: 'parent'
        }, { transaction });

        console.log('【支付宝同步回调】一级代理佣金已创建:', {
          commissionId: parentCommissionRecord.id,
          orderId: order.id,
          agentId: agent.parentAgent.id,
          amount: commissions.parentAgentCommission
        });
      }

      // 更新推广链接的销量和总佣金（仅当有代理时）
      if (agent && order.productLink) {
        await order.productLink.increment('sales', { by: 1, transaction });
        await order.productLink.increment('totalCommission', { by: commissions.agentCommission, transaction });
        console.log('【支付宝同步回调】推广链接销量和佣金已更新');
      }

      // 检查是否是八字查询服务订单，如果是则自动生成报告
      if (order.orderType === 'service' && order.productLink?.product?.type === 'service') {
        console.log('【支付宝同步回调】检测到八字查询服务订单，准备生成报告');
        try {
          // 异步生成八字报告，不阻塞用户跳转
          const generateReportAsync = async () => {
            try {
              const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3005'}/api/bazi/orders/${order.id}/generate-report`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                }
              });
              
              if (response.ok) {
                console.log('【支付宝同步回调】八字报告生成成功');
              } else {
                console.error('【支付宝同步回调】八字报告生成失败:', await response.text());
              }
            } catch (error) {
              console.error('【支付宝同步回调】八字报告生成异常:', error);
            }
          };
          
          // 异步执行，不等待结果
          setImmediate(generateReportAsync);
        } catch (error) {
          console.error('【支付宝同步回调】启动八字报告生成失败:', error);
        }
      }

      // 提交事务
      await transaction.commit();
      console.log('【支付宝同步回调】事务提交成功');

      // 检查产品类型并决定跳转地址
      const productTitle = order.product?.title || order.productLink?.product?.title || '';
      const productType = order.product?.type || order.productLink?.product?.type || '';
      
      // 从请求中获取移动端支付的额外产品类型信息
      let extendParams = req.query.extend_params;
      if (typeof extendParams === 'string') {
        try {
          extendParams = JSON.parse(extendParams);
          console.log('【支付宝同步回调】解析extend_params:', extendParams);
        } catch (e) {
          console.log('【支付宝同步回调】extend_params解析失败:', e.message);
        }
      }
      
      // 判断产品类型
      let redirectUrl;
      let productTypeDesc;
      
      // 1. 检查是否是八字查询服务
      const isBaziService = productType === 'service' &&
                           (productTitle.includes('八字') || productTitle.includes('命理'));

      // 2. 检查是否是起名业务服务
      const isNamingService = productType === 'naming' ||
                             productTitle.includes('起名') ||
                             productTitle.includes('专业起名服务');

      // 3. 检查是否是Deepseek AI课程
      let isDeepseekCourse = false;
      if (extendParams && extendParams.sys_service_provider_id) {
        isDeepseekCourse = extendParams.sys_service_provider_id === 'deepseek_course';
        console.log('【支付宝异步回调】从移动端参数获取产品类型:', extendParams.sys_service_provider_id);
      } else {
        isDeepseekCourse = productTitle.includes('Deepseek') || productTitle.includes('大模型') || productTitle.includes('AI');
        console.log('【支付宝异步回调】从产品标题判断产品类型');
      }

      // 根据产品类型决定跳转地址
      if (isBaziService) {
        redirectUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=${orderId}&status=success`;
        productTypeDesc = '八字查询服务';
      } else if (isNamingService) {
        // 起名业务直接跳转到企业微信客服页面
        const frontendUrl = process.env.FRONTEND_URL || 'https://ye.bzcy.xyz';

        // 从订单的customerAddress字段中提取baziReportId
        let baziReportId = orderId;
        if (order.customerAddress && order.customerAddress.includes('八字报告ID:')) {
          baziReportId = order.customerAddress.replace('八字报告ID: ', '');
          console.log('【支付宝异步回调】从订单地址字段提取八字报告ID:', baziReportId);
        } else if (orderId.startsWith('NAMING')) {
          // 兼容旧的订单号格式：NAMING{baziReportId}{timestamp}
          const match = orderId.match(/^NAMING(.+?)(\d{6})$/);
          if (match && match[1]) {
            baziReportId = match[1];
            console.log('【支付宝异步回调】从起名订单号提取八字报告ID:', baziReportId);
          }
        }

        const baziReportUrl = `${frontendUrl}/bazi-report?orderId=${baziReportId}`;
        const message = `您好！我已完成起名服务支付，这是我的八字报告链接：${baziReportUrl}，请为我提供专业的起名服务。订单号：${orderId}`;
        const encodedMessage = encodeURIComponent(message);
        redirectUrl = `https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd?msg=${encodedMessage}`;
        productTypeDesc = '起名业务服务';
        console.log('【支付宝异步回调】起名业务跳转到企业微信客服，消息:', message);
      } else if (isDeepseekCourse) {
        redirectUrl = `${process.env.PAID_RETURN_URL2}?status=success&orderId=${orderId}`;
        productTypeDesc = 'Deepseek AI课程';
      } else {
        redirectUrl = `${process.env.PAID_RETURN_URL}?status=success&orderId=${orderId}`;
        productTypeDesc = '儿童纪录片';
      }

      console.log('【支付宝同步回调】产品标题:', productTitle);
      console.log('【支付宝同步回调】产品类型:', productType);
      console.log('【支付宝同步回调】产品类型描述:', productTypeDesc);
      console.log('【支付宝同步回调】跳转地址:', redirectUrl);

      // 跳转到成功页面
      res.redirect(redirectUrl);
    } catch (error) {
      // 回滚事务
      await transaction.rollback();
      console.error('【支付宝同步回调】事务回滚:', error);
      throw error;
    }
  } catch (error) {
    console.error('【支付宝同步回调】处理失败:', error);
    res.redirect(`${process.env.PAID_RETURN_URL}?status=fail&reason=system_error`);
  }
});

// 处理GET请求到异步通知URL的情况（仅用于测试/调试）
router.get('/alipay/notify', (req, res) => {
  console.log('【警告】收到GET请求到异步通知URL，这不是支付宝的标准行为');
  return res.status(405).send('异步通知URL只接受POST请求。如果您是开发人员，请使用POST方式测试此接口。');
});

// 异步通知处理（支付宝使用POST方式）
router.post('/alipay/notify', async (req, res) => {
  let transaction = null;
  try {
    console.log('【支付宝异步通知】开始处理...');
    console.log('请求参数:', JSON.stringify(req.body, null, 2));

    // 为异步通知添加 method 参数 - 修正为正确的异步通知方法名
    const params = {
      ...req.body,
      method: 'alipay.trade.wap.pay.notify' // 修正为正确的异步通知方法名
    };
    
    console.log('【支付宝异步通知】完整参数:', JSON.stringify(params, null, 2));
    console.log('【支付宝异步通知】当前环境:', process.env.NODE_ENV);

    // 验证支付结果
    const isValid = await verifyPayment(params);
    if (!isValid) {
      console.error('【支付宝异步通知】支付验证失败');
      return res.status(400).send('fail');
    }

    // 查找订单
    const orderId = req.body.out_trade_no;
    console.log('【支付宝异步通知】查找订单:', orderId);
    
    const order = await Order.findByPk(orderId, {
      include: [{
        model: ProductLink,
        as: 'productLink',
        include: [{
          model: Product,
          as: 'product'
        }, {
          model: User,
          as: 'linkAgent',
          include: [{
            model: User,
            as: 'parentAgent'
          }]
        }]
      }, {
        model: Product,
        as: 'product'
      }]
    });
    
    if (!order) {
      console.error('【支付宝异步通知】订单不存在:', orderId);
      return res.status(404).send('fail');
    }

    // 如果订单已经处理过，直接返回成功
    if (order.status === 'paid') {
      console.log('【支付宝异步通知】订单已处理过:', orderId);
      
      // 检查产品类型 - 增强判断逻辑
      const productTitle = order.product?.title || order.productLink?.product?.title || '';
      const productType = order.product?.type || order.productLink?.product?.type || '';
      const isDeepseekCourse = productTitle.includes('Deepseek') || productTitle.includes('大模型') || productTitle.includes('AI');
      const isNamingService = productType === 'naming' || productTitle.includes('起名') || productTitle.includes('专业起名服务');
      console.log('【支付宝异步通知】产品标题:', productTitle);
      console.log('【支付宝异步通知】产品类型:', productType);
      console.log('【支付宝异步通知】业务类型:', isNamingService ? '起名服务' : (isDeepseekCourse ? 'Deepseek AI课程' : '儿童纪录片'));
      
      return res.send('success');
    }

    // 开启事务
    transaction = await sequelize.transaction();
    console.log('【支付宝异步通知】开启事务');

    try {
      // 检查是否有代理信息
      const agent = order.productLink?.linkAgent;

      let commissions;
      if (agent) {
        console.log('【支付宝异步通知】代理信息:', {
          agentId: agent.id,
          level: agent.level,
          commissionRate: agent.commissionRate,
          parentAgentId: agent.parentAgentId
        });

        // 计算佣金
        commissions = await calculateCommissions(agent, order.amount);
      } else {
        console.log('【支付宝异步通知】无代理信息，使用默认佣金设置');
        // 对于没有代理的订单（如起名业务），使用默认佣金设置
        commissions = {
          agentCommission: 0,
          parentAgentCommission: 0,
          adminCommission: order.adminCommission || 0
        };
      }

      // 打印详细的佣金计算结果
      console.log('【支付宝异步通知】佣金计算结果:', {
        orderAmount: order.amount,
        commissions,
        agent: {
          id: agent.id,
          level: agent.level,
          commissionRate: agent.commissionRate
        }
      });

      if (!commissions || typeof commissions.agentCommission === 'undefined') {
        throw new Error('佣金计算结果无效');
      }

      // 检查产品类型并决定资源链接
      const productTitle = order.productLink?.product?.title || '';
      const productType = order.productLink?.product?.type || '';
      
      // 从请求中获取移动端支付的额外产品类型信息
      let extendParams = req.body.extend_params;
      if (typeof extendParams === 'string') {
        try {
          extendParams = JSON.parse(extendParams);
          console.log('【支付宝异步通知】解析extend_params:', extendParams);
        } catch (e) {
          console.log('【支付宝异步通知】extend_params解析失败:', e.message);
        }
      }
      
      // 判断产品类型
      let resourceUrl;
      let productTypeDesc;
      
      // 1. 检查是否是八字查询服务
      const isBaziService = productType === 'service' && 
                           (productTitle.includes('八字') || productTitle.includes('命理'));
      
      // 2. 检查是否是Deepseek AI课程
      let isDeepseekCourse = false;
      if (extendParams && extendParams.order_product_type) {
        isDeepseekCourse = extendParams.order_product_type === 'deepseek';
        console.log('【支付宝异步通知】从order_product_type参数获取产品类型:', extendParams.order_product_type);
      } else if (extendParams && extendParams.sys_service_provider_id) {
        isDeepseekCourse = extendParams.sys_service_provider_id === 'deepseek_course';
        console.log('【支付宝异步通知】从sys_service_provider_id参数获取产品类型:', extendParams.sys_service_provider_id);
      } else {
        isDeepseekCourse = productTitle.includes('Deepseek') || productTitle.includes('大模型') || productTitle.includes('AI');
        console.log('【支付宝异步通知】从产品标题判断产品类型:', productTitle);
      }
      
      // 根据产品类型决定资源链接
      if (isBaziService) {
        resourceUrl = `${process.env.FRONTEND_URL || 'https://ye.bzcy.xyz'}/bazi-report?orderId=${orderId}`;
        productTypeDesc = '八字查询服务';
      } else if (isDeepseekCourse) {
        resourceUrl = process.env.PAID_RETURN_URL2;
        productTypeDesc = 'Deepseek AI课程';
      } else {
        resourceUrl = process.env.PAID_RETURN_URL;
        productTypeDesc = '儿童纪录片';
      }
      
      console.log('【支付宝异步通知】产品标题:', productTitle);
      console.log('【支付宝异步通知】产品类型:', productType);
      console.log('【支付宝异步通知】产品类型描述:', productTypeDesc);
      console.log('【支付宝异步通知】资源链接:', resourceUrl);
      
      // 更新订单状态和佣金信息
      await order.update({
        status: 'paid',
        paidAt: new Date(),
        paymentId: req.body.trade_no,
        resourceUrl: resourceUrl, // 添加资源链接字段
        agentCommission: commissions.agentCommission, // 二级代理佣金
        parentAgentCommission: commissions.parentAgentCommission, // 一级代理佣金
        adminCommission: commissions.adminCommission
      }, { transaction });

      console.log('【支付宝异步通知】订单状态和佣金已更新:', {
        orderId: order.id,
        status: order.status,
        paidAt: order.paidAt,
        paymentId: order.paymentId,
        agentCommission: commissions.agentCommission,
        parentAgentCommission: commissions.parentAgentCommission,
        adminCommission: commissions.adminCommission
      });

      // 创建二级代理佣金记录（仅当有代理时）
      if (agent && commissions.agentCommission > 0) {
        const commission = await Commission.create({
          orderId: order.id,
          agentId: order.productLink.agentId,
          amount: commissions.agentCommission,
          status: 'pending', // 设置为待结算状态，一天后自动变为可提现
          type: 'agent'
        }, { transaction });

        console.log('【支付宝异步通知】二级代理佣金已创建:', {
          commissionId: commission.id,
          orderId: order.id,
          agentId: order.productLink.agentId,
          amount: commissions.agentCommission
        });
      } else {
        console.log('【支付宝异步通知】无代理或佣金为0，跳过佣金记录创建');
      }

      // 如果有一级代理，创建一级代理佣金记录
      if (agent && agent.parentAgent && commissions.parentAgentCommission > 0) {
        const parentCommissionRecord = await Commission.create({
          orderId: order.id,
          agentId: agent.parentAgent.id,
          amount: commissions.parentAgentCommission,
          status: 'pending', // 设置为待结算状态，一天后自动变为可提现
          type: 'parent'
        }, { transaction });

        console.log('【支付宝异步通知】一级代理佣金已创建:', {
          commissionId: parentCommissionRecord.id,
          orderId: order.id,
          agentId: agent.parentAgent.id,
          amount: commissions.parentAgentCommission
        });
      }

      // 更新推广链接的销量和总佣金（仅当有代理时）
      if (agent && order.productLink) {
        await order.productLink.increment('sales', { by: 1, transaction });
        await order.productLink.increment('totalCommission', { by: commissions.agentCommission, transaction });
        console.log('【支付宝异步通知】推广链接销量和佣金已更新');
      }

      // 检查是否是八字查询服务订单，如果是则自动生成报告
      if (order.orderType === 'service' && order.productLink?.product?.type === 'service') {
        console.log('【支付宝异步通知】检测到八字查询服务订单，准备生成报告');
        try {
          // 异步生成八字报告，不阻塞异步通知响应
          const generateReportAsync = async () => {
            try {
              const response = await fetch(`${process.env.API_BASE_URL || 'http://localhost:3005'}/api/bazi/orders/${order.id}/generate-report`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                }
              });
              
              if (response.ok) {
                console.log('【支付宝异步通知】八字报告生成成功');
              } else {
                console.error('【支付宝异步通知】八字报告生成失败:', await response.text());
              }
            } catch (error) {
              console.error('【支付宝异步通知】八字报告生成异常:', error);
            }
          };
          
          // 异步执行，不等待结果
          setImmediate(generateReportAsync);
        } catch (error) {
          console.error('【支付宝异步通知】启动八字报告生成失败:', error);
        }
      }

      // 提交事务
      await transaction.commit();
      console.log('【支付宝异步通知】事务提交成功');
      res.send('success');
    } catch (error) {
      // 回滚事务
      if (transaction) {
        await transaction.rollback();
        console.error('【支付宝异步通知】事务回滚:', error);
      }
      throw error;
    }
  } catch (error) {
    console.error('【支付宝异步通知】处理失败:', error);
    if (!res.headersSent) {
      res.status(500).send('fail');
    }
  }
});

export default router;