#!/bin/bash

# 部署修复脚本 - 解决API 404问题
echo "🚀 开始部署修复..."

# 1. 停止后端服务
echo "📦 停止后端服务..."
pm2 stop commission-platform-api 2>/dev/null || echo "后端服务未运行"

# 2. 杀掉占用端口3005的进程
echo "🔧 清理端口3005..."
lsof -ti:3005 | xargs kill -9 2>/dev/null || echo "端口3005未被占用"

# 3. 构建前端
echo "🏗️ 构建前端..."
npm run build

# 4. 复制前端文件到部署目录
echo "📁 复制前端文件..."
sudo rm -rf /data/commission-platform/*
sudo cp -r dist/* /data/commission-platform/
sudo chown -R www-data:www-data /data/commission-platform

# 5. 更新Nginx配置
echo "⚙️ 更新Nginx配置..."
sudo cp nginx.conf /etc/nginx/sites-available/commission-platform
sudo ln -sf /etc/nginx/sites-available/commission-platform /etc/nginx/sites-enabled/commission-platform

# 6. 测试Nginx配置
echo "🧪 测试Nginx配置..."
sudo nginx -t
if [ $? -ne 0 ]; then
    echo "❌ Nginx配置测试失败"
    exit 1
fi

# 7. 重新加载Nginx
echo "🔄 重新加载Nginx..."
sudo systemctl reload nginx

# 8. 启动后端服务
echo "🚀 启动后端服务..."
cd backend
pm2 start src/index.js --name commission-platform-api --env production

# 9. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 10. 测试API
echo "🧪 测试API..."
curl -f http://localhost:3005/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 本地API测试成功"
else
    echo "⚠️ 本地API测试失败，但服务可能仍在启动中"
fi

# 11. 测试远程API
echo "🌐 测试远程API..."
sleep 3
curl -f https://ye.bzcy.xyz/api/auth/login -X POST -H "Content-Type: application/json" -d '{"username":"test","password":"test"}' > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 远程API测试成功"
else
    echo "⚠️ 远程API测试失败，请检查配置"
fi

# 12. 显示服务状态
echo "📊 服务状态:"
pm2 status commission-platform-api
echo ""
echo "🎉 部署修复完成！"
echo "🌐 网站地址: https://ye.bzcy.xyz"
echo "📝 如果仍有问题，请检查:"
echo "   - PM2日志: pm2 logs commission-platform-api"
echo "   - Nginx日志: sudo tail -f /var/log/nginx/commission-platform.error.log"
