"use client";

import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Radio,
  Button,
  Card,
  Row,
  Col,
  message,
  Typography,
  Space,
  Divider,
  ConfigProvider
} from 'antd';
import { UserOutlined, CalendarOutlined, ClockCircleOutlined, EnvironmentOutlined, BookOutlined, FileTextOutlined, CustomerServiceOutlined } from '@ant-design/icons';
import { BaZiQueryFormValues, LocationData } from '../types/bazi';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import zhCN from 'antd/locale/zh_CN';
import axios from 'axios';
import { config } from '../config';
import { format } from 'date-fns';
import '../styles/BaziReportEnhanced.css';

// 设置dayjs为中文
dayjs.locale('zh-cn');

const { Title, Paragraph } = Typography;
const { Option } = Select;

// 省市数据
const locationData: LocationData = {
  "北京市": ["东城区", "西城区", "朝阳区", "海淀区", "丰台区", "石景山区", "门头沟区", "房山区", "通州区", "顺义区", "昌平区", "大兴区", "怀柔区", "平谷区", "密云区", "延庆区"],
  "上海市": ["黄浦区", "徐汇区", "长宁区", "静安区", "普陀区", "虹口区", "杨浦区", "闵行区", "宝山区", "嘉定区", "浦东新区", "金山区", "松江区", "青浦区", "奉贤区", "崇明区"],
  "天津市": ["和平区", "河东区", "河西区", "南开区", "河北区", "红桥区", "东丽区", "西青区", "津南区", "北辰区", "武清区", "宝坻区", "滨海新区", "宁河区", "静海区", "蓟州区"],
  "重庆市": ["万州区", "涪陵区", "渝中区", "大渡口区", "江北区", "沙坪坝区", "九龙坡区", "南岸区", "北碚区", "綦江区", "大足区", "渝北区", "巴南区", "黔江区", "长寿区", "江津区", "合川区", "永川区", "南川区", "璧山区", "铜梁区", "潼南区", "荣昌区", "开州区", "梁平区", "武隆区"],
  "河北省": ["石家庄市", "唐山市", "秦皇岛市", "邯郸市", "邢台市", "保定市", "张家口市", "承德市", "沧州市", "廊坊市", "衡水市"],
  "山西省": ["太原市", "大同市", "阳泉市", "长治市", "晋城市", "朔州市", "晋中市", "运城市", "忻州市", "临汾市", "吕梁市"],
  "内蒙古自治区": ["呼和浩特市", "包头市", "乌海市", "赤峰市", "通辽市", "鄂尔多斯市", "呼伦贝尔市", "巴彦淖尔市", "乌兰察布市", "兴安盟", "阿拉善盟", "锡林郭勒盟"],
  "辽宁省": ["沈阳市", "大连市", "鞍山市", "抚顺市", "本溪市", "丹东市", "锦州市", "营口市", "阜新市", "辽阳市", "盘锦市", "铁岭市", "朝阳市", "葫芦岛市"],
  "吉林省": ["长春市", "吉林市", "四平市", "辽源市", "通化市", "白山市", "松原市", "白城市", "延边朝鲜族自治州"],
  "黑龙江省": ["哈尔滨市", "齐齐哈尔市", "鸡西市", "鹤岗市", "双鸭山市", "大庆市", "伊春市", "佳木斯市", "七台河市", "牡丹江市", "黑河市", "绥化市", "大兴安岭地区"],
  "江苏省": ["南京市", "无锡市", "徐州市", "常州市", "苏州市", "南通市", "连云港市", "淮安市", "盐城市", "扬州市", "镇江市", "泰州市", "宿迁市"],
  "浙江省": ["杭州市", "宁波市", "温州市", "嘉兴市", "湖州市", "绍兴市", "金华市", "衢州市", "舟山市", "台州市", "丽水市"],
  "安徽省": ["合肥市", "芜湖市", "蚌埠市", "淮南市", "马鞍山市", "淮北市", "铜陵市", "安庆市", "黄山市", "滁州市", "阜阳市", "宿州市", "六安市", "亳州市", "池州市", "宣城市"],
  "福建省": ["福州市", "厦门市", "莆田市", "三明市", "泉州市", "漳州市", "南平市", "龙岩市", "宁德市"],
  "江西省": ["南昌市", "景德镇市", "萍乡市", "九江市", "新余市", "鹰潭市", "赣州市", "吉安市", "宜春市", "抚州市", "上饶市"],
  "山东省": ["济南市", "青岛市", "淄博市", "枣庄市", "东营市", "烟台市", "潍坊市", "济宁市", "泰安市", "威海市", "日照市", "临沂市", "德州市", "聊城市", "滨州市", "菏泽市"],
  "河南省": ["郑州市", "开封市", "洛阳市", "平顶山市", "安阳市", "鹤壁市", "新乡市", "焦作市", "濮阳市", "许昌市", "漯河市", "三门峡市", "南阳市", "商丘市", "信阳市", "周口市", "驻马店市", "济源市"],
  "湖北省": ["武汉市", "黄石市", "十堰市", "宜昌市", "襄阳市", "鄂州市", "荆门市", "孝感市", "荆州市", "黄冈市", "咸宁市", "随州市", "恩施土家族苗族自治州", "仙桃市", "潜江市", "天门市", "神农架林区"],
  "湖南省": ["长沙市", "株洲市", "湘潭市", "衡阳市", "邵阳市", "岳阳市", "常德市", "张家界市", "益阳市", "郴州市", "永州市", "怀化市", "娄底市", "湘西土家族苗族自治州"],
  "广东省": ["广州市", "深圳市", "珠海市", "汕头市", "佛山市", "韶关市", "湛江市", "肇庆市", "江门市", "茂名市", "惠州市", "梅州市", "汕尾市", "河源市", "阳江市", "清远市", "东莞市", "中山市", "潮州市", "揭阳市", "云浮市"],
  "广西壮族自治区": ["南宁市", "柳州市", "桂林市", "梧州市", "北海市", "防城港市", "钦州市", "贵港市", "玉林市", "百色市", "贺州市", "河池市", "来宾市", "崇左市"],
  "海南省": ["海口市", "三亚市", "三沙市", "儋州市", "五指山市", "琼海市", "文昌市", "万宁市", "东方市", "定安县", "屯昌县", "澄迈县", "临高县", "白沙黎族自治县", "昌江黎族自治县", "乐东黎族自治县", "陵水黎族自治县", "保亭黎族苗族自治县", "琼中黎族苗族自治县"],
  "四川省": ["成都市", "自贡市", "攀枝花市", "泸州市", "德阳市", "绵阳市", "广元市", "遂宁市", "内江市", "乐山市", "南充市", "眉山市", "宜宾市", "广安市", "达州市", "雅安市", "巴中市", "资阳市", "阿坝藏族羌族自治州", "甘孜藏族自治州", "凉山彝族自治州"],
  "贵州省": ["贵阳市", "六盘水市", "遵义市", "安顺市", "毕节市", "铜仁市", "黔西南布依族苗族自治州", "黔东南苗族侗族自治州", "黔南布依族苗族自治州"],
  "云南省": ["昆明市", "曲靖市", "玉溪市", "保山市", "昭通市", "丽江市", "普洱市", "临沧市", "楚雄彝族自治州", "红河哈尼族彝族自治州", "文山壮族苗族自治州", "西双版纳傣族自治州", "大理白族自治州", "德宏傣族景颇族自治州", "怒江傈僳族自治州", "迪庆藏族自治州"],
  "西藏自治区": ["拉萨市", "日喀则市", "昌都市", "林芝市", "山南市", "那曲市", "阿里地区"],
  "陕西省": ["西安市", "铜川市", "宝鸡市", "咸阳市", "渭南市", "延安市", "汉中市", "榆林市", "安康市", "商洛市"],
  "甘肃省": ["兰州市", "嘉峪关市", "金昌市", "白银市", "天水市", "武威市", "张掖市", "平凉市", "酒泉市", "庆阳市", "定西市", "陇南市", "临夏回族自治州", "甘南藏族自治州"],
  "青海省": ["西宁市", "海东市", "海北藏族自治州", "黄南藏族自治州", "海南藏族自治州", "果洛藏族自治州", "玉树藏族自治州", "海西蒙古族藏族自治州"],
  "宁夏回族自治区": ["银川市", "石嘴山市", "吴忠市", "固原市", "中卫市"],
  "新疆维吾尔自治区": ["乌鲁木齐市", "克拉玛依市", "吐鲁番市", "哈密市", "昌吉回族自治州", "博尔塔拉蒙古自治州", "巴音郭楞蒙古自治州", "阿克苏地区", "克孜勒苏柯尔克孜自治州", "喀什地区", "和田地区", "伊犁哈萨克自治州", "塔城地区", "阿勒泰地区", "石河子市", "阿拉尔市", "图木舒克市", "五家渠市", "北屯市", "铁门关市", "双河市", "可克达拉市", "昆玉市", "胡杨河市", "新星市"],
  "香港特别行政区": ["中西区", "湾仔区", "东区", "南区", "油尖旺区", "深水埗区", "九龙城区", "黄大仙区", "观塘区", "葵青区", "荃湾区", "屯门区", "元朗区", "北区", "大埔区", "沙田区", "西贡区", "离岛区"],
  "澳门特别行政区": ["花地玛堂区", "圣安多尼堂区", "大堂区", "望德堂区", "风顺堂区", "嘉模堂区", "圣方济各堂区", "路氹填海区"],
  "台湾省": ["台北市", "新北市", "桃园市", "台中市", "台南市", "高雄市", "基隆市", "新竹市", "嘉义市", "新竹县", "苗栗县", "彰化县", "南投县", "云林县", "嘉义县", "屏东县", "宜兰县", "花莲县", "台东县", "澎湖县", "金门县", "连江县"]
};

const provinces = Object.keys(locationData);

// 生成时辰选项
const hours = Array.from({ length: 24 }, (_, i) => String(i).padStart(2, '0'));
const minutes = Array.from({ length: 60 }, (_, i) => String(i).padStart(2, '0'));

interface BaziFormProps {
  onSubmit?: (values: BaZiQueryFormValues) => void;
  loading?: boolean;
  productInfo?: {
    name: string;
    price: number;
    description: string;
  };
  productCode?: string;
}

const BaziForm: React.FC<BaziFormProps> = ({ 
  onSubmit, 
  loading = false,
  productInfo,
  productCode
}) => {
  const [form] = Form.useForm();
  const [selectedProvince, setSelectedProvince] = useState<string>('');
  const [cities, setCities] = useState<string[]>([]);
  const [currentYear, setCurrentYear] = useState<number | null>(null);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
  }, []);

  // 监听省份变化
  useEffect(() => {
    if (selectedProvince) {
      setCities(locationData[selectedProvince] || []);
      form.setFieldsValue({ birthCity: undefined }); // 清空城市选择
    }
  }, [selectedProvince, form]);

  const handleProvinceChange = (value: string) => {
    setSelectedProvince(value);
  };

  const handleSubmit = async (values: any) => {
    if (!productCode) {
      message.error('缺少产品代码');
      return;
    }

    if (onSubmit) {
      // 如果有onSubmit回调，则使用传统方式（用于兼容性）
      const formattedValues: BaZiQueryFormValues = {
        name: values.name,
        gender: values.gender,
        birthDate: values.birthDate.toDate(),
        birthHour: values.birthHour,
        birthMinute: values.birthMinute,
        birthProvince: values.birthProvince,
        birthCity: values.birthCity,
        calendarType: values.calendarType,
        phone: values.phone
      };
      onSubmit(formattedValues);
      return;
    }

    setSubmitting(true);
    
    try {
      // 构建八字查询订单数据，使用通用购买API格式
      const birthDate = values.birthDate.toDate();
      const requestData = {
        // 八字查询相关数据
        name: values.name,
        gender: values.gender,
        calendarType: values.calendarType,
        birthYear: format(birthDate, 'yyyy'),
        birthMonth: format(birthDate, 'MM'),
        birthDay: format(birthDate, 'dd'),
        birthHour: values.birthHour,
        birthMinute: values.birthMinute,
        birthProvince: values.birthProvince,
        birthCity: values.birthCity,
        // 通用订单数据
        customerPhone: values.phone || '',
        deviceId: 'web-' + Date.now(),
        clientIp: '127.0.0.1' // 前端无法获取真实IP，后端会自动处理
      };

      console.log('创建八字订单，请求数据:', requestData);

      // 调用通用购买API创建订单并获取支付链接
      const response = await axios.post(`${config.apiUrl}/api/orders/purchase/${productCode}`, requestData);

      console.log('订单创建成功:', response.data);

      if (response.data.paymentUrl) {
        message.success('订单创建成功，正在跳转到支付页面...');
        
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          window.location.href = response.data.paymentUrl;
        }, 1000);
      } else {
        throw new Error('未获取到支付链接');
      }
      
    } catch (error) {
      console.error('创建订单失败:', error);
      
      if (axios.isAxiosError(error)) {
        const errorMessage = error.response?.data?.message || '创建订单失败';
        message.error(errorMessage);
      } else {
        message.error('创建订单失败，请重试');
      }
    } finally {
      setSubmitting(false);
    }
  };

  // 如果有传统的onSubmit回调，优先使用它（用于兼容性）
  const finalHandleSubmit = onSubmit ? (values: any) => {
    try {
      const formData: BaZiQueryFormValues = {
        name: values.name,
        gender: values.gender,
        calendarType: values.calendarType,
        birthDate: values.birthDate.toDate(),
        birthHour: values.birthHour,
        birthMinute: values.birthMinute,
        birthProvince: values.birthProvince,
        birthCity: values.birthCity,
      };
      onSubmit(formData);
    } catch (error) {
      message.error('提交信息时出错，请检查填写内容');
    }
  } : handleSubmit;

  return (
    <ConfigProvider locale={zhCN}>
      <div className="min-h-screen bg-gradient-to-br from-amber-50/30 via-white to-orange-50/30">
        {/* 顶部装饰条 */}
        <div className="h-1 bg-gradient-to-r from-amber-400 via-orange-400 to-yellow-400"></div>

        <div className="flex flex-col items-center justify-center min-h-screen mobile-container p-4 py-8">
          <Card
            className="enhanced-card w-full max-w-2xl shadow-2xl"
            style={{
              maxWidth: '48rem',
              background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(245, 158, 11, 0.1)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
            }}
          >
          <div className="text-center pb-8">
            <div className="mb-4">
              <div className="inline-flex p-3 bg-gradient-to-r from-amber-400 to-orange-400 rounded-xl shadow-lg mb-4">
                <BookOutlined style={{ fontSize: '2rem', color: 'white' }} />
              </div>
            </div>
            <Title
              level={1}
              className="enhanced-title text-3xl font-bold"
              style={{
                fontSize: '2rem',
                fontWeight: 'bold',
                background: 'linear-gradient(135deg, #f59e0b, #ea580c)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                marginBottom: '1rem'
              }}
            >
              八字命盘查询
            </Title>
            <Paragraph
              className="text-lg font-medium"
              style={{
                fontSize: '1.125rem',
                color: '#92400e',
                marginTop: '0.5rem',
                marginBottom: 0,
                fontWeight: '500'
              }}
            >
              请输入您的生辰信息以探索命运玄机
            </Paragraph>
            
            {productInfo && (
              <div
                className="enhanced-card mt-6 p-6 bg-gradient-to-br from-amber-50 to-orange-50 border border-amber-200 rounded-xl shadow-lg"
                style={{
                  marginTop: '1.5rem',
                  padding: '1.5rem',
                  background: 'linear-gradient(135deg, #fef3c7 0%, #fed7aa 100%)',
                  border: '1px solid #fbbf24',
                  borderRadius: '0.75rem',
                  boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)'
                }}
              >
                <Title
                  level={4}
                  style={{
                    fontSize: '1.25rem',
                    fontWeight: '700',
                    background: 'linear-gradient(135deg, #f59e0b, #ea580c)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text',
                    marginBottom: '0.75rem'
                  }}
                >
                  {productInfo.name}
                </Title>
                <div style={{ color: '#ea580c', marginBottom: '0.75rem', fontSize: '1.125rem', fontWeight: '600' }}>
                  价格: ¥{productInfo.price}
                </div>
                <Paragraph style={{ fontSize: '1rem', color: '#92400e', margin: 0, fontWeight: '500' }}>
                  {productInfo.description}
                </Paragraph>
              </div>
            )}
          </div>

          <Form
            form={form}
            layout="vertical"
            onFinish={finalHandleSubmit}
            size="large"
            className="space-y-6"
          >
            <Form.Item
              name="name"
              label={
                <span className="flex items-center">
                  <UserOutlined style={{ marginRight: '0.5rem', color: '#C38022' }} />
                  姓名
                </span>
              }
              rules={[
                { required: true, message: '请输入您的姓名' },
                { min: 2, message: '姓名至少需要两个字符。' },
                { max: 50, message: '姓名过长。' }
              ]}
            >
              <Input placeholder="请输入您的姓名" />
            </Form.Item>

            <Form.Item
              name="gender"
              label={
                <span className="flex items-center">
                  <UserOutlined style={{ marginRight: '0.5rem', color: '#C38022' }} />
                  性别
                </span>
              }
              rules={[{ required: true, message: '请选择性别。' }]}
            >
              <Radio.Group>
                <Radio value="male">男</Radio>
                <Radio value="female">女</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="calendarType"
              label={
                <span className="flex items-center">
                  <CalendarOutlined style={{ marginRight: '0.5rem', color: '#C38022' }} />
                  历法
                </span>
              }
              rules={[{ required: true, message: '请选择历法类型。' }]}
              initialValue="gregorian"
            >
              <Radio.Group>
                <Radio value="gregorian">公历 (阳历)</Radio>
                <Radio value="lunar">农历 (阴历)</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name="birthDate"
              label={
                <span className="flex items-center">
                  <CalendarOutlined style={{ marginRight: '0.5rem', color: '#C38022' }} />
                  出生日期
                </span>
              }
              rules={[{ required: true, message: '请选择出生日期。' }]}
            >
              <DatePicker 
                placeholder="选择日期"
                style={{ width: '100%' }}
                disabledDate={(current) => current && (current > dayjs().endOf('day') || current < dayjs('1900-01-01'))}
              />
            </Form.Item>

            <div>
              <label className="flex items-center mb-1">
                <ClockCircleOutlined style={{ marginRight: '0.5rem', color: '#C38022' }} />
                出生时间
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Form.Item
                  name="birthHour"
                  rules={[{ required: true, message: '请选择出生时辰。' }]}
                >
                  <Select placeholder="选择时辰">
                    {hours.map((hour) => (
                      <Option key={hour} value={hour}>
                        {hour} 时
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  name="birthMinute"
                  rules={[{ required: true, message: '请选择出生分钟。' }]}
                >
                  <Select placeholder="选择分钟">
                    {minutes.map((minute) => (
                      <Option key={minute} value={minute}>
                        {minute} 分
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </div>

            <div>
              <label className="flex items-center mb-1">
                <EnvironmentOutlined style={{ marginRight: '0.5rem', color: '#C38022' }} />
                出生地
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <Form.Item
                  name="birthProvince"
                  rules={[{ required: true, message: '请选择出生省份。' }]}
                >
                  <Select 
                    placeholder="选择省份"
                    onChange={handleProvinceChange}
                    showSearch
                    filterOption={(input, option) =>
                      option?.value?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                    }
                  >
                    {provinces.map(province => (
                      <Option key={province} value={province}>{province}</Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  name="birthCity"
                  rules={[{ required: true, message: '请选择出生城市。' }]}
                >
                  <Select 
                    placeholder="选择城市"
                    disabled={!selectedProvince}
                    showSearch
                    filterOption={(input, option) =>
                      option?.value?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                    }
                  >
                    {cities.map(city => (
                      <Option key={city} value={city}>{city}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
              <div style={{ fontSize: '0.875rem', color: '#73590D', marginTop: '0.5rem' }}>
                请选择详细的出生地点。如列表中没有您的城市，请选择最接近的城市。数据仅供参考。
              </div>
            </div>

            <div style={{ fontSize: '0.875rem', color: '#73590D', marginBottom: '1rem' }}>
              我们将取姓名的第一个字作为姓氏，其余作为名字。
            </div>

            <Button
              type="primary"
              htmlType="submit"
              size="large"
              loading={loading || submitting}
              className="enhanced-button glow-effect w-full shadow-lg hover-lift"
              style={{
                background: 'linear-gradient(135deg, #f59e0b 0%, #ea580c 100%)',
                borderColor: 'transparent',
                width: '100%',
                height: '3rem',
                fontSize: '1rem',
                fontWeight: '600',
                borderRadius: '12px',
                transition: 'all 0.3s ease'
              }}
            >
              {(loading || submitting) ? '正在处理中...' : (productCode ? '立即支付并查询' : '查询命理报告')}
            </Button>
          </Form>

          {/* 辅助按钮区域 */}
          <div className="mt-4 space-y-3">
            <div className="mobile-grid-2 gap-3">
              {/* 示例报告按钮 */}
              <Button
                size="large"
                className="secondary-button w-full shadow-sm hover-lift"
                style={{
                  background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
                  borderColor: '#d1d5db',
                  color: '#374151',
                  height: '2.75rem',
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  borderRadius: '10px',
                  transition: 'all 0.3s ease'
                }}
                onClick={() => {
                  // 打开示例报告
                  window.open('/bazi-report-demo', '_blank');
                }}
              >
                <FileTextOutlined style={{ marginRight: '0.5rem' }} />
                查看示例报告
              </Button>

              {/* 客服按钮 */}
              <Button
                size="large"
                className="outline-button w-full shadow-sm hover-lift"
                style={{
                  background: 'transparent',
                  border: '2px solid #f59e0b',
                  color: '#f59e0b',
                  height: '2.75rem',
                  fontSize: '0.9rem',
                  fontWeight: '500',
                  borderRadius: '10px',
                  transition: 'all 0.3s ease'
                }}
                onClick={() => {
                  // 打开客服链接
                  window.open('https://work.weixin.qq.com/kfid/kfcd830f8b9fa7237cd', '_blank');
                }}
              >
                <CustomerServiceOutlined style={{ marginRight: '0.5rem' }} />
                联系客服
              </Button>
            </div>

            {/* 移动端提示文字 */}
            <div className="text-center">
              <p className="mobile-text-sm" style={{ color: '#6b7280', margin: '0.5rem 0' }}>
                查看示例了解报告内容，或联系客服获取帮助
              </p>
            </div>
          </div>

          <div 
            className="flex-col items-center text-center text-muted-foreground text-sm pt-6"
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
              color: '#73590D',
              fontSize: '0.875rem',
              paddingTop: '1.5rem'
            }}
          >
            <p>探索个人命盘，洞悉人生运程。</p>
            {currentYear && <p style={{ marginTop: '0.5rem' }}>&copy; {currentYear} 八字命盘查询. All rights reserved.</p>}
            <p style={{ marginTop: '0.25rem' }}>天地玄黄，宇宙洪荒</p>
          </div>
        </Card>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default BaziForm; 