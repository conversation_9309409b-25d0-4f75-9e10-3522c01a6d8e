import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { clsx } from '@/utils';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  position?: 'center' | 'bottom';
  className?: string;
}

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  position = 'center',
  className,
}) => {
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            style={{ pointerEvents: 'auto' }}
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{
              opacity: 0,
              y: position === 'bottom' ? '100%' : '-50%',
              x: position === 'center' ? '-50%' : '0%',
            }}
            animate={{
              opacity: 1,
              y: position === 'bottom' ? '0%' : '-50%',
              x: position === 'center' ? '-50%' : '0%',
            }}
            exit={{
              opacity: 0,
              y: position === 'bottom' ? '100%' : '-50%',
              x: position === 'center' ? '-50%' : '0%',
            }}
            className={clsx(
              'fixed z-50 bg-white rounded-t-lg',
              position === 'center' && 'top-1/2 left-1/2 rounded-lg max-w-sm w-full mx-4',
              position === 'bottom' && 'bottom-0 left-0 right-0',
              className
            )}
            style={{ pointerEvents: 'auto', zIndex: 51 }}
          >
            {children}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

interface ActionSheetProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  actions: Array<{
    name: string;
    color?: string;
    subname?: string;
    onClick?: () => void;
  }>;
  onSelect?: (action: any, index: number) => void;
}

export const ActionSheet: React.FC<ActionSheetProps> = ({
  isOpen,
  onClose,
  title,
  description,
  actions,
  onSelect,
}) => {
  const handleActionClick = (action: any, index: number) => {
    if (action.onClick) {
      action.onClick();
    }
    if (onSelect) {
      onSelect(action, index);
    }
    // 不在这里调用onClose()，让父组件控制关闭时机
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} position="bottom">
      <div className="van-action-sheet">
        {(title || description) && (
          <div className="van-action-sheet__header">
            {title && <h3 style={{ fontSize: '16px', fontWeight: '500', margin: '0 0 4px 0', color: '#323233' }}>{title}</h3>}
            {description && <div className="van-action-sheet__description">{description}</div>}
          </div>
        )}

        <div className="van-action-sheet__content">
          {actions.map((action, index) => (
            <button
              key={index}
              className="van-action-sheet__item"
              onClick={() => handleActionClick(action, index)}
            >
              <div style={{ fontWeight: '500' }}>{action.name}</div>
              {action.subname && (
                <div style={{ fontSize: '12px', color: '#969799', marginTop: '4px' }}>{action.subname}</div>
              )}
            </button>
          ))}
        </div>

        <button
          className="van-action-sheet__cancel"
          onClick={onClose}
        >
          取消
        </button>
      </div>
    </Modal>
  );
};
