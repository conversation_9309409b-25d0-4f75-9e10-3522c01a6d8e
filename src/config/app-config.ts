// 应用配置 - 产品级配置管理

export interface AppConfig {
  // 环境配置
  env: 'development' | 'staging' | 'production';
  
  // API配置
  api: {
    baseUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
  };
  
  // 支付配置
  payment: {
    providers: string[];
    currency: string;
    sandbox: boolean;
    alipay: {
      appId: string;
      publicKey: string;
      signType: string;
    };
    wechat: {
      appId: string;
      mchId: string;
    };
  };
  
  // 功能开关
  features: {
    analytics: boolean;
    errorReporting: boolean;
    autoSave: boolean;
    offlineMode: boolean;
    performanceMonitoring: boolean;
    userFeedback: boolean;
    abTesting: boolean;
  };
  
  // UI配置
  ui: {
    theme: 'light' | 'dark' | 'auto';
    animations: boolean;
    reducedMotion: boolean;
    language: string;
    dateFormat: string;
    timeFormat: string;
  };
  
  // 业务配置
  business: {
    products: {
      bazi: {
        price: number;
        originalPrice?: number;
        features: string[];
        duration: string;
        accuracy: string;
      };
      naming: {
        priceRange: [number, number];
        commissionRate: number;
        features: string[];
      };
    };
    limits: {
      maxFormSubmissions: number;
      maxHistoryRecords: number;
      sessionTimeout: number;
    };
  };
  
  // 第三方服务
  services: {
    analytics: {
      googleAnalytics?: string;
      baiduAnalytics?: string;
      mixpanel?: string;
    };
    monitoring: {
      sentry?: string;
      logRocket?: string;
    };
    cdn: {
      images: string;
      assets: string;
    };
  };
  
  // 安全配置
  security: {
    encryption: {
      algorithm: string;
      keySize: number;
    };
    rateLimit: {
      windowMs: number;
      maxRequests: number;
    };
    cors: {
      origins: string[];
      credentials: boolean;
    };
  };
}

// 环境变量获取函数
const getEnvVar = (key: string, defaultValue?: string): string => {
  const value = import.meta.env[key];
  if (!value && !defaultValue) {
    console.warn(`Environment variable ${key} is not set`);
  }
  return value || defaultValue || '';
};

const getBooleanEnvVar = (key: string, defaultValue: boolean = false): boolean => {
  const value = getEnvVar(key);
  return value ? value.toLowerCase() === 'true' : defaultValue;
};

const getNumberEnvVar = (key: string, defaultValue: number): number => {
  const value = getEnvVar(key);
  return value ? parseInt(value, 10) : defaultValue;
};

// 开发环境配置
const developmentConfig: AppConfig = {
  env: 'development',
  api: {
    baseUrl: getEnvVar('VITE_API_BASE_URL', 'http://localhost:3005'),
    timeout: 10000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  payment: {
    providers: ['alipay', 'wechat'],
    currency: 'CNY',
    sandbox: true,
    alipay: {
      appId: getEnvVar('VITE_ALIPAY_APP_ID', ''),
      publicKey: getEnvVar('VITE_ALIPAY_PUBLIC_KEY', ''),
      signType: 'RSA2',
    },
    wechat: {
      appId: getEnvVar('VITE_WECHAT_APP_ID', ''),
      mchId: getEnvVar('VITE_WECHAT_MCH_ID', ''),
    },
  },
  features: {
    analytics: false,
    errorReporting: true,
    autoSave: true,
    offlineMode: true,
    performanceMonitoring: true,
    userFeedback: true,
    abTesting: false,
  },
  ui: {
    theme: 'light',
    animations: true,
    reducedMotion: false,
    language: 'zh-CN',
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm',
  },
  business: {
    products: {
      bazi: {
        price: 99,
        originalPrice: 199,
        features: [
          '专业命理师一对一解读',
          '详细的性格分析报告',
          '事业财运发展建议',
          '感情婚姻指导',
          '健康养生建议',
          '未来运势预测'
        ],
        duration: '24小时内出报告',
        accuracy: '准确率95%以上',
      },
      naming: {
        priceRange: [99, 299],
        commissionRate: 0.2,
        features: [
          '专业起名大师服务',
          '五行八字分析',
          '音律美学考量',
          '文化内涵解读',
          '多个备选方案'
        ],
      },
    },
    limits: {
      maxFormSubmissions: 10,
      maxHistoryRecords: 20,
      sessionTimeout: 30 * 60 * 1000, // 30分钟
    },
  },
  services: {
    analytics: {
      googleAnalytics: getEnvVar('VITE_GA_ID'),
      baiduAnalytics: getEnvVar('VITE_BAIDU_ANALYTICS_ID'),
    },
    monitoring: {
      sentry: getEnvVar('VITE_SENTRY_DSN'),
    },
    cdn: {
      images: getEnvVar('VITE_CDN_IMAGES', '/images'),
      assets: getEnvVar('VITE_CDN_ASSETS', '/assets'),
    },
  },
  security: {
    encryption: {
      algorithm: 'AES-256-GCM',
      keySize: 256,
    },
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      maxRequests: 100,
    },
    cors: {
      origins: ['http://localhost:3000', 'http://localhost:3001'],
      credentials: true,
    },
  },
};

// 生产环境配置
const productionConfig: AppConfig = {
  ...developmentConfig,
  env: 'production',
  api: {
    baseUrl: getEnvVar('VITE_API_BASE_URL', 'https://api.bazi.com'),
    timeout: 15000,
    retryAttempts: 5,
    retryDelay: 2000,
  },
  payment: {
    ...developmentConfig.payment,
    sandbox: false,
  },
  features: {
    ...developmentConfig.features,
    analytics: true,
    errorReporting: true,
    abTesting: true,
  },
  services: {
    ...developmentConfig.services,
    cdn: {
      images: getEnvVar('VITE_CDN_IMAGES', 'https://cdn.bazi.com/images'),
      assets: getEnvVar('VITE_CDN_ASSETS', 'https://cdn.bazi.com/assets'),
    },
  },
  security: {
    ...developmentConfig.security,
    cors: {
      origins: ['https://bazi.com', 'https://www.bazi.com'],
      credentials: true,
    },
  },
};

// 测试环境配置
const stagingConfig: AppConfig = {
  ...developmentConfig,
  env: 'staging',
  api: {
    baseUrl: getEnvVar('VITE_API_BASE_URL', 'https://staging-api.bazi.com'),
    timeout: 12000,
    retryAttempts: 4,
    retryDelay: 1500,
  },
  features: {
    ...developmentConfig.features,
    analytics: true,
    abTesting: true,
  },
};

// 根据环境选择配置
const getConfig = (): AppConfig => {
  const env = getEnvVar('VITE_NODE_ENV', 'development');

  switch (env) {
    case 'production':
      return productionConfig;
    case 'staging':
      return stagingConfig;
    default:
      return developmentConfig;
  }
};

export const appConfig = getConfig();

// 配置验证
export const validateConfig = (config: AppConfig): boolean => {
  const requiredFields = [
    'api.baseUrl',
    'payment.providers',
    'business.products.bazi.price'
  ];
  
  for (const field of requiredFields) {
    const keys = field.split('.');
    let value: any = config;
    
    for (const key of keys) {
      value = value?.[key];
    }
    
    if (value === undefined || value === null || value === '') {
      console.error(`Required config field missing: ${field}`);
      return false;
    }
  }
  
  return true;
};

// 导出常用配置
export const {
  api: apiConfig,
  payment: paymentConfig,
  features: featureFlags,
  ui: uiConfig,
  business: businessConfig,
  services: servicesConfig,
  security: securityConfig
} = appConfig;

// 验证配置
if (!validateConfig(appConfig)) {
  console.error('Invalid application configuration');
}
